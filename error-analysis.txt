# Build Error Analysis - 2025-07-25 15:32:18
# Cerebrum30 .NET 8 Upgrade Progress

## Total Errors: 2162

## Error Breakdown by Type (Descending Order):

### CS7036 - Missing Parameter Errors (Method Signatures) (772 errors) 🟠 HIGH
**Sample Error**: There is no argument given that corresponds to the required parameter 'htmlAttributes' of 'IHtmlHelper<VMPatientDemo>.DropDownListFor<TResult>(Expression<Func<VMPatientDemo, TResult>>, IEnumerable<SelectListItem>, string, object)'
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 101 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 101 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsGB: 59 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsGB: 59 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 37 files


### CS1973 - Dynamic Dispatch Errors (Extension Methods) (250 errors) 🔴 CRITICAL
**Sample Error**: 'IHtmlHelper<VMExternalDoctorReportContact>' has no applicable method named 'Hidden' but appears to have an extension method by that name. Extension methods cannot be dynamically dispatched. Consider casting the dynamic arguments or calling the extension method without the extension method syntax.
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 22 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 22 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 18 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 18 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 17 files


### CS1963 - Expression Tree Errors (Dynamic Operations) (244 errors) 🔴 CRITICAL
**Sample Error**: An expression tree may not contain a dynamic operation
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Measurement: 19 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Measurement: 19 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 17 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 17 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/Visit: 15 files


### CS1503 - Argument Type Mismatch Errors (176 errors) 🟢 LOW
**Sample Error**: Argument 2: cannot convert from 'object' to 'string'
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/AdminUser/Views/ImportCDS: 18 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/AdminUser/Views/ImportCDS: 18 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/eConsult/Views/Consult: 12 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/eConsult/Views/Consult: 12 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 8 files


### CS0103 - Name Does Not Exist Errors (Context/Namespace) (166 errors) 🟠 HIGH
**Sample Error**: The name 'Request' does not exist in the current context
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 21 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 21 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Shared: 10 files


### CS0411 - Other Compilation Errors (142 errors) 🟢 LOW
**Sample Error**: The type arguments for method 'IHtmlHelper<VMPatientDemo>.HiddenFor<TResult>(Expression<Func<VMPatientDemo, TResult>>, object)' cannot be inferred from the usage. Try specifying the type arguments explicitly.
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 37 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 37 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsGB/Shared: 23 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsGB/Shared: 23 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Staff: 6 files


### CS1977 - Lambda Expression Errors (Dynamic Context) (90 errors) 🔴 CRITICAL
**Sample Error**: Cannot use a lambda expression as an argument to a dynamically dispatched operation without first casting it to a delegate or expression tree type.
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 10 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 10 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 8 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 8 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Medications/Views/DHDR: 5 files


### CS8323 - Other Compilation Errors (72 errors) 🟢 LOW
**Sample Error**: Named argument 'htmlAttributes' is used out-of-position but is followed by an unnamed argument
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 14 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Documents/Views/Uploads: 13 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Documents/Views/Uploads: 13 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Daysheet/Views/Appointments: 9 files


### CS1061 - Missing Member Errors (Method/Property Not Found) (56 errors) 🟡 MEDIUM
**Sample Error**: 'ReportPhrase' does not contain a definition for 'test' and no accessible extension method 'test' accepting a first argument of type 'ReportPhrase' could be found (are you missing a using directive or an assembly reference?)
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/HL7Report: 15 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/HL7Report: 15 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/OLIS: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsDK: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 3 files


### CS0234 - Other Compilation Errors (54 errors) 🟢 LOW
**Sample Error**: The type or namespace name 'Shared' does not exist in the namespace 'Cerebrum' (are you missing an assembly reference?)
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 27 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 27 files


### CS1929 - Other Compilation Errors (34 errors) 🟢 LOW
**Sample Error**: 'IHtmlHelper<VMTest>' does not contain a definition for 'Action' and the best extension method overload 'UrlHelperExtensions.Action(IUrlHelper, string?, object?)' requires a receiver of type 'Microsoft.AspNetCore.Mvc.IUrlHelper'
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Daysheet/Views/Appointments: 2 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Medications/Views/DHDR: 2 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Medications/Views/DHDR: 2 files


### CS1026 - Other Compilation Errors (28 errors) 🟢 LOW
**Sample Error**: ) expected
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 5 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 5 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Account: 3 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files


### CS0019 - Other Compilation Errors (22 errors) 🟢 LOW
**Sample Error**: Operator '==' cannot be applied to operands of type 'Func<dynamic, bool>' and 'string'
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Daysheet: 3 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 3 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Daysheet: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 2 files


### CS0839 - Other Compilation Errors (10 errors) 🟢 LOW
**Sample Error**: Argument missing


### CS0029 - Other Compilation Errors (10 errors) 🟢 LOW
**Sample Error**: Cannot implicitly convert type 'System.Collections.Generic.List<dynamic>' to 'System.Collections.Generic.List<string>'


### CS1002 - Other Compilation Errors (8 errors) 🟢 LOW
**Sample Error**: ; expected


### CS1513 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: } expected


### CS1729 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: 'ViewDataDictionary' does not contain a constructor that takes 0 arguments


### CS0117 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: 'HttpContext' does not contain a definition for 'Current'


### CS1003 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: Syntax error, ',' expected


### CS1525 - Other Compilation Errors (2 errors) 🟢 LOW
**Sample Error**: Invalid expression term ')'


### CS0246 - Type Not Found Errors (Missing References) (2 errors) 🟡 MEDIUM
**Sample Error**: The type or namespace name 'HtmlString' could not be found (are you missing a using directive or an assembly reference?)

## Recommended Fix Priority Order:

### Phase 1: Dynamic Operation Errors (CS1963/CS1973/CS1977)
These represent ~80% of current errors and are blocking compilation.
- **CS1973** (Dynamic Dispatch): Replace extension method calls on dynamic types
- **CS1963** (Expression Trees): Convert dynamic operations to typed operations  
- **CS1977** (Lambda Expressions): Fix lambda expressions with dynamic dispatch

### Phase 2: Method Signature Errors (CS7036)
- Html.BeginForm parameter fixes
- Other method signature mismatches from .NET Framework to .NET Core migration

### Phase 3: Context/Namespace Errors (CS0103)
- Remaining context access issues
- Missing helper method references

### Phase 4: Reference and Type Errors (CS0246/CS1061/CS0104)
- Assembly reference issues
- Missing member resolution
- Namespace conflict resolution

## Next Steps:
1. Focus on CS1963/CS1973/CS1977 errors first (highest impact)
2. Create helper methods to replace dynamic operations with typed alternatives
3. Update expression tree usage to avoid dynamic operations
4. Fix method signature issues systematically

