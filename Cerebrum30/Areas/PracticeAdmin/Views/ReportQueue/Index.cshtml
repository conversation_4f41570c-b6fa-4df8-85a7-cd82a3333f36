@using Cerebrum.ViewModels.Requisition;
@{
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    ViewBag.Title = "Report Queue";
    var SelectedDate = DateTime.Now;
    var ReportQueueStatuses = (List<TextValueViewModel>)ViewBag.ReportQueueStatuses;
    var itemAll = new TextValueViewModel()
    {
        text = "ALL",
        value = "0",
    };
    ReportQueueStatuses.Insert(0, itemAll);
}
<link href="~/Areas/PracticeAdmin/Content/reportQueue.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.2/css/jquery.dataTables.min.css" />
<script src="~/Areas/PracticeAdmin/Scripts/reportQueue.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.2/js/jquery.dataTables.min.js"></script>

<div class="margin-left-15 margin-right-15 marginBottom">
    <h3><span>Report Queue</span><span class="pull-right"><button class="btn btn-primary" id="btn-resend-suspended-reports">Resend Suspended Reports</button></span></h3>
    <div class="row row-top-padding">
        <div class="form-inline marginBottom">
            <div class="col-md-4 col-lg-4">
                <div class="form-group form-group-sm">
                    <label class="control-label" for="Date">Date In Queue</label>
                    @Html.TextBox("Date", (object)String.Format("{0:MM/dd/yyyy}", SelectedDate), new { @class = "form-control date-picker input-sm" })
                </div>
            </div>
            <div class="col-md-1 col-lg-1 pull-right margin-left-15">
                <div class="form-group form-group-sm">
                    <button class="btn btn-default btn-sm btn-sch-report-queue" id="btn-search-report-queue">Search Queue</button>
                </div>
            </div>
            <div class="col-md-1 col-lg-1 pull-right margin-right-15">
                <div class="form-group">
                    @Html.DropDownList("reportQueueStatus", new SelectList(ReportQueueStatuses, "value", "text"), new { @class = "form-control", @style="height:30px;" })
                </div>
            </div>
        </div>
        <div id="report-queue-search-result"></div>
        <div id="divTableData" style="visibility:hidden;">
            <table id="tables" class="table table-hover table-bordered table-responsive">
                <thead>
                    <tr>
                        <th class="text-center">
                            &num;
                        </th>
                        <th class="text-nowrap">
                            Patient Name
                        </th>
                        <th class="text-nowrap">
                            Test Name
                        </th>
                        <th class="text-nowrap">
                            Test Date
                        </th>
                        <th class="text-nowrap">
                            Send By User
                        </th>
                        <th>
                            Office
                        </th>
                        <th class="text-nowrap">
                            Send Status
                        </th>
                        <th>
                            Attempts
                        </th>
                        <th class="text-nowrap">
                            Time In Queue
                        </th>
                        <th class="text-nowrap">
                            Date In Queue
                        </th>
                        <th class="text-nowrap">
                            Date Sent
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

