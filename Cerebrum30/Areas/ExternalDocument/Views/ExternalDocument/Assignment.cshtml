@model Cerebrum.ViewModels.ExternalDocument.ClinicServerAccessData

@using Cerebrum30.Helpers;
@{
    ViewBag.Title = "Assignment";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<!--
Very important that _AwareServicesUiLoader is placed in the html header (which this section does).
If not the css cascade layer order will get messed up when certian components inject inline styles
  -->
@section AwareServicesMicroUiLoaderSlot {
    @(await Html.PartialAsync("_AwareServicesUiLoader"))
}

<link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
<link href="~/Areas/Schedule/Content/appointments-modal.css" rel="stylesheet" />

<style type="text/css">
    #assignment-container {
        padding-top: 15px;
        padding-bottom: 4px;
        height: calc(100vh - 50px - 30px)
    }

    #assignment-container .transparent {
        opacity: 0;
    }
 
    #assignment-container .badge-warning{
        background-color: #eb5817;
        color:#ffffff;
    }

    #assignment-container .clearable {
        background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center;
        padding: 3px 18px 3px 4px; /* Use the same right padding (18) in jQ! */
        border-radius: 3px;
        transition: background 0.4s;
    }

    #assignment-container .clearable.x {
        background-position: right 5px center;
    }
    /* (jQ) Show icon */
    #assignment-container .clearable.onX {
        cursor: pointer;
    }
    /* (jQ) hover cursor style */
    #assignment-container .clearable::-ms-clear {
        display: none;
        width: 0;
        height: 0;
    }
    /* Remove IE default X */

    #wrapper {
        height: 100%;
        padding-left: 160px;
        transition: all 0.4s ease 0s;
    }

    #sidebar-wrapper {
        /*margin-left: -148px;
        left: 160px;
        width: 160px;
        background: steelblue;
        position: fixed;
        height: 100%;
        overflow-y: auto;
        z-index: 1000;
        transition: all 0.4s ease 0s;*/
    }

    #page-content-wrapper {
        width: 100%;
        height: 100%;
    }

    #assignment-container .sidebar-nav {
        position: absolute;
        top: 0;
        width: 160px;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    #assignment-container .sidebar-nav li {
        line-height: 20px;
        text-indent: 0px;
    }

    #assignment-container .sidebar-nav li a {
        color: yellow;
        display: block;
        text-decoration: none;
        padding-left: 16px;
    }

    #assignment-container .sidebar-nav li a:hover,
    #assignment-container .sidebar-nav li.active {
        color: #fff;
        background: rgba(255,255,255,0.2);
        text-decoration: none;
    }

    #assignment-container .sidebar-nav li a:active,
    #assignment-container .sidebar-nav li a:focus {
        text-decoration: none;
    }

    #assignment-container .high-lighted {
        background-color: #1abc9c;
    }

    .btn-popover-container {
        display: inline-block;
    }

    .btn-popover-content{
        padding-top:0;
        padding-bottom:0;
        margin-top:0;
        margin-bottom:0;
    }

    .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
        display: none;
    }

    .popover-pointer {
        cursor:pointer;
    }

    #reportDiv {
        height: 100%;
    }
</style>

<script type="text/javascript">
    var clinicServerUrl = "@Model.clinicServerUrl";
    var clinicServerToken = "@Html.Raw(Model.clinicServerToken.Replace("\"", "\\\""))"
    var clinicServerOfficeId = "@Model.officeId";
    var referrerUrl = "@Html.GetReferrerUrl()";
</script>
<script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/Assignment.js"></script>
<script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/ExternalDocument.js"></script>

<div id="assignment-container">
    <div id="wrapper">
        <div id="sidebar-wrapper">
            <ul id="assignmentList" name="assignmentList" class="sidebar-nav nav">
                <li><a href="#" onclick="return openAssignment('fax');">Fax <span id="count-fax" class="badge badge-warning"></span></a></li>
                <li><a href="#" onclick="return openAssignment('hl7');">HL7 <span id="count-hl7" class="badge"></span></a></li>
                <li><a href="#" onclick="return openAssignment('hrm');">HRM <span id="count-hrm" class="badge progress-bar-warning"></span></a></li>
                <li><a href="#" config-cat="integrations_ocean-e-referral" onclick="return openAssignment('eReferral');">Service Requests<span id="count-hrm" class="badge progress-bar-warning"></span></a></li>
            </ul>
        </div>
        <div id="page-content-wrapper">
            <div id="reportDiv" name="reportDiv">
            </div>
        </div>
        <div id="patientMenuTemplate" name="patientMenuTemplate" style="display:none;">
            <div class="btn-popover-container">
                <span class="popover-btn popover-pointer cb-text16 text-primary">PatientMenuTemplatePatientName</span>
                <div class="btn-popover-title">
                    <span class="default-text-color">e-Chart</span>
                </div>
                <div class="btn-popover-content">
                    @(await Html.PartialAsync("GetPatientMenu", (object)new { area = "", Id = -123456 }))
                </div>
            </div>
        </div>
        <div class="col-sm-12" id="imageLoading" name="imageLoading" style="position: fixed; left: 46%; top: 40%; display: none; z-index: 100000000;">
            <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
        </div>
    </div>
</div>