@model Cerebrum.ViewModels.ExternalDocument.ExternalDocumentAssignmentResponse
@using Cerebrum30.Helpers;

<style type="text/css">
    #fax-container {
        padding-top: 15px;
    }

    #fax-container input, #fax-container select, #fax-container textarea {
        max-width: 100%;
    }

    #fax-container .form-group {
        margin-bottom: 6px;
    }

    #fax-container .clearable {
        background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center;
        padding: 3px 18px 3px 4px; /* Use the same right padding (18) in jQ! */
        border-radius: 3px;
        transition: background 0.4s;
    }

    #fax-container .clearable.x {
        background-position: right 5px center;
    }
    /* (jQ) Show icon */
    #fax-container .clearable.onX {
        cursor: pointer;
    }
    /* (jQ) hover cursor style */
    #fax-container .clearable::-ms-clear {
        display: none;
        width: 0;
        height: 0;
    }
    /* Remove IE default X */

    #fax-container .ui-dialog-titlebar {
        background-color: steelblue;
        color: yellow;
    }

    .rotate-0 {
        transform-origin: center center;
    }

    .rotate-90 {
        transform: rotate(90deg);
        -o-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        transform-origin: center center;
    }

    .rotate-180 {
        transform: rotate(180deg);
        -o-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        transform-origin: center center;
    }

    .rotate-270 {
        transform: rotate(270deg);
        -o-transform: rotate(270deg);
        -ms-transform: rotate(270deg);
        -webkit-transform: rotate(270deg);
        -moz-transform: rotate(270deg);
        transform-origin: center center;
    }
</style>

<script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/Fax.js"></script>
<script type="text/javascript" src="~/Areas/Schedule/Scripts/sharedFunctions.js"></script>
<script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>
<script type="text/javascript" src="~/Scripts/pdfobject.js"></script>    @*http://miorepository.altervista.org/pdfobject.js*@

<script type="text/javascript">
    var externalDocuments = [];
    @if (Model != null && Model.externalDocuments != null) {
        foreach (var d in Model.externalDocuments) {
            @:externalDocuments.push(["@d.text", "@d.value"]);
        }
    }
    var subClasses = [];
    @if (Model != null && Model.subClasses != null) {
        foreach (var d in Model.subClasses) {
            @:subClasses.push(["@d.Item1", "@d.Item2", "@d.Item3"]);
        }
    }
    var categories = [];
    @if (Model != null && Model.categories != null) {
        foreach (var d in Model.categories) {
            @:categories.push(["@d.Item1", "@d.Item2", "@d.Item3"]);
        }
    }
    var criticalResources = @Html.Raw(Json.Serialize(Model.criticalResources));
    var officeId = @Model.officeId;
    var showExternalDocumentOnReady = false;
    @if (Model != null && string.IsNullOrEmpty(Model.errorMessage)) {
        @:showExternalDocumentOnReady = true;
    }
</script>

@* preload appointment create view  *@
<script type="text/javascript">
    $(function () {
        loadAppScheduleModal(@Model.practiceId,@Model.officeId);
    });
</script>

<div id="fax-container">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()
            <div class="body-content">
                <div class="col-sm-5 form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            <button type="button" id="buttonNewAppointment" name="buttonNewAppointment" data-modal-url="@Url.Action("create", "appointments", new { area = "schedule" })"
                                    data-resource-type="practicedoctorid" data-resource-id="0" data-practice-id="@Model.practiceId" data-office-id="@Model.officeId"
                                    data-patient-record-id="-1" data-appointment-day="@DateTime.Now.ToString("MM/dd/yyyy")" data-patient-name="" data-resource-name="Test Resource222" class="btn btn-xs btn-info btn-sch-new-appointment">
                                <span class="glyphicon glyphicon-calendar" title="New Appointment"></span>
                            </button>
                            &nbsp;&nbsp;&nbsp;
                            <button type="button" class="btn btn-xs btn-info btn-add-patient" data-el="modal-md">
                                <span class="glyphicon glyphicon-user" title="New Patient"></span>
                            </button>
                            &nbsp;&nbsp;&nbsp;
                            <span style="color: red; ">*</span> Patient:
                        </label>
                        <div class="col-sm-5">
                            @Html.Hidden("demographicPatientRecordDoctorId", "")
                            @Html.Hidden("patientName", "")
                            @Html.TextBox("patientNameInput", "", new { @class = "form-control clearable" })
                        </div>
                        <div class="col-sm-3" style="padding-left: 0px;" id="patientMenuDiv" name="patientMenuDiv">
                            <button type="button" class="btn btn-xs btn-info"><span class="glyphicon glyphicon-arrow-right"></span></button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            <span style="color: red; ">*</span> Description
                        </label>
                        <div class="col-sm-8">
                            @Html.TextBox("description", "", new { @class = "form-control clearable" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Test Date
                        </label>
                        <div class="col-sm-8">
                            @Html.TextBox("testDate", "", new { @class = "form-control" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            <span style="color: red; ">*</span> Doctor
                        </label>
                        <div class="col-sm-8">
                            @{
                                @Html.DropDownList("doctorId", new SelectList(Model.doctors, "value", "text"), "Please Select", htmlAttributes: new { @class = "form-control ", multiple = "true", @style = "height: auto;", @onchange = "doctorChange(this.value)" })
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.officeName, htmlAttributes: new { @class = "control-label col-sm-4" })
                        <div class="col-sm-8">
                            @Html.TextBox("office", (object)@Model.officeName, new { @class = "form-control", @readonly = "readonly" })
                        </div>
                    </div>


                    <div class="form-group">
                        @Html.LabelFor(model => model.classes, htmlAttributes: new { @class = "control-label col-sm-4" })
                        <div class="col-sm-8">
                            @Html.DropDownList("classId", new SelectList(Model.classes, "value", "text"), "Please Select", htmlAttributes: new { @class = "form-control " })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            <span style="color: red; ">*</span> Category
                        </label>
                        <div class="col-sm-8">
                            @Html.DropDownList("categoryId", new SelectList(Model.categories, "Item3", "Item2"), "Please Select", htmlAttributes: new { @class = "form-control " })
                        </div>
                    </div>


                    <div class="form-group" style="display: none">
                        @Html.LabelFor(model => model.subClasses, htmlAttributes: new { @class = "control-label col-sm-4" })
                        <div class="col-sm-8">
                            @Html.DropDownList("subClassId", new SelectList(Model.subClasses, "Item3", "Item2"), "Please Select", htmlAttributes: new { @class = "form-control " })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Abnormal
                        </label>
                        <div class="col-sm-8" style="padding-top: 8px;">
                            @Html.CheckBox("abnormal")
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Mark Seen
                        </label>
                        <div class="col-sm-8" style="padding-top: 8px;">
                            @Html.CheckBox("markSeen")
                        </div>
                    </div>
                    <div class="form-group text-center">
                        <div class="col-sm-10 text-right">
                            <input type="button" id="buttonAssign" name="buttonAssign" value="Assign" class="btn btn-info" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Loose report
                        </label>
                        <div class="col-sm-1" style="padding-top: 8px;">
                            <input checked="checked" id="looseReport" name="looseReport" type="radio" value="loosereport" onclick="looseReportClicked()" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Assign as RD
                        </label>
                        <div class="col-sm-1" style="padding-top: 8px;">
                            <input id="assignAsRD" name="assignAsRD" type="checkbox" />
                        </div>
                        <div class="col-sm-7">
                            <select id="appointmentIds" name="appointmentIds" class="form-control" style="width: 100%; height: 120px;" multiple></select>
                        </div>
                    </div>
                    <div class="col-sm-12" id="requisitionListDiv" name="requisitionListDiv" style="height: 208px; padding-left: 0px; overflow-y: auto; display: none;">
                        <table class="table" id="requisitionList" name="requisitionList" style="font-size: 14px; width: 100%">
                            <thead>
                                <tr style="font-size: 16px; font-weight: bold;">
                                    @*class="success vertical-center"*@
                                    <td>Requisition</td>
                                    <td>Date</td>
                                    <td></td>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="col-sm-7 form-horizontal">
                    <div class="form-group">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                Upload Folder:
                            </label>
                            <div class="col-sm-6">
                                @{
                                    @Html.DropDownList("uploadFolder", new SelectList(Model.folders, Model.uploadFolder), htmlAttributes: new { @class = "form-control " })
                                }
                            </div>
                            <label class="col-sm-1"></label>
                            <label class="col-sm-2 control-label" style="padding-left: 0px;">
                                <button type="button" id="buttonGoBack" class="btn btn-xs btn-info">
                                    <span class="glyphicon glyphicon-arrow-left" title="Go Back"></span>
                                </button>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                Move to:
                            </label>
                            <div class="col-sm-6">
                                @{
                                    @Html.DropDownList("moveTo", new SelectList(Model.folders), htmlAttributes: new { @class = "form-control " })
                                }
                            </div>
                            <label class="col-sm-1 control-label" style="padding-left: 0px;">
                                <button type="button" id="buttonMove" name="buttonMove" class="btn btn-xs btn-info">
                                    <span class="glyphicon glyphicon-transfer" title="Move to Folder"></span>
                                </button>
                            </label>
                            <label class="col-sm-2 control-label" style="padding-left: 0px;">
                                <button type="button" id="buttonDelete" name="buttonDelete" class="btn btn-xs btn-info">
                                    <span class="glyphicon glyphicon-trash" title="Delete Item"></span>
                                </button>
                            </label>
                        </div>
                    </div>
                    <div id="externalDocumentDiv" name="externalDocumentDiv">
                        <div class="form-group">
                            <div class="col-sm-3">
                                <label id="totalFile" name="totalFile" class="control-label"></label>
                            </div>
                            <div class="col-sm-3 text-right">
                                <label id="fileof" name="fileof" class="control-label"></label>
                            </div>
                            <div class="col-sm-6 text-right">
                                <label id="fileName" name="fileName" class="control-label" title="Click to open file in new window"></label>
                            </div>
                        </div>

                        <div class="form-group" style="padding-top: 16px;">
                            <div class="col-sm-9">
                                <div class="col-sm-3 text-left">
                                    <button id="buttonFirst" name="buttonFirst" type="button" class="btn btn-xs btn-info">
                                        <span class="glyphicon glyphicon-fast-backward" style="width: 32px;" title="First"></span>&nbsp;
                                    </button>
                                </div>
                                <div class="col-sm-3 text-center">
                                    <button id="buttonPrevious" name="buttonPrevious" type="button" class="btn btn-xs btn-info">
                                        <span class="glyphicon glyphicon-step-backward" style="width: 32px;" title="Previous"></span>&nbsp;
                                    </button>
                                </div>
                                <div class="col-sm-3 text-center">
                                    <button id="buttonNext" name="buttonNext" type="button" class="btn btn-xs btn-info">
                                        <span class="glyphicon glyphicon-step-forward" style="width: 32px;" title="Next"></span>&nbsp;
                                    </button>
                                </div>
                                <div class="col-sm-3 text-right">
                                    <button id="buttonLast" name="buttonLast" type="button" class="btn btn-xs btn-info">
                                        <span class="glyphicon glyphicon-fast-forward" style="width: 32px;" title="Last"></span>&nbsp;
                                    </button>
                                </div>
                            </div>
                            <div class="col-sm-3 text-right">
                                @if (Model.enablePdfRotation)
                                {
                                    <input type="button" id="buttonPdfRotateSave" name="buttonPdfRotateSave" value="Rotate & Save" class="btn btn-xs btn-info" style="width: 120px;" />
                                }
                            </div>
                        </div>

                        <div class="form-group" style="height: 600px;">
                            <iframe name="externalDocument" id="externalDocument" src="" style="width: 100%; height: 100%; z-index: 1;"></iframe>
                        </div>
                    </div>
                    <div id="errorMessageDiv" name="errorMessageDiv" class="text-center text-danger">
                        <br /><br /><br /><br /><br />
                        <label id="errorMessage" name="errorMessage" class="control-label"></label>
                    </div>
                </div>
            </div>
            <div id="imageLoading2" name="imageLoading2" style="position: fixed; left: 30%; top: 40%; display: none;">
                <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
            </div>
            <div id="requisitionFormDialog" name="requisitionFormDialog" style="display:none;"></div>
            <div class="modal fade in col-sm-12" id="modal-template-id" z-index="0" style="margin: auto;">
                <div class="modal-dialog col-sm-12" id="modal-template-content-id">
                    <div class="modal-content col-sm-12">
                        <div class="modal-header no-borders col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                            <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <h4 class="modal-title" id="modal-template-title-id"></h4>
                            </div>
                            <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        </div>
                        <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div id="pdfRotateandSave" name="pdfRotateandSave" style="display:none; margin-top: 8px; ">
                <div name="filePreviewPdfDiv" id="filePreviewPdfDiv" style="width: 600px; height: 600px; z-index: 1;"></div>
                <br />
                <div class="text-right">
                    <input type="button" id="buttonPdfRotate" name="buttonPdfRotate" value="Rotate" class="btn btn-default btn-sm btn-primary" onclick="rotatePdf();" />
                    <input type="button" id="buttonPdfSave" name="buttonPdfSave" value="Save" class="btn btn-default btn-sm btn-primary" onclick="savePdf();" />
                    <input type="button" id="buttonPdfCancel" name="buttonPdfCancel" value="Cancel" class="btn btn-default btn-sm btn-primary" data-dismiss="modal" style="margin-left: 60px;" />
                </div>
            </div>
        }
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>