@model Cerebrum.ViewModels.ExternalDocument.ExternalDocumentAssignmentResponse

<style type="text/css">
    #hrm-container {
        padding-top: 15px;
    }

        #hrm-container input, #hrm-container select, #hrm-container textarea {
            max-width: 100%;
        }

        #hrm-container .form-group {
            margin-bottom: 6px;
        }

        #hrm-container .clearable {
            background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center;
            padding: 3px 18px 3px 4px; /* Use the same right padding (18) in jQ! */
            border-radius: 3px;
            transition: background 0.4s;
        }

            #hrm-container .clearable.x {
                background-position: right 5px center;
            }
            /* (jQ) Show icon */
            #hrm-container .clearable.onX {
                cursor: pointer;
            }
            /* (jQ) hover cursor style */
            #hrm-container .clearable::-ms-clear {
                display: none;
                width: 0;
                height: 0;
            }
        /* Remove IE default X */

        #hrm-container .ui-dialog-titlebar {
            background-color: steelblue;
            color: yellow;
        }
</style>

<script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/HRM.js"></script>
<script type="text/javascript">
    var hasError = true;
    var hrmOfficeId = -1;
    var tbdPracticeDoctorId = "-1";
    var tbdPatientRecordId = -1;
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        @:hasError = false;
        @:hrmOfficeId = "@Model.officeId";
        @:tbdPracticeDoctorId = "@Model.tbdPracticeDoctorId";
        @:tbdPatientRecordId = "@Model.tbdPatientRecordId";
    }
</script>

<div id="hrm-container">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="body-content">
            <div class="col-sm-5 form-horizontal">
                <div class="form-group">
                    <label class="col-sm-5 text-right _345345">
                        Patient
                    </label>
                    <div class="col-sm-7" id="patientDiv" name="patientDiv"></div>


                     @*<div class="col-sm-4">
                        @Html.Hidden("hrmDemographicPatientRecordDoctorId", "")
                        @Html.Hidden("hrmPatientName", "")
                        @Html.TextBox("hrmPatientNameInput", "", new { @class = "form-control clearable", @placeholder = "Re-assign to ..." })
                    </div>*@
                </div>


                <div class="form-group">
                    <label class="col-sm-5 text-right control-label">
                        Re-assign to
                    </label>
                    <div class="col-sm-7">
                        @Html.Hidden("hrmDemographicPatientRecordDoctorId", "")
                        @Html.Hidden("hrmPatientName", "")
                        @Html.TextBox("hrmPatientNameInput", "", new { @class = "form-control clearable", @placeholder = "" })
                    </div>
                </div>



                <div class="form-group">
                    <label class="col-sm-5 text-right ">
                        Author Name
                    </label>
                    <div class="col-sm-7" id="sourceAuthorPhysicianDiv" name="sourceAuthorPhysicianDiv"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 text-right">
                        Sending Facility
                    </label>
                    <div class="col-sm-7" id="sendingFacilityDiv" name="sendingFacilityDiv"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 text-right">
                        Test Date
                    </label>
                    <div class="col-sm-7" id="testDateDiv" name="testDateDiv"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 control-label">
                        Doctor
                    </label>
                    <div class="col-sm-7">
                        @{
                            @Html.DropDownList("hrmPracticeDoctorIds", (object)new MultiSelectList(Model.doctors, "value", "text"), "Please Select", htmlAttributes: new { @class = "form-control ", @style = "height: auto;" })
                        }
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 control-label">
                        Abnormal
                    </label>
                    <div class="col-sm-7" id="abnormalDiv" name="abnormalDiv" style="padding-top: 8px;"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 control-label">
                        Description
                    </label>
                    <div class="col-sm-7" style="padding-right: 30px;">
                        @Html.TextBox("description", "", new {@id = "description", @class = "form-control clearable" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 control-label">
                        Mark Seen
                    </label>
                    <div class="col-sm-7" id="markSeenDiv" name="markSeenDiv" style="padding-top: 8px;"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5">
                        Assign To
                    </label>
                </div>
                <div class="col-sm-12" id="hrmRequisitionListDiv" name="hrmRequisitionListDiv" style="height: 400px; padding-left: 0px; overflow-y: auto;">
                    <table class="table" id="hrmRequisitionList" name="hrmRequisitionList" style="font-size: 14px; width: 100%">
                        <thead>
                            <tr style="font-size: 16px; font-weight: bold;">@*class="success vertical-center"*@
                                <td>Requisition / Loose Report</td>
                                <td>Date</td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                @*<div class="form-group text-center">
                    <div class="col-sm-10 text-right">
                        <input type="button" id="buttonAssign" name="buttonAssign" value="Assign" class="btn btn-info" />
                    </div>
                </div>*@
            </div>
            <div class="col-sm-7">
                <div class="col-sm-12" style="padding-left: 0px; padding-right: 0px;">
                    <table class="table" id="hrmList" name="hrmList" style="font-size: 14px; width: 100%">
                        <thead>
                            <tr  style="font-size: 16px; font-weight: bold;">@*class="success vertical-center"*@
                                <td>Received Date</td>
                                <td>Author Name</td>
                                <td>Sending Facility</td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="col-sm-12 text-right" style="padding-right: 0px;">
                    <ul class="pagination" id="pagination" name="pagination" style="margin: 0; margin-bottom: 16px;"></ul>&nbsp;
                </div>
                <div class="col-sm-12" id="reportViewType" name="reportViewType">
                    <input id="reportView" name="reportView" type="radio" checked value="preview" />
                    <label class="control-label" style="padding-left: 8px;">Report View</label>
                    <input id="reportView" name="reportView" type="radio" value="detail" style="margin-left: 96px;" />
                    <label class="control-label" style="padding-left: 8px;">Report XML Data</label>
                </div>
                <div name="externalDocumentReportView" id="externalDocumentReportView" class="form-group col-sm-12" style="height: 660px; padding-left: 0px; overflow-y: auto; display: none;">
                    <pre name="externalDocumentHRMText" id="externalDocumentHRMText" style="white-space: pre-wrap; display: none;"></pre>
                    <iframe name="externalDocumentHRMPdf" id="externalDocumentHRMPdf" src="" style="width: 100%; height: 100%; z-index: 1; display: none;"></iframe>
                    <img name="externalDocumentHRMImg" id="externalDocumentHRMImg" src="" alt="" style="display: none; color:cadetblue; border: cadetblue 1px solid;">
                </div>
                <div name="externalDocumentXmlDataView" id="externalDocumentXmlDataView" class="form-group col-sm-12" style="height: 660px; padding-left: 0px; overflow-y: auto; display: none;">
                    <table class="table" id="externalDocumentXmlData" name="externalDocumentXmlData" style="font-size: 14px; width: 100%">
                        <thead>
                            <tr style="font-size: 16px; font-weight: bold;">
                                <td>Tag</td>
                                <td>Value</td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div id="requisitionFormDialogHrm" name="requisitionFormDialogHrm" style="display:none;"></div>
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>