@model Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
@{
    ViewBag.ModuleName = "Work Sheet";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
@section customcss{
    @Styles.Render("~/Areas/Measurements/Content/css")
}
@section topscripts{    
    @*@Scripts.Render("~/bundles/schedule")*@
}

@section scripts{
    @Scripts.Render("~/bundles/ws")
    <script src="~/Areas/Measurements/Scripts/SendToContacts.js"></script>
}

@section patientinfo{
    @(await Html.PartialAsync("_PatientInfoMenuWS", (object)(object)Model))
}


<div class="modal fade" id="print-schedule-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title text-info" id="myModalLabel"><span>Schedule Print Preview</span></h4>
            </div>

            <div class="modal-body">
                <div id="divSchedulePrint">
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-schedule-preview" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>
<div>
    <div id="ws-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="ws-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>
<div id="div-prev-tests-menu" class="hidden" data-toggle="popover"></div>
@using (Html.BeginForm("Index", "Measurement", FormMethod.Post, new { @id = "frm-ws", model = @Model }))
{
    @Html.HiddenFor(x => (object)x.AppointmentID)
    @Html.HiddenFor(x => (object)x.TestID)
    @Html.HiddenFor(x => (object)x.AppointmentTestID)
    @Html.HiddenFor(x => (object)x.PracticeID)
    @Html.HiddenFor(x => (object)x.GroupID)
    @Html.HiddenFor(x => (object)x.AppointmentTestLogID)
    @Html.HiddenFor(x => (object)x.PatientID)
    @Html.HiddenFor(x => (object)x.HasRawData)
    @Html.HiddenFor(x => (object)x.RootPhraseFormat)
    @Html.HiddenFor(x => x.ReportPhraseFormatTable, new { @id = "RootPhraseFormatTable" })
    @Html.HiddenFor(m => (object)m.IsWorkList)
    @Html.HiddenFor(x => (object)x.ActionOnAbnormal)
    @Html.HiddenFor(x => (object)x.SetForReview)
    @Html.HiddenFor(x => (object)x.UserID)
    @Html.HiddenFor(x => (object)x.OfficeId)
    @Html.HiddenFor(x => (object)x.DateStr)
    @Html.HiddenFor(x => (object)x.Date)
    @Html.HiddenFor(m => (object)m.DoctorID)
    @Html.HiddenFor(m => (object)m.AssociatedDoctorID)
    @Html.HiddenFor(m => (object)m.RawDataLoaded)
    @Html.HiddenFor(m => (object)m.IsClassicAppointment)
    @Html.HiddenFor(m => (object)m.PracticeDoctorID)
       

    //showmode list
    for (int i = 0; i < Model.ShowModeList.Count; i++)
    {
        @Html.HiddenFor(m => (object)m.ShowModeList[i].Text)
        @Html.HiddenFor(m => (object)m.ShowModeList[i].Value)
    }

    <div id="div-links" class="vp-links _34564567 _toCloneIntoWS" style="display:none"><img src="~/Content/fancybox_loading.gif" /></div>
    <div class="row">&nbsp;</div>   

if (@Model.GroupID == 1)
{
    <div id="div-s">
        <div class="padding-top padding-bottom _3453457" id="div-scroll-ws">

            <div class="row">
                <div class="col-sm-3">
                    <div style="margin-bottom:0px;">
                        <div class="form-inline">
                            <div class="form-group ">
                                <ul class="top-legend">
                                    <li>
                                        <div class="pull-left legend-label">
                                            <span class="">&nbsp;</span>
                                        </div>
                                    </li>
                                    @foreach (var item in Model.RangeTypes)
                                    {
                                        <li class="li-color-desc text-center">
                                            <div class="pull-left legend-name">@item.name</div>
                                            <div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:@item.color;">&nbsp;&nbsp;</div>
                                        </li>
                                    }
                                    <li class="li-color-desc">
                                        <div class="pull-left legend-name">No Range Available</div>
                                        <div class="pull-left legend-color-container " style="border: 1px solid gray; border-image: none; background-color:lightgray;">&nbsp;&nbsp;</div>
                                    </li>
                                    <li class="li-color-desc">
                                        <div class="pull-left legend-name">Out of Range</div>
                                        <div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:#FFF8C6;">&nbsp;&nbsp;</div>
                                    </li>
                                </ul>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-sm-9">
                    <div class="form-group">
                        @*pull-right*@
                    <div style="float: left" id="_placeHolder78"></div>
                    <div style="float: right">@Html.DropDownListFor(m => m.ShowMode, new SelectList(Model.ShowModeList, "Value", "Text", 1), new { @class = "dropdown form-control input-sm" })</div> <span id="_swictStateMeas" class="dropdown form-control input-sm"></span>

                    <script>
        var _____mmm = function (obj) {
            //var layoutType = $("#GroupID").val() == 1 ? "_ReportPhrases_Vertical" : "_ReportPhrases";
            //var url = "Measurements/Measurement/" + layoutType + "?appointmentID=" + $("#AppointmentID").val() + "&testID=" + $("#TestID").val() + "&appointmentTestLogID=" + $("#AppointmentTestLogID").val();

            //$.get(url, function (data) {
                $('#dialog8').dialog({
                    modal: false,
                    title: '__23453456_',
                    height: 300,
                    width: $(window).width() / 2,

                    open: function (event, ui) {
                        $('#patient8').closest('div[role="dialog"]').css({ top: 100, left: 100 });
                    }
                });
            //    $('#patient8').empty();
            //    $('#patient8').append(data);
            //});
        };



        $(document).ready(function () {
            $('.top-legend').hide();
            var clone78 = $('.top-legend').clone();
            clone78.show().appendTo($('#_placeHolder78'));


            var state = false;
            var lbl = 'Expand View';
            $('#_swictStateMeas').html(lbl);

            $('#_swictStateMeas').on('click', function () {
                if (state) {
                    lbl = 'Expand';
                    $('.ddl-max, .chk-discard').hide();
                    state = false;
                }
                else {
                    lbl = 'Contract';
                    $('.ddl-max, .chk-discard').show();
                    state = true;
                }
                $('#_swictStateMeas').html(lbl + ' View');
            });

            $('.measTxtBox').on('click', function () {
                if (!$('#dialog8').closest('.ui-dialog').is(':visible')) {
                    _____mmm();
                }

            });

			$('#chkShowHideDiscardMaxControls').on('click', function () {
                if($(this).prop('checked')){
                    $('.ddl-max, .chk-discard').show();
                }
                else {
                    $('.ddl-max, .chk-discard').hide();
                }
            })

        });
                    </script>
                </div>

            </div>
        </div>

        <div class="row">

            <div id="dialog8" title="___">
                <div id="patient8"><div id="div-phrases">@(await Html.PartialAsync("_ReportPhrases_Vertical", (object)(object)Model))</div></div>

            </div>


            @*<div class="col-sm-3">
                    <div class="pre-scrollable _990987" style="direction: ltr;">
                        <div id="div-phrases">@(await Html.PartialAsync("_ReportPhrases_Vertical", (object)(object)Model))</div>
                    </div>
                </div>*@
            <div class="col-sm-12">
                <div id="div-meas">@(await Html.PartialAsync("_Measurements_Vertical", (object)(object)Model))</div>
                <div class="clearfix">&nbsp;</div>
                <div id="div-change" style="width:100%"> </div>
            </div>
        </div>

    </div>
    <div class="clearfix">&nbsp;</div>
</div>
}
else
{
    <div id="div-phrases">@(await Html.PartialAsync("_ReportPhrases", (object)(object)Model))</div>
    <div class="row">&nbsp;</div>
    <div id="div-change" class="__23497923">
        <div style="margin-bottom:0px;">
            <div class="form-inline">
                <div class="form-group ">
                    <ul class="top-legend">
                        <li>
                            <div class="pull-left legend-label">
                                <span class=""> &nbsp;</span>
                            </div>
                        </li>
                        @foreach (var item in Model.RangeTypes)
                        {
                            <li class="li-color-desc text-center">
                                <div class="pull-left legend-name">@item.name</div>
                                <div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:@item.color;">&nbsp;&nbsp;</div>
                            </li>
                        }
                        <li class="li-color-desc">
                            <div class="pull-left legend-name">No Range Available</div>
                            <div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:lightgray;">&nbsp;&nbsp;</div>
                        </li>
                        <li class="li-color-desc">
                            <div class="pull-left legend-name">Out of Range</div>
                            <div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:#FFF8C6;">&nbsp;&nbsp;</div>
                        </li>
                    </ul>
                </div>
                <div class="form-group pull-right">
                    @Html.DropDownListFor(m => m.ShowMode, new SelectList(Model.ShowModeList, "Value", "Text", 1), new { @class = "dropdown form-control input-sm" })
                </div>
            </div>
        </div>
        <div id="div-meas">@(await Html.PartialAsync("_Measurements", (object)(object)Model))</div>
        <div class="clearfix">&nbsp;</div>
    </div>
}

    <div class="paddedLeft" id="div-comments">
        @Html.Action("_Notes",
    new
    {
        appointmentID = Model.AppointmentID,
        testID = Model.TestID,
        appointmentTestLogID = Model.AppointmentTestLogID
    })
    </div>
       

    <div class="row">&nbsp;</div>
    <div id="cc-docs-link" style="margin-bottom:5px;">
        @{
            var ccDoctorLink = new Cerebrum.ViewModels.Common.VMCCDoctorLink();
            ccDoctorLink.PatientId = Model.PatientID;
            ccDoctorLink.AppointmentId = Model.AppointmentID;
            ccDoctorLink.PatientFullName = "";
        }
        @{ await Html.RenderPartialAsync("_CCDoctorsLink", (object)ccDoctorLink); }
    </div>
    <div class="row">&nbsp;</div>

    <div class="row">
        <div class="col-sm-6">
            <div class="text-left nopadding form-group form-inline form-group-sm">
                <span><b>Log</b></span>
                @Html.DropDownListFor(m => m.AppointmentTestLogID,
            new SelectList(Model.AppointmentTestLogs, "Value", "Text", @Model.AppointmentTestLogID),
            new { @class = "dropdown ", @id = "dd", @style = "min-width:100px" })
            </div>
        </div>
        <div class="col-sm-6">

            @if (!Model.IsClassicAppointment)
            {
                <div class='form-inline pull-right'>
                    <div class="form-group form-group-sm">
                        <a href="#" class="btn  btn-default btn-sm icon-color custom-btn" id="btn-save" data-ds-url='@Url.Action("index", "daysheet",
                        new
                        {
                            Area = "Schedule",
                            OfficeId = Model.OfficeId,
                            Date = Model.DateStr
                        })' data-url='@Url.Action("Save_WS_Data")'>
                            <i class="glyphicon glyphicon-floppy-save"></i>Save
                        </a>
                        <a data-ds-url='@Url.Action("index", "daysheet",
                            new
                            {
                                Area = "Schedule",
                                OfficeId = Model.OfficeId,
                                Date = Model.DateStr
                            })'
                           href="#" class="btn btn-default btn-sm custom-btn" id="btn-save-draft" data-url='@Url.Action("Save_Draft")'>
                            <i class="glyphicon glyphicon-floppy-save"></i>Save & Change Status
                        </a>
                        <a target="_blank"
                           data-ds-url='@Url.Action("index", "daysheet",
                             new
                             {
                                 Area = "Schedule",
                                 OfficeId = Model.OfficeId,
                                 Date = Model.DateStr
                             })'
                             class="btn btn-default btn-sm custom-btn-primary" href="#" data-url='@Url.Action("SendReport", new { appointmentID = Model.AppointmentID, testID = Model.TestID })' id="btn-send-ws-rep">
                            <i class="glyphicon glyphicon-share-alt"></i>
                            Send Report
                        </a>                       
                    </div>
                </div>
            }
        </div>
    </div>

}
<br />
<br />

@*
    <script>


            (function(){

                $(function() {

                    $('#txtReportPhrase_154').tinymce({

                        // Location of TinyMCE script
                        script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
                        theme: "advanced",

                        height: "300",
                        width: "300",
                        verify_html : false,
                        plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

                        // Theme options
                        theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
                        theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
                        theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
                        theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
                        theme_advanced_toolbar_location : "top",
                        theme_advanced_toolbar_align : "left",
                        theme_advanced_statusbar_location : "bottom",
                        theme_advanced_resizing : false,

                        // Example content CSS (should be your site CSS)
                        content_css : "@Url.Content("~/Scripts/tinymce/css/content.css")",
                    convert_urls : false,

                    // Drop lists for link/image/media/template dialogs
                    template_external_list_url : "lists/template_list.js",
                    external_link_list_url : "lists/link_list.js",
                    external_image_list_url : "lists/image_list.js",
                    media_external_list_url : "lists/media_list.js"

                });

            });

            })();
    </script>*@


@*$('#txtReportPhrase_154').tinymce({
             script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
             theme: "advanced",

             height: "500",
             width: "790",
             verify_html : false,
             plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

             // Theme options
             theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
             theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
             theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
             theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
             theme_advanced_toolbar_location : "top",
             theme_advanced_toolbar_align : "left",
             theme_advanced_statusbar_location : "bottom",
             theme_advanced_resizing : false,

             // Example content CSS (should be your site CSS)
             content_css : "@Url.Content("~/content/bootstrap-theme.min.css")",
         convert_urls : false,

         // Drop lists for link/image/media/template dialogs
         template_external_list_url : "lists/template_list.js",
         external_link_list_url : "lists/link_list.js",
         external_image_list_url : "lists/image_list.js",
         media_external_list_url : "lists/media_list.js"
    }

         );

          $(document).ready(function () {
         // ### Initialisation from TinyMCE Richtexteditor ### //
         tinymce.init({
             mode: "specific_textareas",
             editor_selector: "#txtReportPhrase_154",
             theme: "modern",
             width: 360,
             height: 200,
             resize: false,
             toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
             style_formats: [
                 { title: 'Bold text', inline: 'b' },
                 { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
                 { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
                 { title: 'Example 1', inline: 'span', classes: 'example1' },
                 { title: 'Example 2', inline: 'span', classes: 'example2' },
                 { title: 'Table styles' },
                 { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
             ]
         });
     });


     tinymce.init({
         selector: '.txtArea',
         theme: "modern",
         width: 360,
         height: 200,
         resize: false,
         toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
         style_formats: [
                 { title: 'Bold text', inline: 'b' },
                 { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
                 { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
                 { title: 'Example 1', inline: 'span', classes: 'example1' },
                 { title: 'Example 2', inline: 'span', classes: 'example2' },
                 { title: 'Table styles' },
                 { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
         ]
     });



*@
