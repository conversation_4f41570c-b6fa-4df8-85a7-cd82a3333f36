@*
    notes notes notes notes notes:
    notes notes notes notes notes:
    this file is being "minified" to reduce the download file size
    otherwise, in some cases the file size could be more than 3MB
    so now this file is very hard to read - in Visual Studio, you can select-all, then right-click, then click "Format Selection"
    however, remember to shift-tab everything back to left to remove all indents when you are done (select-all, then shift-tab many times)
    we do not want to install or add any 3rd part tools at the top to manage this
    also, although gzip might be another option, but at the time I am typing this, our servers having ongoing cpu/memory issues, gzip will take cpu so it's not the best for us now
*@
@model Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
@using AwareMD.Cerebrum.Shared.Enums;
@{
    ViewBag.ModuleName = "Work Sheet";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    var isBeingSent = Model.AppointmentTestStatusId == (int)AppointmentTestStatuses.BeingSent;
}
@section customcss{
@Styles.Render("~/Areas/Measurements/Content/css")
}
@section topscripts{
@*<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.5/handlebars.js"></script>*@
@*<script src="~/Areas/Schedule/Scripts/schedule.js"></script>*@
@Scripts.Render("~/bundles/schedule")
}

@section scripts{
@Scripts.Render("~/bundles/ws")
<script src="~/Areas/Measurements/Scripts/SendToContacts.js"></script>
<script src="~/Scripts/autosize.min.js"></script>
}

@section patientinfo{
@(await Html.PartialAsync("_PatientInfoMenuWS", (object)(object)Model))
}


<style>
    
.resizable {
width: 50%;
height:100%;
padding: 0;
display: block;
float:left;
color:#000;
position:relative; 
}

.resizable1 {
/*background-color: #f3f3f3;*/
float:left; 
height:100%;
}

.resizable2 {
/*background-color: #eaeaea;*/
float:right;
height:100%;
}

.resizable .inner {
padding:30px;
overflow:hidden;
overflow-y:auto;
position:absolute; 
height:100%;
top:0;
left:0;
-webkit-box-sizing: border-box;
-moz-box-sizing: border-box;
box-sizing: border-box;
}
.resizable1 .inner {
margin-right:7px;
}
.resizable2 .inner {
margin-left:7px;
}

.ui-resizable-e { 
cursor: e-resize; 
display:block!important;
width: 14px; 
right: -7px; 
top: 0; 
bottom: 0; 
background: #333;
}
.range-div{
height: 100px;
}

</style>
<input type="hidden" id="hd-set-normal-for-all" value="" />
<div class="modal fade" id="print-schedule-modal-container" tabindex="-1" role="dialog">
<div class="modal-dialog modal-lg">
<div class="modal-content">
<div class="modal-header">
<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
<h4 class="modal-title text-info" id="myModalLabel"><span>Schedule Print Preview</span></h4>
</div>
<div class="modal-body"><div id="divSchedulePrint"> </div></div>
<div class="modal-footer">
<button id="btn-print-schedule-preview" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
<button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
</div>
</div><!--End modal content-->
</div><!--End modal dialog-->
</div>
<div>
<div id="ws-modal-container" class="modal fade" tabindex="-1" role="dialog">
<div class="modal-dialog" role="document" style="width:auto">
<div class="modal-content">
<div class="modal-body ">
<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
<div id="ws-modal-content"></div>
</div>
</div><!-- /.modal-content -->
</div><!-- /.modal-dialog -->
</div><!-- /.modal -->
</div>
@using (Html.BeginForm("Index", "Measurement", FormMethod.Post, new { @id = "frm-ws", model = @Model }))
{
@Html.HiddenFor(x => (object)x.AppointmentID)
@Html.HiddenFor(x => (object)x.TestID)
@Html.HiddenFor(x => (object)x.AppointmentTestID)
@Html.HiddenFor(x => (object)x.PracticeID)
@Html.HiddenFor(x => (object)x.GroupID)
@Html.HiddenFor(x => (object)x.AppointmentTestLogID)
@Html.HiddenFor(x => (object)x.AppointmentTestLastLogID)
@Html.HiddenFor(x => (object)x.PatientID)
@Html.HiddenFor(x => (object)x.HasRawData)
@Html.HiddenFor(x => (object)x.RootPhraseFormat)
@Html.HiddenFor(x => x.ReportPhraseFormatTable, new { @id = "RootPhraseFormatTable" })
@Html.HiddenFor(m => (object)m.IsWorkList)
@Html.HiddenFor(x => (object)x.ActionOnAbnormal)
@Html.HiddenFor(x => (object)x.SetForReview)
@Html.HiddenFor(x => (object)x.UserID)
@Html.HiddenFor(x => (object)x.OfficeId)
@Html.HiddenFor(x => (object)x.DateStr)
@Html.HiddenFor(x => (object)x.Date)
@Html.HiddenFor(m => (object)m.DoctorID)
@Html.HiddenFor(m => (object)m.AssociatedDoctorID)
@Html.HiddenFor(m => (object)m.RawDataLoaded)
@Html.HiddenFor(m => (object)m.IsClassicAppointment)
@Html.HiddenFor(m => (object)m.PracticeDoctorID)
@Html.HiddenFor(m => (object)m.IsAmended)
@Html.HiddenFor(m => (object)m.AppointmentTestStatusId)


//writing operator list
@*for (int i = 0; i < Model.MeasurementOperators.Count; i++)
{
@Html.HiddenFor(m => (object)m.MeasurementOperators[i].Text)
@Html.HiddenFor(m => (object)m.MeasurementOperators[i].Value)
}*@

//writing log list
@*for (int i = 0; i < Model.AppointmentTestLogs.Count; i++)
{
@Html.HiddenFor(m => (object)m.AppointmentTestLogs[i].Text)
@Html.HiddenFor(m => (object)m.AppointmentTestLogs[i].Value)
}*@

//showmode list
for (int i = 0; i < Model.ShowModeList.Count; i++)
{
@Html.HiddenFor(m => (object)m.ShowModeList[i].Text)
@Html.HiddenFor(m => (object)m.ShowModeList[i].Value)
}

<div id="div-links" class="vp-links _34564567 _toCloneIntoWS" style="display:none"><img src="~/Content/fancybox_loading.gif" /></div>
<div class="row">&nbsp;</div>
@*<div id="div-medications"><img src="~/Content/fancybox_loading.gif" /></div>*@

if (@Model.TestGroupLayoutType == TestGroupLayoutType.SideBySide )
{
<div id="div-s" >
@*<div class="padding-top padding-bottom _3453457" id="div-scroll-ws">*@
<div class="padding-top"  id="div-scroll-ws">
<div class="row">
<div class="col-sm-3">
<div style="margin-bottom:0px;">
<div class="form-inline">
<div class="form-group ">
<ul class="top-legend">
@if (Model.AppointmentTestLogID > 0) { <li id="li-zscore"><div id="div-zcore-container"></div></li> }
<li>
<div class="pull-left legend-label">
<span class="">&nbsp;</span>
</div>
</li>
@foreach (var item in Model.RangeTypes)
{
<li class="li-color-desc text-center">
<div class="pull-left legend-name">@item.name</div>
<div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:@item.color;">&nbsp;&nbsp;</div>
</li>
}
<li class="li-color-desc">
<div class="pull-left legend-name">No Range Available</div>
<div class="pull-left legend-color-container " style="border: 1px solid gray; border-image: none; background-color:lightgray;">&nbsp;&nbsp;</div>
</li>
<li class="li-color-desc">
<div class="pull-left legend-name">Out of Range</div>
<div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:#FFF8C6;">&nbsp;&nbsp;</div>
</li>
</ul>
</div>

</div>
</div>
</div>
<div class="col-sm-9">
<div class="form-group">
@*pull-right*@
<div style="float: left" id="_placeHolder78"></div>
<div style="float: right">@Html.DropDownListFor(m => m.ShowMode, new SelectList(Model.ShowModeList, "Value", "Text", 1), new { @class = "dropdown form-control input-sm" })</div><div style="float: right; margin-right: 22px; margin-top: 3px;">Show/Hide 'Discard' and 'Max' values <input type="checkbox" id="chkShowHideDiscardMaxControls" /></div>
<script>
$(document).ready(function () {
$('.top-legend').hide();
var clone78 = $('.top-legend').clone();
clone78.show().appendTo($("#_placeHolder78"));

$('#chkShowHideDiscardMaxControls').on('click', function () {
if($(this).prop('checked')){
$('.ddl-max, .chk-discard').show();
}
else {
$('.ddl-max, .chk-discard').hide();
}
})
});
</script>
</div>

</div>
</div>
<div class="row">
<div>
<div class="col-sm-3 resizable resizable1">
<div class="pre-scrollable" style="direction: ltr;">
<div id="div-phrases">@(await Html.PartialAsync("_ReportPhrases_Vertical", (object)(object)Model))</div>
</div>
</div>
<div class="col-sm-9 resizable resizable2">
<div id="div-meas">@(await Html.PartialAsync("_Measurements_Vertical", (object)(object)Model))</div>
<div class="clearfix">&nbsp;</div>
<div id="div-change" style="width:100%"> </div>
</div>
</div>
</div>
</div>
<div class="clearfix">&nbsp;</div>
</div>
}
else
{
<div id="div-phrases">@(await Html.PartialAsync("_ReportPhrases", (object)(object)Model))</div>
<div class="row">&nbsp;</div>
<div id="div-change" class="__23497923">
<div style="margin-bottom:0px;">
<div class="form-inline">
<div class="form-group ">
<ul class="top-legend">

@if (Model.AppointmentTestLogID > 0)
{
<li id="li-zscore">
<div id="div-zcore-container"></div>
</li>
}

<li>
<div class="pull-left legend-label">
<span class=""> &nbsp;</span>
</div>
</li>
@foreach (var item in Model.RangeTypes)
{
<li class="li-color-desc text-center">
<div class="pull-left legend-name">@item.name</div>
<div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:@item.color;">&nbsp;&nbsp;</div>
</li>
}
<li class="li-color-desc">
<div class="pull-left legend-name">No Range Available</div>
<div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:lightgray;">&nbsp;&nbsp;</div>
</li>
<li class="li-color-desc">
<div class="pull-left legend-name">Out of Range</div>
<div class="pull-left legend-color-container" style="border: 1px solid gray; border-image: none; background-color:#FFF8C6;">&nbsp;&nbsp;</div>
</li>
</ul>
</div>
<div class="form-group pull-right">
@Html.DropDownListFor(m => m.ShowMode, new SelectList(Model.ShowModeList, "Value", "Text", 1), new { @class = "dropdown form-control input-sm" })
</div>
</div>
</div>
<div id="div-meas">@(await Html.PartialAsync("_Measurements", (object)(object)Model))</div>
<div class="clearfix">&nbsp;</div>
</div>
}

<div class="paddedLeft" id="div-comments">
@(await Html.PartialAsync("_Notes", (object)(object)Model))
</div>

@*<div class="row">&nbsp;</div>
<div id="div-associated-docs">
@Html.Action("GetAssociatedDoctors", "VP", new { area = "VP",patientID =Model.PatientID })
</div>*@

<div class="row">&nbsp;</div>


var ccDoctorLink = new Cerebrum.ViewModels.Common.VMCCDoctorLink();
ccDoctorLink.PatientId = Model.PatientID;
ccDoctorLink.AppointmentId = Model.AppointmentID;
ccDoctorLink.PatientFullName = "";
ccDoctorLink.IsModal = false;

await Html.RenderPartialAsync("_CCDoctorsLink", (object)ccDoctorLink);

<div class="row">&nbsp;</div>



<div class="row">
<div class="col-sm-6">
<div class="text-left nopadding form-group form-inline form-group-sm">
<span><b>Log</b></span>
@Html.DropDownListFor(m => m.AppointmentTestLogID,
new SelectList(Model.AppointmentTestLogs, "Value", "Text", @Model.AppointmentTestLogID),
new { @class = "dropdown ", @id = "dd", @style = "min-width:100px" })
</div>
</div>
<div class="col-sm-6">

@*@if (!Model.IsClassicAppointment)*@
@{
<div class='form-inline pull-right'>
<div class="form-group form-group-sm">
@if (Model.IsAmended)
{
if (Model.AppointmentTestStatusId == (int)AppointmentTestStatuses.BeingSent)
{
<div style="margin-right:15px;padding:5px;margin-top:2px;" class="div-test-status pull-left BeingSent">
<span class="status-desc-holder">Status: Being Sent</span>
</div>
}
else
{
<div style="margin-right:15px;padding:5px;margin-top:2px;" class="div-test-status pull-left ReportCompleted">
<span class="status-desc-holder">Status: Report Completed</span>
</div>
}
}

<a href="#" class="btn  btn-default btn-sm icon-color custom-btn" id="btn-save" data-ds-url='@Url.Action("index", "daysheet",
new
{
Area = "Schedule",
OfficeId = Model.OfficeId,
Date = Model.DateStr
})' data-url='@Url.Action("Save_WS_Data")'>
<i class="glyphicon glyphicon-floppy-save"></i>Save
</a>

@if (!Model.IsAmended)
{
<a data-ds-url='@Url.Action("index", "daysheet",
new
{
Area = "Schedule",
OfficeId = Model.OfficeId,
Date = Model.DateStr
})'
href="#" class="btn btn-default btn-sm custom-btn" id="btn-save-draft" data-url='@Url.Action("Save_Draft")'>
<i class="glyphicon glyphicon-floppy-save"></i>Save & Change Status
</a>
<a target="_blank"
data-ds-url='@Url.Action("index", "daysheet",
new
{
Area = "Schedule",
OfficeId = Model.OfficeId,
Date = Model.DateStr
})'
class="btn btn-default btn-sm custom-btn-primary" href="#" data-url='@Url.Action("SendInterimReport", new { appointmentID = Model.AppointmentID, testID = Model.TestID })' id="btn-send-ws-rep">
<i class="glyphicon glyphicon-share-alt"></i>
Send Interim Report
</a>
}
@if (!isBeingSent)
{
<a target="_blank"
data-ds-url='@Url.Action("index", "daysheet",
new
{
    Area = "Schedule",
    OfficeId = Model.OfficeId,
    Date = Model.DateStr
})'
class="btn btn-default btn-sm custom-btn-primary" href="#" data-url='@Url.Action("SendReport", new { appointmentID = Model.AppointmentID, testID = Model.TestID })' id="btn-send-ws-rep">
<i class="glyphicon glyphicon-share-alt"></i>
Send Report
</a>
}
@*<a target="_blank" class="btn btn-default btn-sm" href="#" data-url='@Url.Action("SendReportList", new { appointmentID = Model.AppointmentID, testID = Model.TestID })' id="btn-send-report">Send History</a>*@
</div>
</div>
}
</div>
</div>

}
<br />
<br />


<script>
$(function () {
$(".resizable1").resizable(
{
autoHide: true,
handles: 'e',
resize: function (e, ui) {
var parent = ui.element.parent();
//alert(parent.attr('class'));
var remainingSpace = parent.width() - ui.element.outerWidth(),
divTwo = ui.element.next(),
divTwoWidth = (remainingSpace - (divTwo.outerWidth() - divTwo.width()) / parent.width() * 100 + "%";
divTwo.width(divTwoWidth);
},
stop: function (e, ui) {
var parent = ui.element.parent();
ui.element.css(
{
width: ui.element.width() / parent.width() * 100 + "%",
});
}
});
});

</script>



@*
<script>


(function(){

$(function() {

$('#txtReportPhrase_154').tinymce({

// Location of TinyMCE script
script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
theme: "advanced",

height: "300",
width: "300",
verify_html : false,
plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

// Theme options
theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
theme_advanced_toolbar_location : "top",
theme_advanced_toolbar_align : "left",
theme_advanced_statusbar_location : "bottom",
theme_advanced_resizing : false,

// Example content CSS (should be your site CSS)
content_css : "@Url.Content("~/Scripts/tinymce/css/content.css")",
convert_urls : false,

// Drop lists for link/image/media/template dialogs
template_external_list_url : "lists/template_list.js",
external_link_list_url : "lists/link_list.js",
external_image_list_url : "lists/image_list.js",
media_external_list_url : "lists/media_list.js"

});

});

})();
</script>*@


@*$('#txtReportPhrase_154').tinymce({
script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
theme: "advanced",

height: "500",
width: "790",
verify_html : false,
plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

// Theme options
theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
theme_advanced_toolbar_location : "top",
theme_advanced_toolbar_align : "left",
theme_advanced_statusbar_location : "bottom",
theme_advanced_resizing : false,

// Example content CSS (should be your site CSS)
content_css : "@Url.Content("~/content/bootstrap-theme.min.css")",
convert_urls : false,

// Drop lists for link/image/media/template dialogs
template_external_list_url : "lists/template_list.js",
external_link_list_url : "lists/link_list.js",
external_image_list_url : "lists/image_list.js",
media_external_list_url : "lists/media_list.js"
}

);

$(document).ready(function () {
// ### Initialisation from TinyMCE Richtexteditor ### //
tinymce.init({
mode: "specific_textareas",
editor_selector: "#txtReportPhrase_154",
theme: "modern",
width: 360,
height: 200,
resize: false,
toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
style_formats: [
{ title: 'Bold text', inline: 'b' },
{ title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
{ title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
{ title: 'Example 1', inline: 'span', classes: 'example1' },
{ title: 'Example 2', inline: 'span', classes: 'example2' },
{ title: 'Table styles' },
{ title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
]
});
});


tinymce.init({
selector: '.txtArea',
theme: "modern",
width: 360,
height: 200,
resize: false,
toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
style_formats: [
{ title: 'Bold text', inline: 'b' },
{ title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
{ title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
{ title: 'Example 1', inline: 'span', classes: 'example1' },
{ title: 'Example 2', inline: 'span', classes: 'example2' },
{ title: 'Table styles' },
{ title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
]
});



*@
