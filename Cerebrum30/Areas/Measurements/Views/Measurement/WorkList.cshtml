@model  Cerebrum.ViewModels.Measurements.VMWorkList
@{
    ViewBag.Title = "To Do/ Worklist";
    ViewBag.ModuleName = "To Do/ Worklist";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";

    var filterRequest = CerebrumUser.WorkListFilterRequest;

}

@section topscripts{
    <script src="~/Areas/Measurements/Scripts/SendToContacts.js"></script>
}

<link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
<script src="//cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="//cdn.datatables.net/1.10.13/css/jquery.dataTables.min.css" />


@section customcss{

    <style>
        .ds-filter-container {
            /*padding: 15px;
            background-color: #F0F0F0;*/
        }

        .tooltip.top .tooltip-inner, .tooltip.right .tooltip-inner, .tooltip.left .tooltip-inner, .tooltip.bottom .tooltip-inner {
            background-color: #333;
            font-weight: normal !important;
        }

        .tooltip.top .tooltip-arrow, .tooltip.right .tooltip-arrow, .tooltip.left .tooltip-arrow, .tooltip.bottom .tooltip-arrow {
            border-top-color: none; /*#808080;*/
        }
    </style>
}

@section scripts{
    <script>
        $(document).ready(function () {

            var LoadData = function () {
                var doctorId = $('#frm-wl #filterRequest_SelectedDoctorID').val();

                var doctorName = $('#frm-wl #filterRequest_SelectedDoctorID').find('option:selected').text();
                if (doctorName == null || doctorName == '')
                {
                    doctorName = $('#frm-wl #filterRequest_DoctorName').val();
                }
                var lstDocName = '';
                if (doctorId > 0)
                {
                    lstDocName = '<h4 class="nopadding">List of studies ready for Dr.' + doctorName + '</h4>';
                }

                var url = "Measurements/Measurement/WS_List_Data";
                var data = $("#frm-wl").serialize();
                var loaderURL = "../../Content/fancybox_loading.gif";
                $("#div-search-results").html("<img src='" + loaderURL + "' />");

                $.ajax({
                    url: url,
                    type: "Post",
                    data: data,
                    complete: function () {
                        $('#ajax-loader').hide();
                    },
                    success: function (result) {
                        if (result.success != null) {
                            if(result.success == false)
                            {
                                var errors = result.message.split(',');
                                var ul = '<ul style="list-style:none;">';
                                for (var i = 0; i < errors.length; i++) {
                                    var li = '<li>' + errors[i] + ' </li>';
                                    ul += li;
                                }
                                ul += '</ul>';
                                var div = '<div style="padding:10px;" class="text-danger">' + ul + '</div>';

                                showMessageModal("error", div, true);
                                $("#div-search-results").html("");
                            }
                        }
                        else {
                            $("#div-search-results").html(result);
                            showNotificationMessage('success', 'Data loaded');
                            $('[data-toggle="tooltip"]').tooltip({ animation: false });
                            $('#lst-doc-name').html(lstDocName);
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        checkAjaxError(jqXHR);
                    }
                });
            }

            $("#txt-start-date").datepicker({
                changeMonth: true,
                changeYear: true
            });
            $("#txt-end-date").datepicker({
                changeMonth: true,
                changeYear: true
            });
            if ($('#frm-wl #filterRequest_FilterSet').val() == 'True') {
                LoadData();
            }

            $(document).on('click', '#btn-submit', function (e) {
                e.preventDefault();
                resetPageToOne();
                LoadData();
            });

            $(document).on('click', '.chk-search-mode', function (e) {
                LoadData();
            });

            $(document).on('click', '.wl-paging-item-size', function (e) {
                e.preventDefault();
                var btn = $(this);
                var pageSize = btn.data('wl-pagesize');
                $('#frm-wl #filterRequest_PageSize').val(pageSize);
                resetPageToOne();
                LoadData();
            });

            $(document).on('click', '.wl-paging-item', function (e) {
                e.preventDefault();
                var btn = $(this);
                var page = btn.data('wl-pagenum');
                $('#frm-wl #filterRequest_Page').val(page);
                LoadData();
            });

        });

        $(document).on('click', '#btn-send-report', function (e) {
            $('#ajax-loader').show();
            e.preventDefault();
            var url = $(this).data("url");
            $("#worklist-modal-content").load(url, function () {

                $("#worklist-modal-container").modal({
                    keyboard: false,
                    backdrop: 'static'
                }, 'show');

                $('#ajax-loader').hide();
            });
        });

        $(document).on('click', '.btn-reassign', function (e) {
            e.preventDefault();
            var url = $(this).data("url");
            var dd = $(this).data("doc-cntrl");
            //var apptid = $(this).data("data-app-id");
            var apptestid = $(this).data("app-test-id");
            var patientid = $(this).data("app-patient-id");
            var selectedDocID = $('#' + dd).val();

            $.ajax({
                url: url,
                data: { appointmentTestID: apptestid, docID: selectedDocID, patientID: patientid },
                success: function (data) {
                    if (data.Errored == "1") {
                        showNotificationMessage('error', data.Result);
                    }
                    else {

                        showNotificationMessage('success', 'Successfully reassigned');
                        //console.log(data.Message);
                        var tr = $('#td_' + apptestid).closest('tr');
                        tr.css("background-color", "#FF3700");
                        tr.fadeOut(400, function () {
                            tr.remove();
                        });

                        //$('#td_' + apptestid).html(data.Result);
                        //window.location = window.location;
                    }
                },
                error: function (xhr, thrownError) {
                    showNotificationMessage('error', xhr.status + " " + thrownError);
                }
            });
        });

        function resetPageToOne()
        {
            var page = 1; //reset back to 1
            $('#frm-wl #filterRequest_Page').val(page);
        }
    </script>
}

@section patientinfo{
    @*@RenderSection("patientinfo", required: false)*@
}

<div>
    <div id="worklist-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="worklist-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>

@{
    await Html.RenderPartialAsync("_worklistFilters", (object)filterRequest);
}

