@model Cerebrum.ViewModels.Measurements.VMShowWebImagesMain
@{
    ViewBag.ModuleName = "Work Sheet Web Viewer";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss{
    @Styles.Render("~/Areas/Medications/Content/css")
    <link href="~/Areas/Measurements/Content/webviewer.css" rel="stylesheet" />
}

@section patientinfo{
    @(await Html.PartialAsync("_PatientInfoMenu", (object)new Cerebrum.ViewModels.Patient.VMPatientInfoRequest { PatientId=Model.PatientId}))
}

@section metatags{
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
}

<div id="web-viewer-container">

    <div class="row">
        <div class="col-xs-12 col-lg-8">

            <label class="lbl-top-msg"></label><br />
            <div id="url" data-patient-id="@Model.URL"></div>
            <div id="name" data-patient-id="@Model.WebImagesStr"></div>
            <div id="token" data-patient-id="@Model.Token"></div>

            <a data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="" data-show-header="off" class="btn btn-default btn-sm" id="btn-patient-info-header-bar" href="#"><span id="show-header-msg"></span></a>
            <input type="text" id="imstatus" value="" readonly style="width: 80px">

            <input type="button" class="btn btn-default btn-sm" id="Crop" value="Zoom">
            <input type="button" class="btn btn-default btn-sm" id="Cine" value="Cine">
            <input type="button" class="btn btn-default btn-sm" id="MG" value="MG">
            <input type="button" class="btn btn-default btn-sm" id="Pan" value="Pan">
            <input type="button" class="btn btn-default btn-sm" id="Reset" value="Reset">
            <input type="button" class="btn btn-default btn-sm" id="Measur" value="Measur">
            <input type="button" class="btn btn-default btn-sm" id="Area" value="Area">
            <input type="text" id="examstatus" value="..." style="width: 80px" readonly>
            <input type="button" class="btn btn-default btn-sm" id="Prev" value="<">
            <input type="button" class="btn btn-default btn-sm" id="Next" value=">">
            <input type="button" class="btn btn-default btn-sm" id="Thumb" value="[]">
            <input type="button" class="btn btn-default btn-sm" id="x1" value="1">
            <input type="button" class="btn btn-default btn-sm" id="x2" value="2">
            <input type="text" id="fdelay" value="..." readonly style="width: 60px">
            <input type="button" class="btn btn-default btn-sm" id="about" value="About">

            <input type="text" id="msstatus" value="..." readonly style="width: 160px">

            <div>
                <canvas id="viewer"></canvas>
                <canvas id="viewer1"></canvas><br />
                <canvas id="viewer2"></canvas>
                <canvas id="viewer3"></canvas><br />
                <input type="hidden" id="ctrl_currentSlide" />

                <input type="hidden" id="ctrl_measurementItemToAction" />
                <input type="hidden" id="ctrl_measurementAction" />
                <input type="button" id="btn_action" value="___check___" style="display: none;" />
            </div>

        </div>

        <div class="col-xs-12 col-lg-4">
            <!-- col2 -->
            <div id="pnl_mainHeader">
                <ul class="ul-header">
                    <li class="lbl-xs"><input type="checkbox" id="chk_all" checked="checked" /></li>
                    <li class="lbl-xs">ID</li>
                    <li class="lbl">Value(s)</li>
                    <li class="lbl-sm">Unit(s)</li>
                    <li class="lbl-sm">Frame</li>
                    <li class="lbl-sm">Region</li>
                    <li class="lbl-xs"></li>
                    <li class="lbl-xs"></li>
                </ul>
            </div>
            <div id="pnl_main"></div>
        </div>
        <!-- /col2 -->
    </div>

</div>
<script>
        var topMsg = $('.lbl-top-msg').text();
        var msg = 'Browser does not support \'Canvas\'. Please update your browser';
        if (Modernizr.canvas) {
            msg = 'Browser supports \'Canvas\'.';        }

        topMsg += '. '+msg;
        $('.lbl-top-msg').text(topMsg);

        $(function () {

            toggleHeaderVisibility('on');

            $(document).on('click', '#btn-patient-info-header-bar', function (e) {
                e.preventDefault();
                var btn = $(this);
                var status = btn.data('show-header');

                toggleHeaderVisibility(status);
            });
        });

        function toggleHeaderVisibility(status)
        {

            if(status != null)
            {
                var btn = $('#btn-patient-info-header-bar');
                var textHolder = $('#show-header-msg');
                if(status == 'on')
                {
                    $('#main-nav').hide();
                    $('#patient-info-container').hide();
                    $('body').css('padding-top', '0px');
                    btn.data('show-header', 'off');
                    //btn.data('cb-tp-title', 'Show patient information');
                    textHolder.html('Show Header');
                }
                else if (status == 'off') {

                    $('body').css('padding-top', '50px');
                    $('#main-nav').show();
                    $('#patient-info-container').show();
                    btn.data('show-header', 'on');
                   // btn.data('cb-tp-title', 'Hide patient information');
                    textHolder.html('Hide Header');

                    var totalZ = $('#patient-info-container .__patient-top-pnl .col-lg-9 .Z').length;
                    var patInfoWidth = $('#patient-info-container .__patient-top-pnl .col-lg-9').width();

                    var maxWidth = patInfoWidth / totalZ;

                    $('.Z').width(maxWidth);


                }
            }


        }
</script>

@if (!String.IsNullOrWhiteSpace(Model.WebImagesStr))
{
<script src="~/Areas/Measurements/Scripts/js-webviewer/Measurement.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/region.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/vstate.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/imageLoader.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/loader.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/render.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/sprite.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/JSLINQ.min.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/reader.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/browse.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/tools.js"></script>
<script src="~/Areas/Measurements/Scripts/js-webviewer/vw.js"></script>

}




