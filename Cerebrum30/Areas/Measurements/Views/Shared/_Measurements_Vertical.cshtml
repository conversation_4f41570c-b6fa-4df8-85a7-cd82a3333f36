@*
    notes notes notes notes notes:
    notes notes notes notes notes:
        this file is being "minified" to reduce the download file size
        otherwise, in some cases the file size could be more than 3MB
        so now this file is very hard to read - in Visual Studio, you can select-all, then right-click, then click "Format Selection"
        however, remember to shift-tab everything back to left to remove all indents when you are done (select-all, then shift-tab many times)
        we do not want to install or add any 3rd part tools at the top to manage this
        also, although gzip might be another option, but at the time I am typing this, our servers having ongoing cpu/memory issues, gzip will take cpu so it's not the best for us now
*@
@model Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
<style>
tooltip-arrow,
.red-tooltip + .tooltip > .tooltip-inner {
background-color: #f00;
}

.tippytrip {
cursor: pointer;
/*margin-bottom: 40px;
width: 80px;
border: 2px solid #ccc;
padding: 5px;*/
}

#tooltip-container {
position: absolute;
padding: 5px;
background-color: lightgray;
color: black;
display: none;
z-index: 999;
}


#div-meas li {
/*font-size: 11px !important;*/
}
</style>
<script>
//$(".measTxtBox ").val("999");
$(document).ready(function () {
$('.tippytrip').hover(function () {
var url = $(this).data("url");
var offset = $(this).offset();
var width = $(this).outerWidth();

$('#tooltip-container')
.empty()
.css({ top: offset.top, left: offset.left + width + 10 })
.load(url).show();
}, function () {
$('#tooltip-container').hide();
});

//$('.hoveroverbox').hover(function () {
//    var self = $(this);
//    var title = $(this).attr("title");
//    if (undefined === title || 'error' === title) {
//        var url = $(this).attr("url");
//        self.attr('title', 'loading...');
//        $.ajax({
//            method: "GET",
//            url: url,
//            success: function (data) {
//              var mssg = data.Message;
//              if (data.Errored == "1") {
//                  self.attr('title', 'error');
//                  self.attr('data-original-title', 'error');
//                  showNotificationMessage('error', 'Range could not be retrieved');
//              }
//              else {
//                  self.attr('title', mssg);
//                  self.attr('data-original-title', mssg);
//                  self.tooltip().mouseout();
//                  self.tooltip().mouseover();
//              }
//          },
//          error: function (xhr, thrownError) {
//              self.attr('title', 'error');
//              self.attr('data-original-title', 'error');
//              console.log("Error while tryng to retrieve ranges for measurement " + xhr.status + " " + thrownError);
//          },
//          complete: function () {
//              if ('loading...' === tb.attr('title')) {
//                  self.attr('title', undefined);
//                  self.attr('data-original-title', undefined);
//              }
//          }
//        });
//    }
//});
$('[data-toggle="tooltip"]').tooltip({ animation: false });
});

</script>
@*@Html.HiddenFor(m  => (object)m.TestID)
@Html.HiddenFor(m  => (object)m.AppointmentID)
@Html.HiddenFor(m  => (object)m.AppointmentTestID)
@Html.HiddenFor(m  => (object)m.PracticeID)*@
@*<div class="range-div "></div>*@
<div id="tooltip-container"></div>
<div class="pre-scrollable" id="divMeasurement">
<table cellpadding="0" class="table tbl-meas89"><!--  .table-striped > tbody > tr:nth-of-type(2n+1) -->
@for (int z = 0; z < Model.MeasurementCategories.Count; z++)
{
@Html.Hidden("MeasurementCategories[" + z + "].Id", Model.MeasurementCategories[" + z + "].Id)
@Html.Hidden("MeasurementCategories[" + z + "].name", Model.MeasurementCategories[z].name)
@Html.Hidden("MeasurementCategories[" + z + "].categoryCode", Model.MeasurementCategories[" + z + "].categoryCode)
<tr>
<td>
<!-- @*label label-primary label-app-type 77865*@ -->
<span id="<EMAIL>[z].Id" class="spacer-top-4 label label-primary customLbl">@Model.MeasurementCategories[z].name</span>
<div>
@*<ol class="meas-list" style="margin:2px 0px 0px 0px; padding:0px;">*@
<ol class="meas-list" style="margin:0px 0px 0px 0px; padding:0px;">
@for (int i = 0; i < Model.MeasurementCategories[z].Measurements.Count; i++)
{
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].Id", Model.MeasurementCategories[z].Measurements[i].Id)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].name", Model.MeasurementCategories[z].Measurements[i].name)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].categoryCode", Model.MeasurementCategories[z].Measurements[i].categoryCode)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].measurementCode", Model.MeasurementCategories[z].Measurements[i].measurementCode)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].calculateBSA", Model.MeasurementCategories[z].Measurements[i].calculateBSA)
if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 0)
{
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].MeasurementSavedValues[0].MeasurementId", Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementId)
}
<li class="__col-sm-1 was2" style="border: 1px solid #e2e2e2; display: inline; float: left">
<div>
<div>
@{
int measurmentCode = @Model.MeasurementCategories[z].Measurements[i].Id;
string measurementNameCode = @Model.MeasurementCategories[z].Measurements[i].name + " - " + measurmentCode;
}
<div style="display: block">
<div class="hideOverflowMeasExtraWidth extraSmall" style="" data-toggle="tooltip" data-placement="bottom" title="@measurementNameCode">@measurementNameCode</div>
<a data-toggle="tooltip" data-placement="top" class="hideOverflowMeasExtraWidth extraSmall" title="@Model.MeasurementCategories[z].Measurements[i].units">(@Model.MeasurementCategories[z].Measurements[i].units)</a>
</div>
<div class="form-inline _23" style="float: left;padding-bottom:0px; /*background: #ff8877 ;*/ width:100%">
@*<div class="form-group">*@ 
<div>
@*if null selected in Operator List, select MAX and assign max property yo value*@
@if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 1 &&
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId == 1
)
{
var tempValue = Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value;
decimal result;
if (!Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Any((System.Func<dynamic, bool>)(x => decimal.TryParse(x.Value, out result)) == false))
{
var newMax = Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues
                            .OrderByDescending(x => decimal.Parse(x.Value))
                            .FirstOrDefault();
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value = newMax.Value;
newMax.Value = tempValue;
}
}

@Html.TextBoxFor((System.Func<dynamic, object>)(x => (object)x.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value), new
{
@measCode = @Model.MeasurementCategories[z].Measurements[i].measurementCode,
@catCode = @Model.MeasurementCategories[z].categoryCode,
@onblur = "GetRange(this)",
@id = "txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@class = " hoveroverbox measTxtBox  " + @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].RangeClass,
@Value = @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value,
//@history = @Url.Action("Measurement", "Measurement", new { measurementID = Model.MeasurementCategories[z].Measurements[i].Id, appointmentID = Model.AppointmentID, testID = Model.TestID }),
//@ondblclick = "GetMeasHistory(this)",
//@url = Url.Action("GetRangeToolTip", "Measurement",
//   new
//   {
//       mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
//       catCode = @Model.MeasurementCategories[z].categoryCode,
//       appointmentID = Model.AppointmentID
//   }),
//data_meashistory_url= Url.Action("MeasurementHistory", "Measurement", new { measurementID = Model.MeasurementCategories[z].Measurements[i].Id, appointmentID = Model.AppointmentID, testID = Model.TestID })
@url = Url.Action("GetRangeToolTip", "Measurement",
new
{
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
}
)

})

@* // show Range tooltip on the textbox when clicking, JS is in ws.js click '.getrange' *@
<span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Click to view Range" data-getrange-textbox-id="txtVal_@(Model.MeasurementCategories[z].Id)_@(Model.MeasurementCategories[z].Measurements[i].Id)" class="glyphicon glyphicon-registration-mark getrange c-pointer"></span>
@*2nd txt*@
@if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != null 
&& Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != AwareMD.Cerebrum.Shared.Enums.MeasurementRanges.OutOfRange
)
{
@Html.TextBox("txtIndex"
, (!string.IsNullOrEmpty(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value) ? (Math.Round(decimal.Parse(Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value) / Model.BSA, 2)).ToString() : string.Empty)
, new {
@id = "txtIndex_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@class = "555 hoveroverbox measTxtBox " + @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].BSARangeClass,
@readonly = "readonly",
@url = Url.Action("GetBSARangeToolTip", "Measurement",
new
{
mid = Model.MeasurementCategories[z].Measurements[i].Id,
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
})
})

@* // show Range tooltip on the textbox when clicking, JS is in ws.js click '.getrange' *@
<span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Click to view Range" data-getrange-textbox-id="txtIndex_@(Model.MeasurementCategories[z].Id)_@(Model.MeasurementCategories[z].Measurements[i].Id)" class="glyphicon glyphicon-registration-mark getrange c-pointer"></span>
}
@*end of 2nd*@
@{
var measurementItem = Model.MeasurementCategories[z].Measurements[i];
var measurementName = measurementItem.name;
var measurementId = measurementItem.Id;
}
<span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Double click to view Measurement History" data-meashistory-url="@Url.Action("MeasurementHistory", "Measurement", new { measurementID = measurementId, appointmentID = Model.AppointmentID, testID = Model.TestID })" class="glyphicon glyphicon-info-sign meashistory c-pointer"></span>
<span data-toggle="tooltip" title="Discard all values on Merge" data-placement="top" class="chk-discard">@Html.CheckBoxFor(x  => x.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Discard)</span>
@*<i data-toggle="tooltip"
data-placement="right"
title="Ranges"
tooltip="Ranges"
class="glyphicon glyphicon-info-sign c-pointer"
data-url='@Url.Action("GetRanges", "Measurement",
new {
mid = Model.MeasurementCategories[z].Measurements[i].Id,
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
})'
id="icon-range">
</i>*@
@*<a data-toggle="tooltip"
data-placement="top"
class="hideOverflowUnits"
title="@Model.MeasurementCategories[z].Measurements[i].units">(@Model.MeasurementCategories[z].Measurements[i].units)</a>*@
</div>
</div>
@*@if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != null &&
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != AwareMD.Cerebrum.Shared.Enums.MeasurementRanges.OutOfRange
){
@Html.TextBox("txtIndex", (object)(object)(!string.IsNullOrEmpty(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value) ? (Math.Round(decimal.Parse(Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value)/ Model.BSA, 2)).ToString() : string.Empty), new {@class = "555 hoveroverbox measTxtBox " + @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].BSARangeClass, @readonly = "readonly", @url = Url.Action("GetBSARangeToolTip", "Measurement",
new {
mid = Model.MeasurementCategories[z].Measurements[i].Id,
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
})
})
}*@

@if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId != 1)
{
@Html.DropDownList("ddOpear", new SelectList(Model.MeasurementOperators, "Value", "Text", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId), new { @class = "dropdown ddl-max", @disabled = "disabled" })
}

@if (@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 1)
{
@Html.DropDownList("ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
new SelectList(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues,
"Value", "Value", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value),
new
{
data_toggle = "MultiValueDropDownSelect",
data_target = "#txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@class = "dropdown",
@id = "ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id
})
if (

Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId == 1
)
{
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId = 2;
}


@Html.DropDownListFor((System.Func<dynamic, object>)(m => m.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId),
new SelectList(Model.MeasurementOperators, "Value", "Text", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId),
new
{
@class = "dropdown ddOperator _mm86",
@id = "ddOper_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@onchange = "OpeartorChanged(this,"
+ "'txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id + "',"
+ "'ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id + "')"
})

}
</div>
</div>
</li>
}
</ol>
</div>
</td>
</tr>
}
</table>
</div>

<script>
$(document).ready(function () {

//$('.ddl-max, .chk-discard').hide();


$('.hideOverflowUnits').each(function () {
if ($(this).html() == '()') {// no unit - hide
$(this).html('');
}
});


});
</script>