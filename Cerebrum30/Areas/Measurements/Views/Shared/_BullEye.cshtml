@model Cerebrum30.Areas.Measurements.Models.ViewModels.BullEyeResponse
@using Cerebrum30.Helpers;

<script type="text/javascript">
    var svgAppointmentId = @Model.appointmentId;
    var svgTestId = @Model.testId;
    var selectedSegments = [];  // group id; segment id; segment color id
    @if (string.IsNullOrEmpty(Model.errorMessage) && Model.segments != null) {
        @:selectedSegments = @Html.Raw(Json.Serialize(Model.segments));
    }

    function saveImageClicked() {
        var svgData = new XMLSerializer().serializeToString(document.getElementById("svgContainer"));
        var data = { appointmentId: svgAppointmentId, testId: svgTestId, segments: selectedSegments, svgData: svgData };
        var url = "Measurements/Measurement/SaveSvgImage";
        $.ajax({
            method: "POST",
            url: url,
            data: data,
            async: true,
            success: function (result) {
                if (result != "") {
                    showMessageModal("error", result, false);
                }
            },
            error: function (xhr, thrownError) {
                checkAjaxError(url, xhr, thrownError);
            }
        });
    }
</script>

@Html.ModalHeader("Bull's Eye for patient: " + Model.patientName)
<div class="clearfix"></div>
<div class="col-xs-12 text-center">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        int isNewBullEye = 0;
        if (Model.segments == null)
        {
            isNewBullEye = 1;
        }
        @(await Html.PartialAsync("~/Views/Shared/_SegmentPartial.cshtml", (object)new ViewDataDictionary { { "svgNumber", Model.svgNumber }, { "isNewBullEye", isNewBullEye } }))
    }
    else
    {
        <span style="font-size: 20px; color: red;"><br /><br /><br /><br /><br /><br />@Model.errorMessage<br /><br /><br /><br /></span>
    }
</div>
<div class="clearfix"></div>
<div class="modal-footer">
    @*<div class="col-xs-6">
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal" onclick="saveImageClicked();" style="display: @(string.IsNullOrEmpty(Model.errorMessage) ? "": "none");">Save As Image</button>
    </div>*@
    <div class="col-xs-12" style="float:right">
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal" onclick="saveImageClicked();" style="display: @(string.IsNullOrEmpty(Model.errorMessage) ? "": "none");">Save As Image</button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
    </div>
</div>