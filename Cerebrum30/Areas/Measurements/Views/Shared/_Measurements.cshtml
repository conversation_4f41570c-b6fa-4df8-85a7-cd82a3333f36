@*
    notes notes notes notes notes:
    notes notes notes notes notes:
        this file is being "minified" to reduce the download file size
        otherwise, in some cases the file size could be more than 3MB
        so now this file is very hard to read - in Visual Studio, you can select-all, then right-click, then click "Format Selection"
        however, remember to shift-tab everything back to left to remove all indents when you are done (select-all, then shift-tab many times)
        we do not want to install or add any 3rd part tools at the top to manage this
        also, although gzip might be another option, but at the time I am typing this, our servers having ongoing cpu/memory issues, gzip will take cpu so it's not the best for us now
*@

@model Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
<style>
tooltip-arrow,
.red-tooltip + .tooltip > .tooltip-inner {
background-color: #f00;
}

.tippytrip {
cursor: pointer
/*margin-bottom: 40px;
width: 80px;
border: 2px solid #ccc;
padding: 5px;*/
}

#tooltip-container {
position: absolute;
padding: 5px;
background-color: lightgray;
color: black;
display: none;
z-index:999;
}
   

</style>
<script>
$(document).ready(function () {
$('.tippytrip').hover(function () {

var url = $(this).data("url");
var offset = $(this).offset();
var width = $(this).outerWidth();

$('#tooltip-container')
.empty()
.css({ top: offset.top, left: offset.left + width + 10 })
.load(url).show();
}, function () {
$('#tooltip-container').hide();
});

$(document).on('hover', '.hoveroverbox', function () {
});

$('[data-toggle="tooltip"]').tooltip({ animation: false });

});



</script>
@*@Html.HiddenFor(m  => (object)m.TestID)
@Html.HiddenFor(m  => (object)m.AppointmentID)
@Html.HiddenFor(m  => (object)m.AppointmentTestID)
@Html.HiddenFor(m  => (object)m.PracticeID)*@
<div class="range-div "></div>
<div id="tooltip-container"></div>
<div class="pre-scrollable" id="divMeasurement">
<table cellpadding="0" class="table table-striped table-condensed table-bordered">
@for (int z = 0; z < Model.MeasurementCategories.Count; z++)
{
@Html.Hidden("MeasurementCategories[" + z + "].Id", Model.MeasurementCategories[" + z + "].Id)
@Html.Hidden("MeasurementCategories[" + z + "].name", Model.MeasurementCategories[z].name)
@Html.Hidden("MeasurementCategories[" + z + "].categoryCode", Model.MeasurementCategories[" + z + "].categoryCode)
@*//take this off, dont need this
@Html.Hidden("MeasurementCategories[" + z + "].par", Model.MeasurementCategories[z].par)
@Html.Hidden("MeasurementCategories[" + z + "].order", Model.MeasurementCategories[" + z + "].order)
@Html.Hidden("MeasurementCategories[" + z + "].status", Model.MeasurementCategories[z].status)
@Html.Hidden("MeasurementCategories[" + z + "].dateAdded", Model.MeasurementCategories[" + z + "].dateAdded)*@
<tr>
<td>
<p>
<span id="<EMAIL>[z].Id" class="label label-primary label-app-type">@Model.MeasurementCategories[z].name</span>
</p>
<div>
<ol class="meas-list" style="margin:0;padding:0;">
@for (int i = 0; i < Model.MeasurementCategories[z].Measurements.Count; i++)
{
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].Id", Model.MeasurementCategories[z].Measurements[i].Id)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].name", Model.MeasurementCategories[z].Measurements[i].name)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].categoryCode", Model.MeasurementCategories[z].Measurements[i].categoryCode)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].measurementCode", Model.MeasurementCategories[z].Measurements[i].measurementCode)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].calculateBSA", Model.MeasurementCategories[z].Measurements[i].calculateBSA)
//take this off, dont ned it , only for multiple value demo
@*
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].order", Model.MeasurementCategories[z].Measurements[i].order)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].units", Model.MeasurementCategories[z].Measurements[i].units)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].status", Model.MeasurementCategories[z].Measurements[i].status)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].mask", Model.MeasurementCategories[z].Measurements[i].mask)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].dateAdded", Model.MeasurementCategories[z].Measurements[i].dateAdded)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].isCompulsory", Model.MeasurementCategories[z].Measurements[i].isCompulsory)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].visibleOnWorkSheet", Model.MeasurementCategories[z].Measurements[i].visibleOnWorkSheet)
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].VisibleByPractice", Model.MeasurementCategories[z].Measurements[i].VisibleByPractice)*@
if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 0)
{
@Html.Hidden("MeasurementCategories[" + z + "].Measurements[" + i + "].MeasurementSavedValues[0].MeasurementId", Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementId)
}

<li class="col-sm-1">
<div class="">
<div>
@{ 
int measurmentCode = @Model.MeasurementCategories[z].Measurements[i].Id;
string measurementNameCode = @Model.MeasurementCategories[z].Measurements[i].name+" - "+measurmentCode;
}
<span class="hideOverflowMeasExtraWidthS extraSmall "
data-toggle="tooltip"
data-placement="right"
tooltip="@measurementNameCode"
title="@measurementNameCode">
@measurementNameCode
</span>
<div class="form-inline">
<div class="form-group">

@*if null selected in Operator List, select MAX and assign max property yo value*@
@if (
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 1 &&
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId == 1
)
{

var tempValue = Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value;
decimal result;
if (!Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Any((System.Func<dynamic, bool>)(x => decimal.TryParse(x.Value, out result)) == false))
{
var newMax = Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues
            .OrderByDescending(x => decimal.Parse(x.Value))
            .FirstOrDefault();
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value = newMax.Value;
newMax.Value = tempValue;
}
}

@Html.TextBoxFor((System.Func<dynamic, object>)(x => (object)x.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value),
new
{

@measCode = @Model.MeasurementCategories[z].Measurements[i].measurementCode,
@catCode = @Model.MeasurementCategories[z].categoryCode,
@onblur = "GetRange(this)",
@id = "txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@class = " hoveroverbox measTxtBox  " + @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].RangeClass,
@Value = @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value,
//@history = @Url.Action("MeasurementHistory", "Measurement", new { measurementID = Model.MeasurementCategories[z].Measurements[i].Id, appointmentID = Model.AppointmentID, testID = Model.TestID }),

//@ondblclick = "GetMeasHistory(this)",
//@data_toggle = "tooltip",
//@data_placement = "right",
//@tooltip = @Model.MeasurementCategories[z].Measurements[i].name,
//@title= @Model.MeasurementCategories[z].Measurements[i].RangeStr,
//@onblur = "GetRange(this,'" + @Model.MeasurementCategories[z].Measurements[i].measurementCode + "','" + @Model.MeasurementCategories[z].categoryCode + "')"
//@url = Url.Action("GetRangeToolTip", "Measurement",
//   new
//   {
//       mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
//       catCode = @Model.MeasurementCategories[z].categoryCode,
//       appointmentID = Model.AppointmentID
//   }),
//data_meashistory_url= Url.Action("MeasurementHistory", "Measurement", new { measurementID = Model.MeasurementCategories[z].Measurements[i].Id, appointmentID = Model.AppointmentID, testID = Model.TestID })
@url = Url.Action("GetRangeToolTip", "Measurement",
new
{
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
}
)

})
@{ 
var measurementItem = Model.MeasurementCategories[z].Measurements[i];
var measurementName = measurementItem.name;
var measurementId = measurementItem.Id;
}
                                                
@* // show Range tooltip on the textbox when clicking, JS is in ws.js click '.getrange' *@
<span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Click to view Range" data-getrange-textbox-id="txtVal_@(Model.MeasurementCategories[z].Id)_@(Model.MeasurementCategories[z].Measurements[i].Id)" class="glyphicon glyphicon-registration-mark getrange c-pointer"></span>
<span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Double click to view Measurement History" data-meashistory-url="@Url.Action("MeasurementHistory", "Measurement", new { measurementID = measurementId, appointmentID = Model.AppointmentID, testID = Model.TestID })" class="glyphicon glyphicon-info-sign meashistory c-pointer"></span>
                                               
<span data-toggle="tooltip"
title="Discard on Merge"
data-placement="right">
@Html.CheckBoxFor(x  => x.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Discard)
</span>

<span>
@*<a class="icon-color"
data-toggle="tooltip"
data-placement="right"
title="History"
id="btn-meas-history" target="_blank" href=# data-url='@Url.Action("History", "Measurement", new { measurementID = Model.MeasurementCategories[z].Measurements[i].Id, appointmentID = Model.AppointmentID, testID = Model.TestID })'><i class="glyphicon glyphicon-flash btn-apptest-status c-pointer"></i></a>*@
@*<i data-toggle="tooltip"
data-placement="right"
tooltip="Ranges"
title="Ranges"
class="glyphicon glyphicon-info-sign c-pointer"
data-url='@Url.Action("GetRanges", "Measurement",
new {
mid = Model.MeasurementCategories[z].Measurements[i].Id,
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
})'
id="icon-range">
</i>*@
@if (!string.IsNullOrEmpty(Model.MeasurementCategories[z].Measurements[i].units))
{
<a data-toggle="tooltip"
data-placement="right"
class="hideOverflowUnits" title="@Model.MeasurementCategories[z].Measurements[i].units">(@Model.MeasurementCategories[z].Measurements[i].units)</a>
}
</span>
</div>
</div>

@if (
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != null &&
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != AwareMD.Cerebrum.Shared.Enums.MeasurementRanges.OutOfRange
)
{

@Html.TextBox("txtIndex", (object)(object)(!string.IsNullOrEmpty(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value)
? (Math.Round(decimal.Parse(Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value) / Model.BSA, 2)).ToString()
: string.Empty),

new
{
@class = "hoveroverbox  " + @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].BSARangeClass,
@readonly = "readonly",
@style = "width:85px;background-color:pink",
@url = Url.Action("GetBSARangeToolTip", "Measurement",

new
{
mid = Model.MeasurementCategories[z].Measurements[i].Id,
mesCode = Model.MeasurementCategories[z].Measurements[i].measurementCode,
catCode = @Model.MeasurementCategories[z].categoryCode,
appointmentID = Model.AppointmentID
})
})


}
@if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId != 1)
{
@Html.DropDownList("ddOpear", new SelectList(Model.MeasurementOperators, "Value", "Text", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId), new { @class = "dropdown", @disabled = "disabled" })
}
@if (@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues.Count > 1)
{

@Html.DropDownList("ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
new SelectList(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues,
"Value", "Value", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].Value),
new { data_toggle="MultiValueDropDownSelect",data_target= "txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@class = "dropdown", @id = "ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id })


if (Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId == 1)
{
Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId = 2;
}

<br />

@Html.DropDownListFor((System.Func<dynamic, object>)(m => m.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId),
new SelectList(Model.MeasurementOperators, "Value", "Text", @Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId),
new
{
@class = "dropdown ddOperator",
@id = "ddOper_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id,
@onchange = "OpeartorChanged(this,"
+ "'txtVal_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id + "',"
+ "'ddMulti_" + @Model.MeasurementCategories[z].Id + "_" + @Model.MeasurementCategories[z].Measurements[i].Id + "')"
})

}
</div>
</div>
<div>
@if (!string.IsNullOrEmpty(@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].ErrorMessage))
{
<span id="<EMAIL>[z].Measurements[i].MeasurementSavedValues[0].MeasurementId" class="label label-danger">@Model.MeasurementCategories[z].Measurements[i].MeasurementSavedValues[0].ErrorMessage </span>
}
</div>
</li>
}
</ol>
</div>
</td>
</tr>
}
</table>
</div>
