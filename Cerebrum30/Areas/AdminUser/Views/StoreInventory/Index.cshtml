@model Cerebrum.ViewModels.Inventory.VMInventoryMain
@{
    ViewBag.Title = "Inventory";
    ViewBag.ModuleName = "Inventory";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss{
    
}

@section scripts{
    <script src="~/Areas/AdminUser/Scripts/inventory.js"></script>
}

@*For the inventory search*@
<div style="padding-top:15px;" id="inventory-search">
    @using (Html.BeginForm("index", "storeinventory", new { area = "adminuser" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-inventory-search" }))
    {        
        <div class="form-group form-group-sm">
            <label class="control-label" for="OfficeId">Office</label>
            @Html.DropDownList("OfficeId", new SelectList(Model.Offices, "Value", "Text", Model.Request.OfficeId), new { @class = "form-control" })
        </div>

        <div class="form-group form-group-sm">
            <label class="control-label" for="StatusId">Status</label>
            @Html.DropDownList("StatusId", new SelectList(Model.Statuses, "Value", "Text", Model.Request.StatusId), new { @class = "form-control" })
        </div>

        <div class="form-group form-group-sm">
            <label class="control-label" for="DeviceTypeId">Device Type</label>
            @Html.DropDownList("DeviceTypeId", new SelectList(Model.DeviceTypes, "Value", "Text", Model.Request.DeviceTypeId), new { @class = "form-control" })
        </div>

        <div class="form-group form-group-sm">
            <label class="control-label" for="DeviceNumber">Device Number</label>
            @Html.TextBox("DeviceNumber", Model.Request.DeviceNumber, new { @class = "form-control" })
        </div>

        <button id="btn-search-inventory" type="submit" style="margin-right:5px;" class="btn btn-default btn-xs">Search</button>
        <button id="btn-add-inventory" data-url="@Url.Action("CreateInventoryItem","StoreInventory",new { area="adminuser" })" type="button" style="margin-right:5px;" class="btn btn-default btn-xs">Add New Item</button>
        <a id="btn-show-overdue-devices" class="btn btn-default btn-xs" data-office-id="@Model.Request.OfficeId" data-selected-date="@System.DateTime.Now.ToShortDateString()" data-url="@Url.Action("GetOverDueDevices", "StoreInventory", new { area = "AdminUser" })"><span class="glyphicon glyphicon-phone default-text-color"> </span>Over Due Devices</a>
        <span id="msg-inventory-load" class="text-primary"></span>      

    }
</div>

<hr />

<div id="inventory-list-container">

    @*Inventory paging*@
    @if (Model.InventoryMainItems.Paging)
    {
        var totalPages = Model.InventoryMainItems.TotalPages;
        var pageNumber = Model.InventoryMainItems.PageNumber;
        var currentPaging = Model.InventoryMainItems.PagingDescription;
        var pagingUrls = (List<Cerebrum.ViewModels.Common.VMLookupItem>)ViewBag.PagingUrls;
        <div class="row">
            <div class="col-md-12">
                <div class="pull-left">
                    <span>@currentPaging</span>
                </div>
                <div style="padding-top:3px" class="pull-right">
                    <span> of </span><span>@totalPages</span>
                </div>
                <div class="pull-right">
                    <div class="dropdown pull-right">
                        <button class="btn btn-default dropdown-toggle btn-xs" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            Page @pageNumber
                            <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            @for (int i = 1; i <= totalPages; i++)
                        {
                            var active = i == pageNumber ? "active" : "";
                            var officeId = Model.Request.OfficeId;
                            var statusId = Model.Request.StatusId;
                            var deviceTypeId = Model.Request.DeviceTypeId;
                            var pagingUrl = Url.Action("index", "StoreInventory",
                                new { area = "adminuser", OfficeId = officeId, StatusId = statusId, deviceTypeId, Page = i });
                                <li class="@active"><a href="@pagingUrl">Page @i</a></li>
                        }
                        </ul>
                    </div>
                </div>                 
            </div>
        </div>

    }

    @*Inventory Items*@
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">Inventory Items <span class="badge cbadge">@Model.InventoryMainItems.TotalItems</span></h3>
        </div>
        <table class="table table-condensed table-bordered table-hover">
            <tr>
                <th>
                    Office
                </th>
                <th>
                    Device Number
                </th>

                <th>
                    Device Type
                </th>

                <th>
                    Status
                </th>
                <th>
                    History
                </th>
                <th>
                    Notes
                </th>

                <th>
                    Date Created
                </th>
                <th></th>
            </tr>

            @foreach (var item in Model.InventoryMainItems.InventoryItems)
        {
        <tr id="<EMAIL>">
            <td>
                @item.OfficeName
            </td>
            <td>
                @item.DeviceNumber
            </td>

            <td>
                @item.DeviceType
            </td>
            <td>
                @item.StatusType
            </td>
            <td>
                @if (item.HistoryCount > 0)
                    {
                    <a class="btn-inventory-item-history" data-inventory-id="@item.InventoryId"
                       data-device-number="@item.DeviceNumber"
                       data-url="@Url.Action("GetInventoryItemHistory","storeinventory", new { area="adminuser" })"
                       href="#">View History (@item.HistoryCount)</a>
                    }

            </td>
            <td>
                @item.Notes
            </td>
            <td>
                @item.DateCreated
            </td>
            <td>
                <button class="btn btn-primary btn-xs btn-inventory-item-edit" data-inventory-id="@item.InventoryId" href="#">Edit</button>
                <button class="btn btn-primary btn-xs btn-inventory-item-delete" data-inventory-id="@item.InventoryId" data-confirm-msg="Delete Item @item.DeviceNumber?" href="#">Delete</button>
            </td>
        </tr>
        }

        </table>
        
    </div>

</div>

