@model IEnumerable<Cerebrum.ViewModels.Inventory.VMInventoryOverDue>

@{ 
    var officeId = (int)ViewBag.OfficeId;
    var selectedDate = (DateTime)ViewBag.SelectedDate;
}

<script type="text/javascript">
    $(function () {
        resetOverDueButton();
    });

    function resetOverDueButton()
    {
        if ($("#btn-show-overdue-devices").length) {
            var btn = $('#btn-show-overdue-devices');            
            btn.data('office-id','@officeId');
            btn.data('selected-date','@selectedDate.ToShortDateString()');            
        }
    }

</script>

@*For the Over Due search*@
<div style="padding-top:10px;padding-bottom:15px;" id="div-overdue-search">
    @using (Html.BeginForm("GetOverDueDevices", "storeinventory", new { area = "adminuser" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-overdue-search" }))
    {
        <div class="form-group form-group-sm">
            <label class="control-label" for="officeId">Office</label>
            @Html.DropDownList("officeId", new SelectList(ViewBag.Offices, "Value", "Text", officeId),"Choose One", new { @class = "form-control" })
        </div>        

        <div class="form-group form-group-sm">
            <label class="control-label" for="selectedDate">Due Date</label>
            @Html.TextBox("selectedDate", (object)(object)selectedDate.ToShortDateString(), new { @class = "form-control date-picker" })
        </div>

        <button id="btn-search-overdue" type="button" style="margin-right:5px;" class="btn btn-primary btn-xs">Search</button>        

    }
</div>

<div class="panel panel-info content-height300">
    <div class="panel-heading">
        <h3 class="panel-title">Over Due <span class="badge cbadge">@Model.Count()</span></h3>
    </div>
    <table class="table table-condensed table-hover table-bordered">
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.OfficeName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PatientFullName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DeviceNumber)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DeviceType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateStarted)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateExpectedReturn)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AssignedByUser)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Notes)
            </th>
            <th></th>
        </tr>

        @foreach (var item in Model) {
    <tr>
        <td>
            @item.OfficeName
        </td>
        <td>
            @item.PatientFullName
        </td>
        <td>
            @item.DeviceNumber
        </td>
        <td>
            @item.DeviceType
        </td>
        <td>
            @item.DateStarted
        </td>
        <td>
            @item.DateExpectedReturn
        </td>
        <td>
            @item.AssignedByUser
        </td>
        <td>
            @item.Notes
        </td>
        <td>
            <a href="#" data-app-test-id="@item.AppointmentTestId" data-device-number-id="@item.DeviceNumberId" data-patient-equipment-id="@item.PatientEquipmentId" data-confirm-msg="Mark Device returned for @item.PatientFullName?" class="btn btn-primary btn-xs btn-patient-device-returned">Mark as returned</a>
        </td>
    </tr>
}

    </table>
</div>