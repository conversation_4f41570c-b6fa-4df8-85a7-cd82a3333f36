@model List<Cerebrum.ViewModels.AdminUsersUao.VMUserUao>
@using Cerebrum.ViewModels.User;
@{
    ViewBag.Title = "UAO Settings";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    VMUser user = (VMUser)ViewBag.UserInfo;
}

@section scripts{
    <script src="~/Areas/AdminUser/Scripts/admin-user-uao.js"></script>
}

<h3 class="text-center">List of @user.FullNameType UAO</h3>
<div class="col-md-12">
    <input type="button" class="btn btn-primary btn-sm pull-right" id="btn-add-user-uao" value="Add UAO to User" data-user-id="@user.UserId" data-user-uao-id="0" />
    <input type="hidden" id="hdUserId" value="@user.UserId" />
    <br />
    <div id="user-uao-container">
        @(await Html.PartialAsync("_userUaoList", (object)(object)Model))
    </div>
</div>

