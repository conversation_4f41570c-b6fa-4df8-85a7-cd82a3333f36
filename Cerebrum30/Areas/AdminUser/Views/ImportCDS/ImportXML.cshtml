@model Cerebrum30.Areas.AdminUser.Models.CdsImport
@{
    ViewBag.ModuleName = "CDS Import V5";
    ViewBag.Title = "CDS Import V5";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss{
    
}

@section scripts{
    <script src="~/Scripts/cds_js.js"></script>
}

<script>

        $(document).ready(function () {
            $("#close").click(function () { window.close(); });

            $(document).on('click', '.btn-frm-submit-cds', function (e) {
                e.preventDefault();

                var btn = $(this);
                var btnValue = btn.data('submit-type');
                $('#frm-cds-import-data #importdatakey').val(btnValue);

                $('#ajax-loader').show();
                $('#frm-cds-import-data').submit();
            });

            var DATEFORMAT = "mm/dd/yy";           
            $("#AppointmentDateOlderThan").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true });
            $("#LabCollectionDateOlderThan").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true });
            $("#ExternalDocumentDateOlderThan").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true });
        });

</script>
<style>
    body {
        padding-top: 0px !important;
        padding-bottom: 0px !important;
        background-color: rgb(233, 238, 240) !important;
    }

    #mainDiv {
        width: 550px;
        background-color: rgb(200, 205, 207) !important;
        margin-top: 60px;
    }

    .headerS {
        font-size: 21px;
        font-weight: 700;
        background-color: rgb(168, 174, 176);
    }

    .rowsS {
        background-color: rgb(193, 198, 200);
    }

    .green_ {
        color: #4b9e02 !important;
    }

    .wholeWidth input {
        max-width: 500px;
        width: 100%;
    }

    .btnWidth {
        width: 180px;
    }

    .width100 {
        width: 100px;
    }

    .cl_bt a, input, select {
        /*height: 25px !important;*/
        padding-top: 2px !important;
    }

    .inpSt {
        display: inline-block;
        margin-left: 15px !important;
    }

    .top_bottom_marg_0 {
        margin-top: 0px !important;
        margin-bottom: 0px !important;
    }

    .data-import-check-box {
        display: inline;
    }
    .cds-input-text {
        width: 100px;
        border: 1px solid #ccc;
        box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    }
</style>

<div class="" id="mainDiv">
    @using (Html.BeginForm("ImportXML", "ImportCDS",new { area="adminuser"}, FormMethod.Post, true, new { enctype = "multipart/form-data", @id="frm-cds-import-data" }))
    {
        @Html.HiddenFor(model  => (object)model.message)
        @Html.Hidden("importdatakey", "Import Data")
        <div class="row headerS" style="">
            <div class="form-group">
                <div class="col-md-12 text-center" style="">
                    <span>Import Cds Data ( V5 )</span>
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:16px;">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.uploadFileId), new SelectList(Model.listUploadFile, "Value", "Text", Model.uploadFileId), new { @class = "form-control select" })
                </div>
                <div id="pathId" class="col-md-8 wholeWidth cl_bt" style="@(Model.uploadFileId == 0 ? "display:block" : "display:none")">
                    @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.folderPath), new { @class = "form-control", id = "folderPathId" })
                </div>
                <div id="uploadId" class="col-md-6 wholeWidth cl_bt" style="@(Model.uploadFileId == 1 ? "display:block" : "display:none")">
                    <input type="file" id="File" name="File" style="width:280px;" />
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.docBillingNum)
                </div>
                <div class="col-md-4 cl_bt">
                    @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.docBillingNum), new { @class = "form-control", id = "docBillingNumId" })
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.practiceId)
                </div>
                <div class="col-md-4 cl_bt">
                    @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.practiceId), new { @class = "form-control", id = "practiceId__" })
                    @Html.ValidationMessageFor(model => model.practiceId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.officesList)
                </div>
                <div class="col-md-4 cl_bt">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.officeId), Model.officesList, "", new { @class = "form-control selec_" })
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="form-group">
                <div class="col-md-3 cl_bt">
                    <div class="radio">
                        @*<span style="display:inline-block;margin-top:7px;">CDS50: </span> @Html.RadioButtonFor((System.Func<dynamic, object>)(t => t.cds_version), "CDS50", new { @class = "inpSt" })*@
                    </div>
                </div>
                <div class="col-md-3 cl_bt">
                    <div class="radio">
                        @*<span style="display:inline-block;margin-top:7px;">CDS40: </span> @Html.RadioButtonFor((System.Func<dynamic, object>)(t => t.cds_version), "CDS40", new { @class = "inpSt" })*@
                    </div>
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="panel panel-default" style="margin: 8px;">
                <div class="panel-heading" style="background-color: rgb(212,207,202);"><span style="color: Highlight;">Data to be imported</span></div>
                <div class="panel-body">
                    <div class="col-md-12">
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddAppointment), new { @class = "data-import-check-box" }) Appointment
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddLab), new { @class = "data-import-check-box" }) Lab
                        </div>
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddReport), new { @class = "data-import-check-box" }) Report
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddMedication), new { @class = "data-import-check-box" }) Medication
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddRiskFactor), new { @class = "data-import-check-box" }) Risk Factor
                        </div>
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddProblemList), new { @class = "data-import-check-box" }) Problem List
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddPastHealth), new { @class = "data-import-check-box" }) Past Health
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddFamilyHistory), new { @class = "data-import-check-box" }) Family History
                        </div>
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddAlert), new { @class = "data-import-check-box" }) Alert
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddImmunization), new { @class = "data-import-check-box" }) Immunization
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddPersonalHistory), new { @class = "data-import-check-box" }) Personal History
                        </div>
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddClinicalNote), new { @class = "data-import-check-box" }) Clinical Note
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddCareElement), new { @class = "data-import-check-box" }) Care Element
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddAllergie), new { @class = "data-import-check-box" }) Allergy
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-12">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AddOther), new { @class = "data-import-check-box" }) Other (Health Card, Address, Phone, ...)
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-8" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.AppointmentDateCheck), new { @class = "data-import-check-box" }) Appointment after
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.AppointmentDateOlderThan), new { @class = "cds-input-text" })
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-8" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.LabCollectionDateCheck), new { @class = "data-import-check-box" }) lab collection date after
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.LabCollectionDateOlderThan), new { @class = "cds-input-text" })
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-8" style="display: block;">
                            @Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.ExternalDocumentDateCheck), new { @class = "data-import-check-box" }) external document creation date/test date after
                        </div>
                        <div class="col-md-4" style="display: block;">
                            @Html.TextBoxFor((System.Func<dynamic, object>)(model => (object)model.ExternalDocumentDateOlderThan), new { @class = "cds-input-text" })
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row rowsS" style="padding-top:5px;">
            <div class="form-group">
                <div class="col-md-4 cl_bt" style="padding-top:8px;">
                    <button type="button" data-submit-type="Update" value="Update" class="btn btn-default btn-frm-submit-cds">Update</button>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-4 cl_bt text-center">
                    <button type="button" data-submit-type="Import Data" class="btn btn-default btnWidth btn-frm-submit-cds">Import Data</button>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-4 text-center cl_bt">
                    @*<button type="button" id = "close" value="Close" class="btn btn-default">Close</button>*@
                </div>
            </div>
        </div>

        <div class="row rowsS">
            <div class="form-group">
                <div class="col-md-12 text-center" style="min-height:41px;">
                    @Html.LabelFor((System.Func<dynamic, object>)(model => model.message), Model.message, new { @class = "green_" })
                </div>
            </div>
        </div>
    }
</div>

