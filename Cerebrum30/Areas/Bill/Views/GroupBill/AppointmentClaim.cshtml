@model Cerebrum.ViewModels.Bill.AppointmentClaimResponse

<style type="text/css">
    #appointment-invoice-container input, #appointment-invoice-container select, #appointment-invoice-container textarea {
        max-width: 100%;
    }

    .billingStyle {
        height: 432px;
        overflow-y: auto;
        margin-top: 16px;
    }

    @@media screen and (min-width: 768px) {
        #cbAppointmentClaimModal .modal-lg {
            width: 85% !important;
        }
    }
</style>

<script type="text/javascript">
    var appointmentId_Claim = @Model.appointmentId;
    var admissionIds_Claim = "@Model.admissionIds";
    var admissionActionIds_Claim = "@Model.admissionActionIds";
    var billStatuseIdPaid = @Model.billStatuseIdPaid;
    var billStatuseIdMixed = @Model.billStatuseIdMixed;
    var cashPaymentId = @Model.cashPaymentId;
    var appointmentClaimCount = 3;
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        @:appointmentClaimCount = @(Model.items.Count + 3);
            }
</script>
<script type="text/javascript" src="~/Areas/Bill/Scripts/AppointmentClaim.js"></script>

<div class="modal-body __893745">
    <div class="row">
        <div class="col-sm-12" id="appointment-claim-container" style="background-color: white; padding: 0 0 0 0; margin: 0 0 0 0;">
            @if (string.IsNullOrEmpty(Model.errorMessage))
            {
                <div id="claimDiv">
                    <div class="col-sm-12">
                        <br />
                        <div class="col-sm-8" style="margin-left: 0px; padding-left: 0px;">
                            Doctor: @Model.doctorName &nbsp;&nbsp;&nbsp;
                            @(string.IsNullOrEmpty(Model.officeName) ? string.Empty : "Office: " + Model.officeName) &nbsp;&nbsp;&nbsp;
                            Date: @(!string.IsNullOrEmpty(Model.appointmentDate) ? Model.appointmentDate : Model.admissionActionDate) &nbsp;&nbsp;&nbsp;
                        </div>
                        <div class="col-sm-3">
                            @Html.DropDownList("appointmentBillStatusId", new SelectList(Model.billStatuses, "value", "text", Model.appointmentBillStatusId), htmlAttributes: new { @class = "form-control", style = "width: 128px", onchange = "appointmentBillStatusChange();" })
                        </div>
                        <div class="col-sm-1">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        Patient: @Model.patientName &nbsp;&nbsp;&nbsp;
                        Registration #: @Model.healthcardNumber @Model.healthcardVersion &nbsp;&nbsp;&nbsp;
                        Appt: @Model.appointmentTime &nbsp;&nbsp;&nbsp;
                        <hr />
                    </div>
                    @{ string showServiceDate = string.Empty;
                        if (!Model.isMultipleDate)
                        {
                            showServiceDate = "style='display: none;'";
                        }
                        var isTelephoneConsultCodeMapping = false;
                    }
                    <div class="col-sm-12 billingStyle">
                        <table id="billCodeList" name="billCodeList" class="table" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th @showServiceDate>Date</th>
                                    <th>Service</th>
                                    <th>Code</th>
                                    <th>Diagnosis</th>
                                    <th>Fee Claimed</th>
                                    <th>Fee Paid</th>
                                    <th>Number of Services</th>
                                    <th>Billing Group</th>
                                    <th>SLI</th>
                                    <th>@(Model.appointmentId > 0 ? "Master Number" : "Hospital Code")</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Speciality </th>
                                    <th>Manual Review</th>
                                    <th>Bill To</th>
                                    <th>Ref Dr.</th>
                                    <th>MOH ID</th>
                                    <th>Explanatory Code</th>
                                    <th>Error Code</th>
                                    <th>Send Date</th>
                                    <th>Pay Date</th>
                                    <th>Reconcile Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.items.Count; i++)
                                {
                                    var item = Model.items[i];
                                    if (item.serviceCode == "K083A")
                                    {
                                        isTelephoneConsultCodeMapping = true;
                                    }
                                    <tr>
                                        <td @showServiceDate>@item.serviceDate</td>
                                        <td>@item.service</td>
                                        <td>
                                            @Html.Hidden("detailId" + i.ToString(), item.detailId)
                                            @Html.TextBox("serviceCode" + i.ToString(), item.serviceCode, new { @class = "form-control", style = "width: 80px" })
                                        </td>
                                        <td>@Html.TextBox("diagnosticCode" + i.ToString(), item.diagnosticCode, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("fee" + i.ToString(), item.fee, new { @class = "form-control", style = "width: 80px" })</td>
                                        <td>@Html.TextBox("feePaid" + i.ToString(), item.feePaid, new { @class = "form-control", style = "width: 80px" })</td>
                                        <td>@Html.TextBox("numberOfServices" + i.ToString(), item.numberOfServices, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("edtGroupId" + i.ToString(), item.edtGroupId, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("sli" + i.ToString(), item.sli, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("hospitalCode" + i.ToString(), item.hospitalCode, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.DropDownList("billStatusId" + i.ToString(), new SelectList(Model.billStatuses, "value", "text", item.billStatusId), htmlAttributes: new { @class = "form-control", style = "width: 120px", onchange = "billStatusChange(" + i.ToString() + ");" })</td>
                                        <td>@Html.DropDownList("paymentMethod" + i.ToString(), new SelectList(Model.paymentMethods, "value", "text", item.payment), htmlAttributes: new { @class = "form-control", style = "width: 120px", onchange = "setInvoiceButton();" })</td>
                                        <td>@Html.TextBox("speciality" + i.ToString(), item.speciality, new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.DropDownList("manualReview" + i.ToString(), new SelectList(Model.manualReviews, "value", "text", item.manualReview), htmlAttributes: new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@item.billToDoctor</td>
                                        <td>@item.referralDoctorName</td>
                                        <td>@item.MOHId</td>
                                        <td>@item.explanatoryCode</td>
                                        <td>@item.errorCode</td>
                                        <td>@item.sendDate</td>
                                        <td>@item.payDate</td>
                                        <td>@item.reconcileDate</td>
                                    </tr>
                                }
                                @for (int i = Model.items.Count; i < 3 + Model.items.Count; i++)
                                {
                                    <tr>
                                        <td @showServiceDate></td>
                                        <td></td>
                                        <td>
                                            @Html.Hidden("detailId" + i.ToString(), 0)
                                            @Html.TextBox("serviceCode" + i.ToString(), "", new { @class = "form-control", style = "width: 80px" })
                                        </td>
                                        <td>@Html.TextBox("diagnosticCode" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("fee" + i.ToString(), "", new { @class = "form-control", style = "width: 80px" })</td>
                                        <td>@Html.TextBox("feePaid" + i.ToString(), "", new { @class = "form-control", style = "width: 80px" })</td>
                                        <td>@Html.TextBox("numberOfServices" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("edtGroupId" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("sli" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.TextBox("hospitalCode" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.DropDownList("billStatusId" + i.ToString(), new SelectList(Model.billStatuses, "value", "text", 0), htmlAttributes: new { @class = "form-control", style = "width: 120px", onchange = "billStatusChange(" + i.ToString() + ");" })</td>
                                        <td>@Html.DropDownList("paymentMethod" + i.ToString(), new SelectList(Model.paymentMethods, "value", "text", 0), htmlAttributes: new { @class = "form-control", style = "width: 120px", onchange = "setInvoiceButton();" })</td>
                                        <td>@Html.TextBox("speciality" + i.ToString(), "", new { @class = "form-control", style = "width: 64px" })</td>
                                        <td>@Html.DropDownList("manualReview" + i.ToString(), new SelectList(Model.manualReviews, "value", "text", " "), htmlAttributes: new { @class = "form-control", style = "width: 64px" })</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <hr />
                        @if (Model.isTelephoneConsult && isTelephoneConsultCodeMapping)
                        {
                            <input type="button" id="buttonK084A" value="K084A" class="btn-lg btn-info" onclick="buttonK084AClicked()" />
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="col-sm-12 text-center text-danger">
                    <br /><br /><br /><br /><br /><br /><br /><br />
                    <h1>@Model.errorMessage</h1>
                    <br /><br /><br /><br /><br /><br /><br /><br />
                </div>
            }
            <div class="col-sm-12" style="margin: 16px 0 16px 0;">
                <div class="col-sm-4">
                    <div class="row">
                        <label class="col-sm-2 text-right">Notes</label>
                        <div class="col-sm-10">
                            @Html.TextArea("billingNotes", (object)Model.billingNotes, 3, 60, new { @class = "form-control", @maxlength = "500" })
                        </div>
                    </div>
                </div>
                <div class="col-sm-8">
                    <div class="col-sm-4 text-right">
                        <input type="button" id="buttonAppointmentClaimSave" value="Save" class="btn-lg btn-info" style="display: @(string.IsNullOrEmpty(Model.errorMessage) ? "" : "none")" data-dismiss="modal" />
                    </div>
                    <div class="col-sm-4 text-center">
                        <input type="button" id="buttonAppointmentClaimInvoice" value="" class="btn-lg btn-info" style="display: @(string.IsNullOrEmpty(Model.errorMessage) ? "" : "none")" />
                    </div>
                    <div class="col-sm-4 text-left">
                        <input type="button" value="Close" class="btn-lg btn-info" data-dismiss="modal" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>