@model Cerebrum.ViewModels.Bill.InvoiceResponse

<style type="text/css">
    #appointment-invoice-container input, #appointment-invoice-container select, #appointment-invoice-container textarea {
        max-width: 100%;
    }

    @@media screen and (min-width: 768px) {
        #cbAppointmentClaimInvoiceModal .modal-lg, #cbAdmissionActionInvoiceModal .modal-lg {
            width: 85% !important;
        }
    }
</style>

<script type="text/javascript">
    var appointmentId_Invoice = @Model.appointmentId;
    var admissionIds_Invoice = "@Model.admissionIds";
    var admissionActionIds_Invoice = "@Model.admissionActionIds";
    var billingDetailCount = 3;
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        @:billingDetailCount = @(Model.items.Count + 3);
        }
</script>
<script type="text/javascript" src="~/Areas/Bill/Scripts/Invoice.js"></script>

<div class="col-sm-12" id="appointment-invoice-container" style="background-color: white; padding: 0 0 0 0; margin: 0 0 0 0;">
    <div class="col-sm-12">
        <br />
        <div class="col-sm-11" style="margin-left: 0px; padding-left: 0px;">
            <h4 class="modal-title">Bill</h4>
        </div>
        <div class="col-sm-1">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        </div>
    </div>
    <div class="col-sm-12">
        <hr />
    </div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-12">
            <table style="width: 50%; ">
                <tr>
                    <td width="64px" style="margin-left: 8px; padding-left: 8px; ">Payor:</td>
                    <td width="24px"><input type="radio" name="payorType" value="0" checked></td>
                    <td><input type="text" id="payor" class="form-control" onclick="changePayorType(0);"></td>
                </tr>
                <tr>
                    <td></td>
                    <td><input type="radio" name="payorType" value="1"></td>
                    <td>@Html.DropDownList("payoyId", new SelectList(Model.payors, "value", "text"), htmlAttributes: new { @class = "form-control ", onchange = "changePayorType(1);" })</td>
                </tr>
                <tr>
                    <td style="margin-left: 8px; padding-left: 8px; ">OMA:</td>
                    <td width="24px"><input type="checkbox" id="omaRateChecked"></td>
                    <td><input type="text" id="omaRate" class="form-control" value="@Model.omaRate"></td>
                </tr>
            </table>
        </div>
        <div class="col-sm-12" style="height: 568px; overflow-y: auto; margin-top: 16px; padding-top: 16px;">
            @{ string showServiceDate = "none;";
                if (Model.isMultipleDate)
                {
                    showServiceDate = "block;";
                }
            }
            <table id="billCodeList" name="billCodeList" class="table" style="width: 100%;">
                <thead>
                    <tr>
                        <th style="display: @showServiceDate">Date</th>
                        <th>Diagnosis</th>
                        <th>Service</th>
                        <th>Professional / Technical</th>
                        <th>Billing Code</th>
                        <th>Fee</th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.items.Count; i++)
                    {
                        var item = Model.items[i];
                        <tr>
                            <td style="display: @showServiceDate">@item.serviceDate</td>
                            <td>
                                @Html.Hidden("billingDetailId" + i.ToString(), item.billingDetailId.ToString())
                                @Html.TextBox("diaDiagnosticCode" + i.ToString(), item.diagnosticCode, new { @class = "form-control" })
                            </td>
                            <td>@Html.TextBox("service" + i.ToString(), item.service, new { @class = "form-control" })</td>
                            <td>@Html.DropDownList("billingTypeId" + i.ToString(), new SelectList(Model.billingTypes, "value", "text", item.billingTypeId), htmlAttributes: new { @class = "form-control " })</td>
                            <td>@Html.TextBox("billingCode" + i.ToString(), item.billingCode, new { @class = "form-control" })</td>
                            <td>@Html.TextBox("fee" + i.ToString(), item.fee, new { @class = "form-control" })</td>
                        </tr>
                    }
                    @for (int i = Model.items.Count; i < 3 + Model.items.Count; i++)
                    {
                        <tr>
                            <td style="display: @showServiceDate"></td>
                            <td>
                                @Html.Hidden("billingDetailId" + i.ToString(), "0")
                                @Html.TextBox("diaDiagnosticCode" + i.ToString(), "", new { @class = "form-control" })
                            </td>
                            <td>@Html.TextBox("service" + i.ToString(), "", new { @class = "form-control" })</td>
                            <td>@Html.DropDownList("billingTypeId" + i.ToString(), new SelectList(Model.billingTypes, "value", "text"), htmlAttributes: new { @class = "form-control " })</td>
                            <td>@Html.TextBox("billingCode" + i.ToString(), "", new { @class = "form-control" })</td>
                            <td>@Html.TextBox("fee" + i.ToString(), "", new { @class = "form-control" })</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="col-sm-12 text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
    <div class="col-sm-12" style="margin: 16px 0 16px 0;">
        <div class="col-sm-9 text-center">
            <input type="button" id="buttonContinueBilling" value="Continue Billing and Show Invoice" class="btn-lg btn-info" data-modal-url="@Url.Action("SaveInvoice", "groupbill", new { area = "bill" })" style="display: @(string.IsNullOrEmpty(Model.errorMessage) ? "" : "none")" />
        </div>
        <div class="col-sm-3 text-left">
            <input type="button" value="Close" class="btn-lg btn-info" data-dismiss="modal" />
        </div>
    </div>
</div>