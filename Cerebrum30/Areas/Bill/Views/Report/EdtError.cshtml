@model Cerebrum.ViewModels.Bill.EdtErrorResponse
@{
    string page = "Billing/Claim Errors";
    ViewBag.ModuleName = page;
    Layout = null;
}
<script>
    $(document).ready(function () {
        setHeaderTitle('@@page');
    });
</script>

<style type="text/css">
    /*.ui-dialog, .ui-widget, .ui-widget-content {
        background: white !important;
    }

    .noclose .ui-dialog-titlebar-close {
        display: none;
    }

    .ui-dialog-titlebar {
        background-color: steelblue;
        color: yellow;
    }*/
</style>
<script type="text/javascript" src="~/Areas/Bill/Scripts/EdtError.js"></script>

<div>

@if (string.IsNullOrEmpty(Model.errorMessage))
{
    <div class="col-sm-4" style="padding-left:0px">
        <div style="width:100%" >
            <div class="row">
                <div class="col-sm-12" style="padding: 0;">
                    <div class="form-group col-sm-6">
                        <label for="downloadStart">Download Start</label>
                        <input type="text" class="form-control input-sm" id="downloadStart">
                    </div>
                    <div class="form-group col-sm-6">
                        <label for="downloadEnd">Download End</label>
                        <input type="text" class="form-control input-sm" id="downloadEnd">
                    </div>
                </div>
                
                <div class="col-sm-12" style="padding: 0; ">
                    <div class="form-group col-sm-6">
                        @Html.DropDownList("practiceDoctorId", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                    </div>
                    <div class="form-group col-sm-6">
                        @if (Model.offices.Count() == 1)
                        {
                            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                        }
                        else
                        {
                            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                        }
                    </div>
                </div>

                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div class="col-sm-6">
                        <input id="checkBoxErrorsNotFixed" type="checkbox" checked> Errors Not Fixed
                    </div>
                </div>
                <div class="col-sm-12 text-right">
                    <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" />
                </div>
            </div>
            </div>

        </div>

        <div class="col-sm-8">
            <div style="width: 100%;">@*<div class="col-sm-12">*@
            <table id="edtErrorList" name="edtErrorList" class='table' style="width: 100%;">
                <thead>
                    <tr>
                        <th><a href="" onclick="sortBy('doctor'); return false;">Doctor<span id="imageDoctor" name="imageDoctor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        <th><a href="" onclick="sortBy('office'); return false;">Office<span id="imageOffice" name="imageOffice" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        <th><a href="" onclick="sortBy('edtgroup'); return false;">Billing Group<span id="imageEdtGroup" name="imageEdtGroup" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        <th><a href="" onclick="sortBy('file'); return false;">File<span id="imageFile" name="imageFile" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        <th><a href="" onclick="sortBy('downloaded'); return false;">Downloaded<span id="imageDownloaded" name="imageDownloaded" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        <th><a href="" onclick="sortBy('status'); return false;">Status<span id="imageStatus" name="imageStatus" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
            @*<div class="col-sm-12 text-right spacer-top-7">*@
            <div class="text-right spacer-top-7">
                <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
            </div>
        </div>
}
else
{
    <div class="text-center text-danger">
        <br /><br /><br /><br /><br /><br /><br /><br />
        <h1>@Model.errorMessage</h1>
        <br /><br /><br /><br /><br /><br /><br /><br />
    </div>
}
</div>

<div id="edtErrorDialog" name="edtErrorDialog" style="display:none;"></div>
