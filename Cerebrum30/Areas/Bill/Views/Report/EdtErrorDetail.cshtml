@model Cerebrum.ViewModels.Bill.EdtErrorShowResponse

@{
    ViewBag.Title = "Report";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
}
<script type="text/javascript" src="~/Areas/Bill/Scripts/EdtErrorDetail.js"></script>

<div class="col-sm-12" style="margin-top: 0px;">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-12" style="font-size: 18px; font-weight: bold; margin-top: 12px;">
            ERROR FILE: @Model.errorFileName
        </div>
        foreach (var doctorEdtErrorData in Model.doctorEdtErrorData)
        {
            <div class="col-sm-12" style="font-size: 16px; font-weight: bold; margin-top: 8px;">
                Doctor: @(doctorEdtErrorData.doctorName)             
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                Process Date: @doctorEdtErrorData.ProcessDate
            </div>
            if (doctorEdtErrorData.patientEdtErrorData.Count == 0)
            {
                <div class="col-sm-12" style="margin-left: 32px; margin-top: 8px; padding-top: 8px; font-size: 16px; color: red;">No data</div>
            }
            else
            {
                foreach (var patientEdtErrorData in doctorEdtErrorData.patientEdtErrorData)
                {
                    <div class="col-sm-12" style="margin-left: 32px; margin-top: 8px; padding-top: 8px;">
                        Patient:&nbsp;&nbsp;&nbsp;
                        @if (patientEdtErrorData.patientRecordId == 0)
                        {
                            @(patientEdtErrorData.patientName)
                        }
                        else
                        {
                            <div class="btn-popover-container">
                                <span class="popover-btn popover-pointer cb-text16 text-primary">@patientEdtErrorData.patientName</span>
                                <div class="btn-popover-title">
                                    <span class="default-text-color">e-Chart</span>
                                </div>
                                <div class="btn-popover-content">
                                    @(await Html.PartialAsync("GetPatientMenu", (object)new { area = "", Id = patientEdtErrorData.patientRecordId }))
                                </div>
                            </div>
                                        }
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Ohip #:&nbsp;&nbsp;&nbsp;@patientEdtErrorData.healthCardNumber
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Version Code:&nbsp;&nbsp;&nbsp;@patientEdtErrorData.healthCardVersion
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        DOB:&nbsp;&nbsp;&nbsp;@patientEdtErrorData.dateOfBirth
                    </div>
                    <div class="col-sm-12" style="margin-left: 32px;">
                        <div class="col-sm-8" style="margin-left: 0; padding-left: 0; ">
                            Ref.MD:&nbsp;&nbsp;&nbsp;@patientEdtErrorData.referralDoctorName
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            Ref. Number:&nbsp;&nbsp;&nbsp;@patientEdtErrorData.referralNumber
                        </div>
                        <div class="col-sm-4" style="margin-left: 0;">
                            @if (patientEdtErrorData.appointmentId > 0)
                            {
                                @Html.Raw(string.Format("<a style='color: Maroon; padding-right: 32px; display: inline;' target='_blank' href='Schedule/daysheet?OfficeId={0}&Date={1}'>Goto Daysheet</a>", patientEdtErrorData.officeId, patientEdtErrorData.appointmentDate))
                                @Html.Raw(string.Format("<a onclick='showClaim({0}, 0); return false;' style='color: Maroon; display: inline; cursor: pointer;'>Show Claim</a>", patientEdtErrorData.appointmentId))
                            }
                            else if (patientEdtErrorData.admissionActionId > 0)
                            {
                                @Html.Raw(string.Format("<a style='color: Maroon; padding-right: 32px; display: inline;' target='_blank' href='hd/admissions?Date={0}&practiceDoctorId={1}'>Goto Hospital Billing</a>", patientEdtErrorData.admissionDate, doctorEdtErrorData.practiceDoctorId))
                                @Html.Raw(string.Format("<a onclick='showClaim(0, {0}); return false;' style='color: Maroon; display: inline; cursor: pointer;'>Show Claim</a>", patientEdtErrorData.admissionActionId))
                            }
                            else
                            {
                                @("Status: rebill or upbill")
                            }
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <table class="table table-striped" style="width: 92%; margin-top: 8px; margin-left: 32px;">
                            <tr>
                                <td>Service Code</td>
                                <td>Fee</td>
                                <td>Number of Service</td>
                                <td>Service Date</td>
                                <td>Diagnose Code</td>
                                <td>Errors</td>
                            </tr>
                            @foreach (var paymentEdtErrorData in patientEdtErrorData.paymentEdtErrorData)
                            {
                                <tr>
                                    @if (string.IsNullOrEmpty(paymentEdtErrorData.hx8Message))
                                    {
                                        <td>@paymentEdtErrorData.serviceCode</td>
                                        <td>@paymentEdtErrorData.fee</td>
                                        <td>@paymentEdtErrorData.numberOfServices</td>
                                        <td>@paymentEdtErrorData.serviceDate</td>
                                        <td>@paymentEdtErrorData.diagnoseCode</td>
                                        <td>@(string.IsNullOrEmpty(paymentEdtErrorData.errorCode) ? string.Empty : paymentEdtErrorData.errorCode + ": " + paymentEdtErrorData.errorMessage)</td>
                                    }
                                    else
                                    {
                                        <td colspan="6">@paymentEdtErrorData.hx8Message</td>
                                    }
                                </tr>
                            }
                        </table>
                    </div>
                }
            }
        }
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>