@model Cerebrum.ViewModels.Bill.ClaimSearchResponse
@{
    string page = "Billing/Claim Search";
    ViewBag.ModuleName = page;
    Layout = null;
}
<script>
    $(document).ready(function () {
        setHeaderTitle('@@page');
    });
</script>

<script type="text/javascript" src="~/Areas/Bill/Scripts/ClaimSearch.js"></script>

<div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-5" style="padding-left:0px">
            <div style="width:100%">
                <div class="row" style="margin-bottom: 8px;">
                    <div class="col-sm-4">
                        <input name="claimSearchOfficeBilling" type="radio" value="1" checked />&nbsp;&nbsp;Office Billing
                    </div>
                    <div class="col-sm-4">
                        <input name="claimSearchOfficeBilling" type="radio" value="0" />&nbsp;&nbsp;Hospital Billing
                    </div>
                </div>  
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="patientName">Patient</label>
                            <input type="text" class="form-control clearable input-sm" id="patientName">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="serviceDateStart">Service Date Start</label>
                            <input type="text" class="form-control input-sm" id="serviceDateStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="serviceDateEnd">Service Date End</label>
                            <input type="text" class="form-control input-sm" id="serviceDateEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="billingCode">Billing Code</label>
                            <input type="text" class="form-control clearable input-sm" id="billingCode">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="billingDateStart">Billing Date Start</label>
                            <input type="text" class="form-control input-sm" id="billingDateStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="billingDateEnd">Billing Date End</label>
                            <input type="text" class="form-control input-sm" id="billingDateEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="diagnoseCode">Diagnostic Code</label>
                            <input type="text" class="form-control clearable input-sm" id="diagnoseCode">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="paymentDateStart">Payment Date Start</label>
                            <input type="text" class="form-control input-sm" id="paymentDateStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="paymentDateEnd">Payment Date End</label>
                            <input type="text" class="form-control input-sm" id="paymentDateEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="errorCode">Error Code</label>
                            <input type="text" class="form-control clearable input-sm" id="errorCode">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="reconciledDateStart">Reconciled Date Start</label>
                            <input type="text" class="form-control input-sm" id="reconciledDateStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="reconciledDateEnd">Reconciled Date End</label>
                            <input type="text" class="form-control input-sm" id="reconciledDateEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="ohipNumber">HIN #</label>
                            <input type="text" class="form-control clearable input-sm" id="ohipNumber">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="claimNumber">Claim # (<span class="office-bill-field">Appointment ID</span><span class="hospital-bill-field">Admission ID</span>)</label>
                            <input type="text" class="form-control clearable input-sm" id="claimNumberAppointmentAdmissionId">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="claimNumber">Claim # (OHIP Reference Number)</label>
                            <input type="text" class="form-control clearable input-sm" id="claimNumberOhip">
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 8px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("practiceDoctorId", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @if (Model.offices.Count() == 1)
                            {
                                @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm office-bill-field", multiple = "true", @style = "height: 120px;" })
                            }
                            else
                            {
                                @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control input-sm office-bill-field", multiple = "true", @style = "height: 120px;" })
                            }
                        </div>
                        <div class="form-group col-sm-4 text-right">
                            <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" style="margin-top: 12px;" />
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("paymentId", new SelectList(Model.payments, "value", "text", 0), "All of Claims", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("billStatusId", new SelectList(Model.billStatuses, "value", "text"), "All of Bill Statuses", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("testId", new SelectList(Model.testCodes, "value", "text"), "All Tests", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("consultCodes", new SelectList(Model.consultCodes, "value", "text"), "All of Consult Codes", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("diagnoseCodes", new SelectList(Model.diagnoseCodes, "value", "text"), "All of Diagnose Codes", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                    </div>
                </div>

            </div>


        </div>



        <div class="col-sm-7">  
            <div style="width: 100%;">
                <table id="claimList" name="claimList" class='table' style="width: 100%;">
                    <thead>
                        <tr>
                            <th><a href="" onclick="sortBy('patient'); return false;">Patient<span id="imagePatient" name="imagePatient" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('doctor'); return false;">Doctor<span id="imageDoctor" name="imageDoctor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('office'); return false;">Office<span id="imageOffice" name="imageOffice" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('status'); return false;">Status<span id="imageStatus" name="imageStatus" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th class="office-bill-field"><a href="" onclick="sortBy('appointment'); return false;">Appointment<span id="imageAppointment" name="imageAppointment" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th class="hospital-bill-field"><a href="" onclick="sortBy('admission'); return false;">Admission<span id="imageAdmission" name="imageAdmission" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('billcode'); return false;">Bill Code<span id="imagebillcode" name="imagebillcode" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('diagnosecode'); return false;">Diagnose Code<span id="imagediagnosecode" name="imagediagnosecode" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('errorcode'); return false;">Error Code<span id="imageerrorcode" name="imageerrorcode" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('mohid'); return false;">MoHID<span id="imagemohid" name="imagemohid" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('billed'); return false;">Billed<span id="imagebilled" name="imagebilled" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('reconciled'); return false;">Reconciled<span id="imagereconciled" name="imagereconciled" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('paid'); return false;">Paid<span id="imagePaid" name="imagePaid" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="text-right spacer-top-7">
                @*<div class="row col-sm-12 text-right spacer-top-7">*@
                <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
            </div>
        </div>
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
