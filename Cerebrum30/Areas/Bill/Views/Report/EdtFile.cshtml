@model Cerebrum.ViewModels.Bill.EdtFileResponse
@{
    string page = "Billing/EDT Files";
    ViewBag.ModuleName = page;
    Layout = null;
}
<script>
    $(document).ready(function () {
        setHeaderTitle('@@page');
    });
</script>
<script type="text/javascript" src="~/Areas/Bill/Scripts/EdtFile.js"></script>

<div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-4" style="padding-left:0px">
            <div style="width:100%">
                <div class="row">

                    @*<div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="serviceStart">Service Start</label>
                            <input type="text" class="form-control input-sm" id="serviceStart">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="serviceEnd">Service End</label>
                            <input type="text" class="form-control input-sm" id="serviceEnd">
                        </div>
                    </div>*@
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="sentDateStart">Senddate Start</label>
                            <input type="text" class="form-control input-sm" id="sentDateStart">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="sentDateEnd">Senddate End</label>
                            <input type="text" class="form-control input-sm" id="sentDateEnd">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="confirmedStart">Confirmed Start</label>
                            <input type="text" class="form-control input-sm" id="confirmedStart">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="confirmedEnd">Confirmed End</label>
                            <input type="text" class="form-control input-sm" id="confirmedEnd">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="confirmDLStart">Confirm / Download Start</label>
                            <input type="text" class="form-control input-sm" id="confirmDLStart">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="confirmDLEnd">Confirm / Download End</label>
                            <input type="text" class="form-control input-sm" id="confirmDLEnd">
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            @Html.DropDownList("practiceDoctorId", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                        </div>
                        <div class="form-group col-sm-6">
                            @if (Model.offices.Count() == 1)
                            {
                                @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                            }
                            else
                            {
                                @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 180px;" })
                            }
                        </div>
                    </div>
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            <input id="checkBoxNotSent" type="checkbox">Not Sent
                        </div>
                        <div class="col-sm-4">
                            <input id="checkBoxNotConfirmed" type="checkbox">Not Confirmed
                        </div>
                        <div class="col-sm-4">
                            <input id="checkBoxError" type="checkbox">Error
                        </div>
                    </div>
                    <div class="col-sm-12 text-right">
                        <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" />
                    </div>

                </div>
            </div>
 </div>


        <div class="col-sm-8">
            <div style="width: 100%;">
                <table id="edtFileList" name="edtFileList" class='table' style="width: 100%;">
                    <thead>
                        <tr>
                            <th><a href="" onclick="sortBy('doctor'); return false;">Doctor<span id="imageDoctor" name="imageDoctor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('office'); return false;">Office<span id="imageOffice" name="imageOffice" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('edtgroup'); return false;">Billing Group<span id="imageEDTGroup" name="imageEDTGroup" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('filename'); return false;">File Name<span id="imageFileName" name="imageFileName" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('confirmationfile'); return false;">Confirmation File<span id="imageConfirmationFile" name="imageConfirmationFile" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('servicedate'); return false;">Service Date<span id="imageServiceDate" name="imageServiceDate" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('senddate'); return false;">Send Date<span id="imageSendDate" name="imageSendDate" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('confirmationdate'); return false;">Confirmation Date<span id="imageConfirmationDate" name="imageConfirmationDate" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('confirmationdownload'); return false;">Confirmation Download<span id="imageConfirmationDownload" name="imageConfirmationDownload" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('error'); return false;">Error<span id="imageError" name="imageError" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('status'); return false;">Status<span id="imageStatus" name="imageStatus" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            @*<th><a href="" onclick="sortBy('links'); return false;">Links<span id="imageLinks" name="imageLinks" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>*@
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="text-right spacer-top-7">
                <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
            </div>
            <br /><br />
        </div>
    }
    else
    {
        <div class="col-sm-12 text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>