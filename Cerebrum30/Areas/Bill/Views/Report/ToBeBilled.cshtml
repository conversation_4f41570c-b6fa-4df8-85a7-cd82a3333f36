@model Cerebrum.ViewModels.Bill.ToBeBilledResponse
    @{
        string page = "Billing/Appointment Details";
        ViewBag.ModuleName = page;
        Layout = null;

        var checkboxes = new List<dynamic>();
        checkboxes.Add(new { name = "columnPatient", text = "Show Patients", status = "checked", cssClass= "", isPaymentMethod = false });
        checkboxes.Add(new { name = "filterArrived", text = "Arrived", status = "checked", cssClass = "office-bill-field", isPaymentMethod = false });
        checkboxes.Add(new { name = "filterNotArrived", text = "Not Arrived", status = string.Empty, cssClass = "office-bill-field", isPaymentMethod = false });
        //checkboxes.Add(new { name = "filterNotFinished", text = "Not Finished", status = string.Empty, cssClass= "", isPaymentMethod = false });
        checkboxes.Add(new { name = "filterBilled", text = "Billed", status = string.Empty, cssClass = "", isPaymentMethod = false });
        checkboxes.Add(new { name = "filterNotBilled", text = "Not Billed", status = "checked", cssClass = "", isPaymentMethod = false });
        foreach (var paymentMethod in Enum.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.PaymentMethod)))
        {
            string status = string.Empty;
            if (paymentMethod.ToString().ToLower() == "ohip" || paymentMethod.ToString().ToLower() == "rmb")
            {
                status = "checked";
            }
            checkboxes.Add(new { name = paymentMethod.ToString(), text = paymentMethod.ToString(), status = status, cssClass = "", isPaymentMethod = true });
        }
    }
    <script>
        var filterCheckboxes = [];
        var filterPaymentMethodCheckboxes = [];
        @foreach (var filterCheckbox in checkboxes) {
            if (filterCheckbox.isPaymentMethod)
            {
                @:filterPaymentMethodCheckboxes.push(["@filterCheckbox.name"]);
            }
            else
            {
                @:filterCheckboxes.push(["@filterCheckbox.name"]);
            }
        }

        $(document).ready(function () {
            setHeaderTitle('@@page');
        });
    </script>

<script type="text/javascript" src="~/Areas/Bill/Scripts/ToBeBilled.js"></script>

<div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-4" style="padding-left:0px">
            <div style="width:100%">      
                <div class="row" style="margin-bottom: 8px;">
                    <div class="col-sm-3">
                        <input name="tobeBilledOfficeBilling" type="radio" value="1" checked />&nbsp;&nbsp;Office Billing
                    </div>
                    <div class="col-sm-3">
                        <input name="tobeBilledOfficeBilling" type="radio" value="0" />&nbsp;&nbsp;Hospital Billing
                    </div>
                </div>     
                <div class="row">
                    @{ 
                        for (int i = 0; i < checkboxes.Count; i++)
                        {
                            if (i % 4 == 0)
                            {
                                @Html.Raw("<div class='col-sm-12' style='padding-bottom: 24px; padding: 0; margin: 0;'>")
                            }
                            <div class="col-sm-3 @checkboxes[i].cssClass">
                                <input id="@checkboxes[i].name" name="@checkboxes[i].name" type="checkbox" value="1" @checkboxes[i].status />&nbsp;&nbsp;@checkboxes[i].text
                            </div>
                            if (i % 4 == 3 || i == checkboxes.Count - 1)
                            {
                                @Html.Raw("</div>")
                            }
                        }
                    }             
                
                    <div class="col-sm-12" style="padding:32px 0px 0px 0px; margin: 0;" >
                        <div class="form-group col-sm-6">
                            <label for="filterAppointmentStart">Start</label>
                            <input type="text" class="form-control input-sm" id="filterAppointmentStart">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="filterAppointmentEnd">End</label>
                            <input type="text" class="form-control input-sm" id="filterAppointmentEnd">
                        </div>
                    </div>
               

                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="filterFamilyDoctorName">Family Doctor</label>
                            <input type="text" class="form-control clearable input-sm" id="filterFamilyDoctorName">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="filterReferralDoctorName">Referral Doctor</label>
                            <input type="text" class="form-control clearable input-sm" id="filterReferralDoctorName">
                        </div>
                    </div>
               
               

                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-6">
                            <label for="filterPatientName">Patient</label>
                            <input type="text" class="form-control clearable input-sm" id="filterPatientName">
                        </div>
                    </div>
        

                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-6">
                            @Html.DropDownList("filterPracticeDoctorIds", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-6">
                            @if (Model.offices.Count() == 1)
                            {
                                @Html.DropDownList("filterOfficeIds", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm office-bill-field", multiple = "true", @style = "height: 120px;" })
                            }
                            else
                            {
                                @Html.DropDownList("filterOfficeIds", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control input-sm office-bill-field", multiple = "true", @style = "height: 120px;" })
                            }
                        </div>
                    </div>
               

               
                    <div class="col-sm-12 text-right spacer-top-7">
                        <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" />
                    </div>                    
                </div>

            </div>       
        </div>

        <div class="col-sm-8">
            <div style="width: 100%;" id="toBeBilledList" name="toBeBilledList" class="__0009878">
                <table class='table' style="width: 100%;" id="tbl_toBeBilled">
                    <thead>
                        <tr>
                            <th><a href="">Patient</a></th>
                            <th class="office-bill-field"><a href="">Appointment</a></th>
                            <th class="hospital-bill-field"><a href="">Admission</a></th>
                            <th><a href="">Doctor</a></th>
                            <th><a href="">Office</a></th>
                            <th><a href="">Show Claim</a></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
           
            <div>
                <div id="exportDiv" name="exportDiv" class="col-sm-2" style="padding-left: 0; display: none;">
                    <input type="button" id="to-be-billed-buttonExport" name="buttonExport" value="Export to CSV" class="btn btn-default btn-sm" style="margin-top: 7px;" />
                </div>
                <div class="text-right spacer-top-7">
                    <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
                </div>
            </div>
            <br /><br />
        </div>
        <div class="modal fade" id="billing-message-modal" role="dialog">
            <div class="modal-dialog" style="width: 900px;">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Message</h4>
                    </div>
                    <div class="modal-body">
                        <div id="billing-message-modal-header"></div>
                        <div class="modal-body" id="billing-message-modal-body" style="overflow-y: scroll; height:320px;"></div>
                    </div>
                    <div class="modal-footer">
                        <div class="col-md-8" id="billing-message-modal-footer"></div>
                        <div class="col-md-4">
                            <div id="billing-message-modal-continue-buttons">
                                <button class="btn btn-default btn-sm" id="btn-view-bill-office" data-dismiss="modal" style="width: 64px;" data-appointment-id="" data-admission-id="" onclick="return billClaim(this);">Yes</button>
                                <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">No</button>
                            </div>
                            <div id="billing-message-modal-close-button">
                                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="modal fade" id="billing-test-status-modal" role="dialog">
            <div class="modal-dialog" style="width: 450px;">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Select Test Status</h4>
                        <br /><br />Appointment tests with selected status (and beyond) will be billed.
                    </div>
                    <div class="modal-body">
                        @foreach (var item in Model.testStatuses)
                        {
                            if (item.text != null && !(item.text == "Requested" || item.text == "Appointed" || item.text == "Not arrived" || item.text == "Test ready to start" || item.text == "Being Sent"))
                            {
                                string checkedFlag = string.Empty;
                                if (item.text.Contains("started"))
                                {
                                    checkedFlag = "checked";
                                }
                                <div><input type="radio" id="billingTestStatus" name="billingTestStatus" value="@item.value" @checkedFlag>&nbsp;&nbsp;&nbsp; @item.text</div>
                            }
                        }
                    </div>
                    <div class="modal-footer">
                        <div class="col-md-6"></div>
                        <div class="col-md-6">
                            <div>
                                <button class="btn btn-default btn-sm btn-prebill-day-sheet" data-dismiss="modal" style="width: 64px;" onclick="preBillClaimPage()">Bill</button>
                                <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
