@model Cerebrum.ViewModels.Bill.PerformeterResponse
@{
    string page = "Billing/Billing Details";
    ViewBag.ModuleName = page;
    Layout = null;
}
<script>
    $(document).ready(function () {
        setHeaderTitle('@@page');
    });
</script>
<script type="text/javascript" src="~/Areas/Bill/Scripts/Performeter.js"></script>

<div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-5" style="padding-left:0px"> <!-- first column --> 
            <div style="width:100%">
                <div class="row" style="margin-bottom: 8px;">
                    <div class="col-sm-3">
                        <input name="performeterOfficeBilling" type="radio" value="1" checked />&nbsp;&nbsp;Office Billing
                    </div>
                    <div class="col-sm-3">
                        <input name="performeterOfficeBilling" type="radio" value="0" />&nbsp;&nbsp;Hospital Billing
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding-bottom: 24px; padding: 0; margin: 0;">
                        <div class="col-sm-3">
                            <input id="columnDoctor" name="columnDoctor" type="checkbox" value="1" />&nbsp;&nbsp;Doctor
                        </div>
                        <div class="col-sm-3">
                            <span class="office-bill-field"><input id="columnOffice" name="columnOffice" type="checkbox" value="1" />&nbsp;&nbsp;Office</span>
                            <span class="hospital-bill-field"><input id="columnHospital" name="columnHospital" type="checkbox" value="1" />&nbsp;&nbsp;Hospital</span>
                        </div>
                        <div class="col-sm-3">
                            <span class="office-bill-field"><input id="columnEdtGroup" name="columnEdtGroup" type="checkbox" value="1" />&nbsp;&nbsp;Billing Group</span>
                        </div>
                        <div class="col-sm-3">
                            <input id="columnPatient" name="columnPatient" type="checkbox" value="1" />&nbsp;&nbsp;Patient
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding-bottom: 24px; padding: 0; margin: 0;">
                        <div class="col-sm-3">
                            <span class="office-bill-field"><input id="columnAppointment" name="columnAppointment" type="checkbox" value="1" />&nbsp;&nbsp;Appointment</span>
                            <span class="hospital-bill-field"><input id="columnAdmission" name="columnAdmission" type="checkbox" value="1" />&nbsp;&nbsp;Admission</span>
                        </div>
                        <div class="col-sm-3">
                            <input id="columnServiceCode" name="columnServiceCode" type="checkbox" value="1" />&nbsp;&nbsp;B.Code
                        </div>
                        <div class="col-sm-3">
                            <input id="columnReferralDoctor" name="columnReferralDoctor" type="checkbox" value="1" />&nbsp;&nbsp;Ref.Doctor
                        </div>
                        <div class="col-sm-3">
                            <input id="columnFamilyDoctor" name="columnFamilyDoctor" type="checkbox" value="1" />&nbsp;&nbsp;Fam.Doctor
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding-bottom: 24px; padding: 0; margin: 0;">
                        <div class="col-sm-6">
                            <span class="office-bill-field"><input id="columnAppointmentBillStatus" name="columnAppointmentBillStatus" type="checkbox" value="1" />&nbsp;&nbsp;Appointment Bill Status</span>
                            <span class="hospital-bill-field"><input id="columnAdmissionBillStatus" name="columnAdmissionBillStatus" type="checkbox" value="1" />&nbsp;&nbsp;Admission Bill Status</span>
                        </div>
                        <div class="col-sm-3">
                            <input id="columnClaimStatus" name="columnClaimStatus" type="checkbox" value="1" />&nbsp;&nbsp;Claim Status
                        </div>
                        <div class="col-sm-3">
                            <span class="office-bill-field"><input id="columnTest" name="columnTest" type="checkbox" value="1" />&nbsp;&nbsp;Test</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding-bottom: 24px; padding: 0; margin: 0;">
                        <div class="col-sm-3">
                            <input id="columnCohort" name="columnCohort" type="checkbox" value="1" />&nbsp;&nbsp;Cohort
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 32px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="patientName">Patient</label>
                            <input type="text" class="form-control clearable input-sm" id="patientName">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="serviceStart">Service Start</label>
                            <input type="text" class="form-control input-sm" id="serviceStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="serviceEnd">Service End</label>
                            <input type="text" class="form-control input-sm" id="serviceEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="referralDoctorName">Referral Doctor</label>
                            <input type="text" class="form-control clearable input-sm" id="referralDoctorName">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="reconcileStart">Reconcile Start</label>
                            <input type="text" class="form-control input-sm" id="reconcileStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="reconcileEnd">Reconcile End</label>
                            <input type="text" class="form-control input-sm" id="reconcileEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            <label for="familyDoctorName">Family Doctor</label>
                            <input type="text" class="form-control clearable input-sm" id="familyDoctorName">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="sendDateStart">Send Date Start</label>
                            <input type="text" class="form-control input-sm" id="sendDateStart">
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="sendDateEnd">Send Date End</label>
                            <input type="text" class="form-control input-sm" id="sendDateEnd">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4">
                            @*<label for="letterStatus">Letter Status</label>
                            <input type="text" class="form-control clearable input-sm" id="letterStatus">*@
                        </div>
                        <div class="form-group col-sm-4">
                            @*<label for="testReportStatus">Test Report Status</label>
                            <input type="text" class="form-control clearable input-sm" id="testReportStatus">*@
                        </div>
                        <div class="form-group col-sm-4 text-right">
                            <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" style="margin-top: 19px;" />
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 32px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("practiceDoctorId", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            <span class="office-bill-field">
                                @if (Model.offices.Count() == 1)
                                {
                                    @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                                else
                                {
                                    @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                            </span>
                            <span class="hospital-bill-field">
                                @if (Model.hospitals    .Count() == 1)
                                {
                                    @Html.DropDownList("hospitalId", new SelectList(Model.hospitals, "value", "text"), htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                                else
                                {
                                    @Html.DropDownList("hospitalId", new SelectList(Model.hospitals, "value", "text"), "All the Hospitals", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                            </span>
                        </div>
                        <div class="col-sm-4">
                            <span class="office-bill-field">
                                @if (Model.edtGroups.Count() == 1)
                                {
                                    @Html.DropDownList("edtGroupId", new SelectList(Model.edtGroups, "value", "text"), htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                                else
                                {
                                    @Html.DropDownList("edtGroupId", new SelectList(Model.edtGroups, "value", "text"), "All Billing Groups", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                                }
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("paymentId", new SelectList(Model.payments, "value", "text", 0), "All of Claims", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            <span class="office-bill-field">@Html.DropDownList("appointmentBillStatusId", new SelectList(Model.appointmentBillStatuses, "value", "text"), "All of Appointment Bill Statuses", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })</span>
                            <span class="hospital-bill-field">@Html.DropDownList("admissionBillStatusId", new SelectList(Model.appointmentBillStatuses, "value", "text"), "All of Admission Bill Statuses", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })</span>
                        </div>
                        <div class="col-sm-4">
                            <span class="office-bill-field">@Html.DropDownList("appointmentTypeId", new SelectList(Model.appointmentTypes, "value", "text"), "All of Reasons", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })</span>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("claimStatusId", new SelectList(Model.claimStatuses, "value", "text"), "All of Claim Statuses", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("testBillCodeId", new SelectList(Model.testBillCodes.Where(a => string.Compare(a.text, "G650") < 0).OrderBy(b => b.text), "value", "text"), "All of Test's Bill Codes (1)", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("testBillCodeId2", new SelectList(Model.testBillCodes.Where(a => string.Compare(a.text, "G650") >= 0).OrderBy(b => b.text), "value", "text"), "All of Test's Bill Codes (2)", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("consultCodeId", new SelectList(Model.consultCodes.Where(a => string.Compare(a.text, "A435") < 0), "value", "text"), "All of Consult's Bill Codes (1)", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            @Html.DropDownList("consultCodeId2", new SelectList(Model.consultCodes.Where(a => string.Compare(a.text, "A435") >= 0), "value", "text"), "All of Consult's Bill Codes (2)", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                        <div class="col-sm-4">
                            <span class="office-bill-field">@Html.DropDownList("testId", new SelectList(Model.internalTests, "value", "text"), "All of Internal Tests", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })</span>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding-top: 16px; padding-bottom: 64px;">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="col-sm-4">
                            @Html.DropDownList("cohortId", new SelectList(Model.cohorts, "value", "text"), "All of Cohorts", htmlAttributes: new { @class = "form-control input-sm", multiple = "true", @style = "height: 120px;" })
                        </div>
                    </div>
                </div>
            </div><!-- ***************-->
        </div>





      

        <div class="col-sm-7">     <!-- second column -->    
            <div style="width: 100%;">
                <div id="performeterList" name="performeterList">
                    <table class='table' style="width: 100%;">
                        <thead>
                            <tr>
                                <th><a href="">Quantity</a></th>
                                <th><a href="">Amount Claimed</a></th>
                                <th><a href="">Professional Claimed</a></th>
                                <th><a href="">Technical Claimed</a></th>
                                <th><a href="">Amount Paid</a></th>
                                <th><a href="">Professional Paid</a></th>
                                <th><a href="">Technical Paid</a></th>
                                <th><a href="">Total Amount Delta</a></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div>
                    <div id="exportDiv" name="exportDiv" class="col-sm-2" style="padding-left: 0; display: none;">
                        <input type="button" id="buttonExport" name="buttonExport" value="Export to Excel" class="btn btn-default btn-sm" style="margin-top: 7px;" />
                    </div>
                    <div class="text-right spacer-top-7 col-sm-10" style="padding-right: 0;">
                        <ul class="pagination" id="pagination"68 name="pagination" style="margin: 0;"></ul>
                    </div>
                </div>
            </div>
        </div>




    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
