@model Cerebrum.ViewModels.Bill.StatementResponse
@{
    string page = "Billing/Billing Details";
    ViewBag.ModuleName = page;
    Layout = null;
}

<style type="text/css">
    @@media print {
        .table {
            font-size: 12px;
        }
    }
</style>

<script>
    var patientRecordIdStatement = "@Model.patientRecordId";
    $(document).ready(function () {
        setHeaderTitle('@@page');
    });
</script>
<script type="text/javascript" src="~/Areas/Bill/Scripts/Statement.js"></script>

<div>
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="col-sm-3" style="padding-left:0px"> <!-- first column --> 
            <div style="width:100%">
                <div class="row" style="margin-bottom: 8px; margin-left: 4px;">
                    <div class="col-sm-6">
                        <input name="statementOfficeBilling" type="radio" value="1" checked />&nbsp;&nbsp;Office Billing
                    </div>
                    <div class="col-sm-6">
                        <input name="statementOfficeBilling" type="radio" value="0" />&nbsp;&nbsp;Hospital Billing
                    </div>
                </div>
                <div class="row" style="padding: 32px 0 0 16px;">
                    <div class="col-sm-3"><input type="radio" name="payorType" value="0" checked style="margin-right: 8px;"><span style="color: red;">*</span> Patient</div>
                    <div class="col-sm-9"><input type="text" id="patientNameInput" class="form-control" onclick="changePayorType(0);" value="@Model.patientInfo"></div>
                </div>
                <div class="row" style="padding: 16px 0 0 16px;">
                    <div class="col-sm-3"><input type="radio" name="payorType" value="1" style="margin-right: 8px;"><span style="color: red;">*</span> Payor</div>
                    <div class="col-sm-9">@Html.DropDownList("payoyId", new SelectList(Model.payors, "value", "text"), htmlAttributes: new { @class = "form-control ", onchange = "changePayorType(1);" })</div>
                </div>
                <div class="row" style="padding-top: 32px;">
                    <div class="col-sm-12">
                        <div class="form-group col-sm-6">
                            <label for="startDate">Start Date</label>
                            <input type="text" class="form-control input-sm" id="startDate">
                        </div>
                        <div class="form-group col-sm-6">
                            <label for="endDate">End Date</label>
                            <input type="text" class="form-control input-sm" id="endDate">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" style="padding: 0; margin: 0;">
                        <div class="form-group col-sm-4 col-sm-offset-8 text-right">
                            <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn btn-default btn-sm" style="margin-top: 19px;" />
                        </div>
                    </div>
                </div>
            </div><!-- ***************-->
        </div>
     

        <div class="col-sm-9">     <!-- second column -->    
            <div style="width: 100%;">
                <div class="col-sm-12" id="statementDiv" name="statementDiv" style="padding: 0; margin: 0;">
                    <div class="col-sm-12 text-center">
                        <span style="font-size: 20px; padding-right: 32px; margin-right: 32px;">Statement</span> <span>@(DateTime.Now.ToString("MM/dd/yyyy"))</span><br /><br />
                    </div>
                    <div class="col-sm-12" id="patientInfo" name="patientInfo" style="padding: 0; margin: 0; display: none;">
                        <span id="patientName"></span> <br />
                        <span id="patientAddress"></span> <br />
                        <span id="patientCity"></span> <span id="patientProvince"></span> <span id="patientPostalCode"></span> <br />
                        Patient DOB: <span id="patientDob"></span>
                        <br /><br />
                    </div>
                    <div class="col-sm-12" id="payorInfo" name="payorInfo" style="padding: 0; margin: 0; display: none;">
                        <span id="payorName"></span> <br />
                        <span id="payorAddress"></span>
                        <br /><br />
                    </div>
                    <div>
                        <table id="statementList" class='table' style="width: 100%;">
                            <thead>
                                <tr>
                                    <th class="office-bill-field"><a href="" onclick="sortBy('appointmentdate'); return false;">Appointment <span id="imageAppointmentDate" name="imageAppointmentDate" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th class="hospital-bill-field"><a href="" onclick="sortBy('admissiondate'); return false;">Admission <span id="imageAdmissionDate" name="imageAdmissionDate" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('physician'); return false;">Physician <span id="imagePhysician" name="imagePhysician" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('patientpayor'); return false;"><span id="spanPatientPayor" name="spanPatientPayor">Payor</span> <span id="imagePatientPayor" name="imagePatientPayor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('diagnosis'); return false;">Diagnosis <span id="imageDiagnosis" name="imageDiagnosis" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('servicesrendered'); return false;">Services Rendered <span id="imageServicesRendered" name="imageServicesRendered" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('code'); return false;">Code <span id="imageCode" name="imageCode" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('referringdoctor'); return false;">Referring Doctor <span id="imageReferringDoctor" name="imageReferringDoctor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('numberofservice'); return false;"># <span id="imageNumberOfService" name="imageNumberOfService" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('fee'); return false;">Fee <span id="imageFee" name="imageFee" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                    <th><a href="" onclick="sortBy('billstatus'); return false;">Bill Status <span id="imageBillStatus" name="imageBillStatus" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div  id="divStatementPrint" name="divStatementPrint" class="col-sm-4 text-center">
                        <input type="button" id="buttonStatementPrint" name="buttonStatementPrint" value="Print" class="btn btn-default btn-sm" style="margin-top: 8px; display: none;" />
                    </div>
                    <div class="col-sm-8 text-right spacer-top-7">
                        <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
                    </div>
                </div>
            </div>
        </div>

    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
