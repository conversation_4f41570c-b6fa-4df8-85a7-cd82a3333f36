@model Cerebrum.ViewModels.Bill.IndexResponse

@{
    ViewBag.Title = "Report";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <style type="text/css">
        #bill-report-container {
            padding-top: 15px;
        }

            #bill-report-container .transparent {
                opacity: 0;
            }

            #bill-report-container .clearable {
                background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center;
                padding: 3px 18px 3px 4px; /* Use the same right padding (18) in jQ! */
                border-radius: 3px;
                transition: background 0.4s;
            }

                #bill-report-container .clearable.x {
                    background-position: right 5px center;
                }
                /* (jQ) Show icon */
                #bill-report-container .clearable.onX {
                    cursor: pointer;
                }
                /* (jQ) hover cursor style */
                #bill-report-container .clearable::-ms-clear {
                    display: none;
                    width: 0;
                    height: 0;
                }
        /* Remove IE default X */

        #wrapper {
            padding-left: 160px;
            /*transition: all 0.4s ease 0s;*/
        }

        #sidebar-wrapper {
            margin-left: -148px;
            left: 160px;
            width: 160px;
            background: steelblue;
            position: fixed;
            height: 100%;
            overflow-y: auto;
            z-index: 1000;
            /*transition: all 0.4s ease 0s;*/
        }

        #page-content-wrapper {
            width: 100%;
        }

        #bill-report-container .sidebar-nav {
            position: absolute;
            top: 0;
            width: 160px;
            list-style: none;
            margin: 0;
            padding: 0;
        }

            #bill-report-container .sidebar-nav li {
                /*line-height: 20px;
                text-indent: 0px;*/
            }

                #bill-report-container .sidebar-nav li a {
                    color: yellow;
                    display: block;
                    text-decoration: none;
                    padding-left: 16px;
                }

                    #bill-report-container .sidebar-nav li a:hover,
                    #bill-report-container .sidebar-nav li.active {
                        color: #fff;
                        background: rgba(255,255,255,0.2);
                        text-decoration: none;
                    }

                    #bill-report-container .sidebar-nav li a:active,
                    #bill-report-container .sidebar-nav li a:focus {
                        text-decoration: none;
                    }

        #bill-report-container .high-lighted {
            background-color: #1abc9c;
        }

        .btn-popover-container {
            display: inline-block;
        }

        .btn-popover-content {
            padding-top: 0;
            padding-bottom: 0;
            margin-top: 0;
            margin-bottom: 0;
        }

        .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
            display: none;
        }

        .popover-pointer {
            cursor: pointer;
        }
    </style>
    <link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
}

@section scripts {
    <script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>
    <script type="text/javascript" src="~/Areas/Bill/Scripts/Report.js"></script>
    <script src="~/Scripts/cerebrum3-global.js"></script>
}

<script type="text/javascript">
    @{ 
        string cookieToken, formToken;
        AntiForgery.GetTokens(null, out cookieToken, out formToken);
    }
    var requestVerificationToken = "@(cookieToken + ":" + formToken)";
</script>

<div id="bill-report-container">
    <div id="wrapper">
        @if (string.IsNullOrEmpty(Model.errorMessage))
        {
            <!-- Sidebar -->
            <div id="sidebar-wrapper">
                <ul class="sidebar-nav nav" id="billModuleList">
                    <li><a href="#" onclick="return openNewReport('performeter', 0)">Performeter</a></li>
                    <li><a href="#" onclick="return openNewReport('edtfiles', 0)">EDT Files</a></li>
                    <li><a href="#" onclick="return openNewReport('edterrors', 0)">Claim Errors</a></li>
                    <li><a href="#" onclick="return openNewReport('tobebilled', 0)">To Be Billed</a></li>
                    <li><a href="#" onclick="return openNewReport('claimsearch', 0)">Claim Search</a></li>
                    <li><a href="#" onclick="return openNewReport('remittanceadvicefiles', 0)">Remittance Advice Files</a></li>
                    <li><a href="#" onclick="return openNewReport('billinghistory', 0)">Billing History</a></li>
                    <li><a href="#" onclick="return openNewReport('statement', 0)">Statement</a></li>
                    <li><a href="#" onclick="return openNewReport('logfiles', 0)">Log Files</a></li>
                    <li><a target="_blank" href="Help/ThirdPartyGuide.pdf">OMA Guide To Uninsured Services</a></li>
                </ul>
            </div>
            <!-- Page content -->
            <div id="page-content-wrapper">
                <div class="col-sm-12 __mainPlace3Holder__" id="reportDiv" name="reportDiv">
                </div>
            </div>
            <div id="patientMenuTemplate" name="patientMenuTemplate" style="display:none;">
                <div class="btn-popover-container">
                    <span class="popover-btn popover-pointer cb-text16 text-primary">PatientMenuTemplatePatientName</span>
                    <div class="btn-popover-title">
                        <span class="default-text-color">e-Chart</span>
                    </div>
                    <div class="btn-popover-content">
                        @(await Html.PartialAsync("GetPatientMenu", (object)new { area = "", Id = -123456 }))
                    </div>
                </div>
            </div>
            @*<div class="col-sm-12" id="imageLoading" name="imageLoading" style="position: fixed; left: 30%; top: 40%; display: none; z-index: 100000000;">*@
            <div class="col-sm-12 __445637" id="imageLoading" name="imageLoading" style="position: fixed; left: 48%; top: 200px; display: none; z-index: 100000000;">
                <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
            </div>
        }
        else
        {
            <div class="text-center text-danger">
                <br /><br /><br /><br /><br /><br /><br /><br />
                <h1>@Model.errorMessage</h1>
                <br /><br /><br /><br /><br /><br /><br /><br />
            </div>
        }
    </div>
    <div class="modal fade in col-sm-12" id="modal-template-id">
        <div class="modal-dialog col-sm-12" id="modal-template-content-id">
            <div class="modal-content col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                <div class="modal-header no-borders col-sm-12">
                    <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                        <h4 class="modal-title" id="modal-template-title-id"></h4>
                    </div>
                    <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto; padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;"></div>
            </div>
        </div>
    </div>
</div>