@model ClaimResponse
@using Cerebrum.ViewModels.Bill;

@{
    ViewBag.Title = "Claim";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
}
<script type="text/javascript" src="~/Areas/Bill/Scripts/Claim.js"></script>

<div class="col-sm-12" style="margin-top: 0px;">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        string claimNumber = Model.admissionActionId;
        string officeBillingChecked = string.Empty;
        string hospitalBillingChecked = "checked";
        if (string.IsNullOrWhiteSpace(Model.admissionActionId) || Model.admissionActionId == "0")
        {
            claimNumber = Model.appointmentId;
            officeBillingChecked = "checked";
            hospitalBillingChecked = string.Empty;
        }
        <div class="col-sm-12" style="margin-top: 12px;">
            <div class="col-sm-3">
                <div class="col-sm-4">
                    <input name="claimOfficeBilling" type="radio" value="1" @officeBillingChecked />&nbsp;&nbsp;Office Billing
                </div>
                <div class="col-sm-8">
                    <input name="claimOfficeBilling" type="radio" value="0" @hospitalBillingChecked />&nbsp;&nbsp;Hospital Billing
                </div>
            </div>
            <div class="col-sm-3 form-group text-right">
                <label for="appointmentId" style="float:left; margin-right:5px;">Claim Number:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="claimNumber" value="@claimNumber">
                </div>
            </div>
            <div class="col-sm-3">
                <input type="button" id="buttonSearch" name="buttonSearch" value="Show Claims by Claim number" class="btn-lg btn-info" />
            </div>
        </div>
        <div class="col-sm-12 text-center">
            <div class="col-sm-6 text-right">
                <span style="font-weight: bold;">Patient: </span><span id="patientName" name="patientName"></span>  
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                <span style="font-weight: bold;">HIN: </span><span id="healthCardNumber" name="healthCardNumber"></span> 
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                <span style="font-weight: bold;"><span class="office-bill-field">Appointment Date:</span><span class="hospital-bill-field">Admission Date:</span> </span><span id="appointmentAdmissionDate" name="appointmentAdmissionDate"></span>
            </div> 
            <div class="col-sm-2">
                <select id="billStatus" name="billStatus" class="form-control">
                </select>
            </div>           
        </div>
        <div class="col-sm-12 text-center" style="margin-top: 16px;">
            <table class="table table-striped" id="claimList" name="claimList" style="width: 98%;">
                <thead>
                    <tr>
                        <td>Doctor</td>
                        <td>Ref Dr.</td>
                        <td>Office</td>
                        <td align="left">Billing Group</td>
                        <td>Service Date</td>
                        <td align="left">Service Code</td>
                        <td align="left">Diagnosis</td>
                        <td align="left">Fee</td>
                        <td align="left">Number of Services</td>
                        <td align="left">Status</td>
                        <td align="left">Payment</td>
                        <td align="left">Speciality</td>
                        <td align="left">Manual Review</td>
                        <td align="left">MOH ID</td>
                        <td align="left">Error Code</td>
                        <td align="left">Send Date</td>
                        <td align="left">Pay Date</td>
                        <td align="left">Reconcile Date</td>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="col-sm-12 text-center">
            <br />
            <input type="button" id="buttonSave" name="buttonSave" value="Save" class="btn-lg btn-info" />
        </div>
        <div id="patientMenuTemplate" name="patientMenuTemplate" style="display:none;">
            <div class="btn-popover-container">
                <span class="popover-btn popover-pointer cb-text16 text-primary">PatientMenuTemplatePatientName</span>
                <div class="btn-popover-title">
                    <span class="default-text-color">e-Chart</span>
                </div>
                <div class="btn-popover-content">
                    @(await Html.PartialAsync("GetPatientMenu", (object)new { area = "", Id = -123456 }))
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="col-sm-12 text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>