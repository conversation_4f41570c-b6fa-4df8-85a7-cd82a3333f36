@model Cerebrum.ViewModels.BillingAdmin.IndexResponse

@{
    ViewBag.Title = "Billing Admin";
    ViewBag.ModuleName = "Billing Admin";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <style type="text/css">
        .link-pointer {cursor: pointer; }
    </style>
}

@section scripts {
    <script type="text/javascript" src="~/Areas/Bill/Scripts/BillingAdmin.js"></script>
}

<div id="bill-report-container" style="padding-top: 15px">
    <div id="wrapper7567">
        @if (string.IsNullOrEmpty(Model.errorMessage))
        {
            <div class="row">
                <div class="form-group form-group-sm col-sm-6">
                    <div class="form-group col-sm-1"><label class="control-label hdr-lbl billing-heading" title=""> Office </label></div>
                    <div class="form-group col-sm-3">
                        @if (Model.offices.Count() == 1)
                        {
                            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text", Model.offices[0].value), htmlAttributes: new { @class = "form-control select-office-change" })
                        }
                        else
                        {
                            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "Please Select Office", htmlAttributes: new { @class = "form-control select-office-change" })
                        }
                    </div>
                </div>
            </div>
            <div id="officeBilling">@(await Html.PartialAsync("OfficeBilling", (object)(object)Model.officeBilling))</div>
            <br /><br />
            <div>
                <button type="button" class="btn btn-default btn-sm btn-primary btn-test-billing-type">Test Billing (Group / Solo)</button>
                <button type="button" class="btn btn-default btn-sm btn-primary btn-test-billing-code" style="margin-left: 24px;">Test Billing Codes</button>
                <button type="button" class="btn btn-default btn-sm btn-primary btn-consult-billing-code" style="margin-left: 24px;">Consult Billing Codes</button>
            </div>
            <br />
            <div id="billingCodes"></div>
        }
        else
        {
            <div class="text-center text-danger">
                <br /><br /><br /><br /><br /><br /><br /><br />
                <h1>@Model.errorMessage</h1>
                <br /><br /><br /><br /><br /><br /><br /><br />
            </div>
        }
    </div>
</div>