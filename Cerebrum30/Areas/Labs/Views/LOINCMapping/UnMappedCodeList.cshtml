@model Cerebrum.ViewModels.HL7.UnmappedCodeList
<script>
    $(document).on('click', "a.unmapped-page", function () {
        var liId = $(this).parent("li").data("id");
        $(this).parent("li").addClass("active");
        $.get('/labs/loincmapping/UnMappedCodeList/', { pageNumber: liId }).done(function (respVal) {
            $("#div-mapcodes").html(respVal);
        });
    });
</script>
@if (Model != null && Model.MapCodes != null && Model.MapCodes.Count() > 0)
{
    <div class="panel panel-default">
        <div class="panel-heading">@Model.SearchType Laboratory Codes [@Model.MapCodes.Count]</div>
        @{ await Html.RenderPartialAsync("UnmappedCodes", (object)Model.MapCodes); }
    </div>
}
else
{
    <p class="alert alert-warning">could not find any search result.</p>
}

