@model Cerebrum.ViewModels.OLIS.VMOlisLogsMain
@{
    ViewBag.Title = "OLIS Communication Logs"; // "OLIS Search";
    ViewBag.ModuleName = "OLIS Communication Logs"; // "OLIS Search";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<style>
    #tbl-olis-com-logs .expand-btn,
    #tbl-olis-com-logs .collapse-btn {
        font-size: small;
    }

    .text-primary {
        color: #428bca !important; /*#186571*/
    }

    .btn-default {
        background-color: #f9f9f9;
    }

    .btn-primary {
        background-color: #428bca;
    }

    .tr-child-row-white-bg {
        background-color: #ffffff;
    }

    .tr-child-row-white-bg td {
        background-color: #ffffff;
        /*font-size: 11px;
        font-style: italic;*/
    }
</style>

<h2>OLIS Communication Logs</h2>
<div class="row">

</div>


@if (Model.Paging || Model.TotalItems > 0)
{
    var totalPages = Model.TotalPages;
    var pageNumber = Model.PageNumber;
    var currentPaging = Model.PagingDescription;
   
    <div style="margin-bottom:10px;" class="row">
        <div class="col-md-12">
            <div class="pull-left">
                <span>@currentPaging</span>
            </div>
            @if (Model.Paging)
            {
            <div style="padding-top:3px;padding-right:2px;" class="pull-right">
                <span> of </span><span>@totalPages</span>
            </div>
            
            <div class="pull-right">
                <div class="dropdown pull-right">
                    <button class="btn btn-default dropdown-toggle btn-xs" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        Page @pageNumber
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        @for (int i = 1; i <= totalPages; i++)
                        {
                            var active = i == pageNumber ? "active" : "";
                            var pagingUrl = Url.Action("index", "oliscommunicationlog",
                                new { area = "labs", Page = i });
                            <li class="@active"><a href="@pagingUrl">Page @i</a></li>
                        }
                    </ul>
                </div>
            </div>
            }
        </div>
    </div>

}
@if (Model != null && (Model.Requests!=null && Model.Requests.Any()))
{
<table id="tbl-olis-com-logs" class="table">
    <tr>
        <th></th>
        <th>Id</th>
        <th>
            Start Date
        </th>
        <th>
            End Date
        </th>
        <th>
            Transcation Date
        </th>
        <th>Office</th>
        <th>
            Run By
        </th>
        <th>
            For Doctor
        </th>
        <th>
            Query Type
        </th>
        <th>
            Consent Overrride
        </th>

        <th>
            OLIS Id
        </th>
        <th>
            Successfull
        </th>
        <th>
            Message
        </th>
        <th>
            Message Type
        </th>

        <th>
            Error
        </th>
        <th></th>
        <th></th>
        <th></th>
    </tr>

    @foreach (var item in Model.Requests)
    {
        await Html.RenderPartialAsync("CommLogs", (object)item);
    }

</table>
}
<hr />
<script>
    $(document).ready(function () {
        $('[data-toggle="popover"]').css('overflow-y', 'auto').popover().map(function () {
            $(this).data('bs.popover')
              .tip()
              .addClass('popover-container-custom')
        });
        $('.popover-dismiss').popover({
            'trigger': 'hover',
            'placement': 'left',
            'container': '.panel-body'
        });

        $(document).on('click', '#tbl-olis-com-logs .expand-btn, #tbl-olis-com-logs .collapse-btn', function (e) {

            e.preventDefault();

            if ($(this).hasClass('expand-btn')) {
                $(this).html('<small><span class="glyphicon glyphicon-minus"></span></small>')
                $(this).removeClass('expand-btn')
                $(this).addClass('collapse-btn')
            }
            else {
                $(this).html('<small><span class="glyphicon glyphicon-plus"></span></small>')
                $(this).removeClass('collapse-btn')
                $(this).addClass('expand-btn')
            }
            var tr = $(this).parents('tr:first');
            var trID = tr.attr('id');
            var childRowsCriteria = "tr[id^=" + trID + "-]";

            $(childRowsCriteria).toggle();

        });

        //expandAllRows();
    });

    function expandAllRows()
    {
        $('#tbl-olis-com-logs .expand-btn').each(function () {
            var btn = $(this);
            btn.trigger('click');
        });
    }
</script>