@model Cerebrum.ViewModels.OLIS.VMOLISCommunicationLog


@{
    var item = Model;
    var msh = item.MSH10ClientTransactionID.ToString().Replace("-", "");
    var parentId = !item.IsRequest ? (int)item.OLISCommunicationLogId : 0;
    var trId = item.IsRequest ? "tr-com-log-" + msh : "tr-com-log-" + msh + "-" + item.Id;
    var trClass = item.IsRequest ? "tr-com-request" : "tr-com-response tr-child-row-white-bg";
    var trstyle = item.IsRequest ? "" : "display:none;";
    var showExpandLink = item.IsRequest && item.Responses.Any() ? true : false;

}
<tr id="@trId" class="@trClass" style="@trstyle">
    <td style="width:10px;">
        @if (showExpandLink)
        {<a href="#" class="expand-btn"><small><span class="glyphicon glyphicon-plus"></span></small></a>}
    </td>
    <td>
        @item.Id
        <a class="popoverButton" data-toggle="popover" title="MSH" data-trigger="focus" tabindex="0"
            data-content="@item.MSH10ClientTransactionID"><span class="glyphicon glyphicon-file"></span></a>
    </td>
    <td>
        @item.StartDateTime
    </td>
    <td>
        @item.EndDateTime
    </td>
    <td>
        @item.createdDateTime
    </td>
    <td>
        @item.OfficeName
    </td>
    <td>
        @item.UserName
    </td>
    <td>
        @item.DoctorName
    </td>
    <td>
        @item.EMRToOLISQueryType
    </td>
    <td>
        @item.consentViewOverride
    </td>

    <td>
        @item.OLISTransactionID
    </td>
    <td>
        @item.IsSuccessStatusCode
    </td>
    <td>
        @if (!string.IsNullOrWhiteSpace(item.Message))
        {
            <a class="popoverButton" data-toggle="popover" title="Message" data-trigger="focus" tabindex="0"
                data-content="@item.Message"><span class="glyphicon glyphicon-file"></span></a>
        }
    </td>
    <td>
        @item.MessageType
    </td>

    <td>
        @if (!string.IsNullOrWhiteSpace(item.Error))
        {
            <a class="popoverButton" data-toggle="popover" title="Error" data-trigger="focus" tabindex="0"
                data-content="@item.Error"><span class="glyphicon glyphicon-file"></span></a>
        }
            
    </td>
    <td>@item.ReportSummary</td>
    <td>
        @if (item.PatientReportStatus != null && item.PatientReportStatus.Any())
        {
            var ri = item.PatientReportStatus.FirstOrDefault();
            @Html.ActionLink("Preview", "ReportPreview", new { controller = "OLIS", Area = "Labs", report = ri.ReportId }, new { @class = "btn btn-primary btn-xs", target = "_blank" })
            
        }
    </td>
    <td>
        @if (item.PatientReportStatus.Any())
        {
            <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#<EMAIL>">
                Report Status
            </button>
            <div id="<EMAIL>" class="modal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">                                
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">Report Status</h4>
                        </div>
                        <div class="modal-body">
                            @Html.RenderPartialAsync("OLISLogPatientStatus", (object)item.PatientReportStatus);}
                        </div>

                    </div>
                </div>
            </div>
        }

    </td>

</tr>

@* Print the responses for *@
@foreach (var res in Model.Responses)
{
    await Html.RenderPartialAsync("CommLogs", (object)res);    
}
