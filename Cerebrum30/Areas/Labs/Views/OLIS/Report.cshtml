@model Cerebrum.ViewModels.OLIS.VMOlisReport
@using Cerebrum30.Utility;
<div style="width: 100%;" >
    
    <div class="row">
        <div class="col-sm-4"> @(await Html.PartialAsync("Patient", (object)(object)Model.Patient))</div>
        <div class="col-sm-4"> @(await Html.PartialAsync("Provider", (object)(object)Model.Provider))</div>
        <div class="col-sm-4"> @(await Html.PartialAsync("ReportDetails", (object)(object)Model.ReportDetails))</div>
    </div>
    @if (!string.IsNullOrWhiteSpace(Model.PatientReportBlockConsentNote))
    {
    <div class="alert alert-danger text-center" role="alert">
        <p class="red">@Html.Raw(Model.PatientReportBlockConsentNote)</p>
    </div>
    }
    <div class="row">
        <div class="col-md-12">@*width: 100%; display: block; clear: both; background: #4cff00*@
            <div style="float: left; border-top: 1px solid #c5c1c1; width: 100%" class="olis-heading">Comments</div>
            @*<hr />*@
            <div class="dont-break-out fixedWidthfont" style="float: left;">
               @foreach (var c in Model.ReportNotes)
               {
                   string comment = c;
                <div>@Html.Raw(comment)</div><br />
               }            
            </div>            
        </div>
    </div>


    @if (Model.Patient != null && (!string.IsNullOrWhiteSpace(Model.Patient.OrderNotes)))
    {
        <div class="row">
            <div class="col-md-12">
                <h4>Comments (orderNote)</h4>
                <div class="dont-break-out fixedWidthfont">@Html.Raw(Model.Patient.OrderNotes.FormatHL7Report())</div>
                <hr />
            </div>
        </div>
    }   
</div>


<div class="requested-tests" style="width: 100%; margin-bottom: 70px">
    @(await Html.PartialAsync("TestCategories", (object)(object)Model.TestCategories))
</div>


<hr />
<div class="row">
    <div class="col-md-3">@(await Html.PartialAsync("OLISCCList", (object)(object)Model.CCList))</div>
    <div class="col-md-3">@(await Html.PartialAsync("OLISOrderingFacility", (object)(object)Model.OrderingFacility))</div>
    <div class="col-md-3">
        @if (Model.AdmittingProvider != null)
        {
        <div>
            <h4>Admitting Provider</h4>
            <hr />
            @(await Html.PartialAsync("PractitionerProvider", (object)(object)Model.AdmittingProvider))
        </div>
        }
        @if (Model.AttendingProvider != null)
        {
        <div>
            <h4>Attending Provider</h4>
            <hr />
            @*@(await Html.PartialAsync("PractitionerProvider", (object)(object)Model.AdmittingProvider))*@
        </div>
        }
    </div>
</div>