@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISTestCategory>
@using Cerebrum30.Utility;
@{
    var tstcargrp = (from mo in Model
                     group mo by mo.TestCategory into mog
                     select new { grpreq = mog.Key, reqs = mog.SelectMany(sml => sml.RequestedTests).ToList() }).ToList();
}
@foreach (var i in tstcargrp)
{
    <div class="panel panel-default">
        <div class="panel-heading report-header">
        @*@Html.CheckBox("TestCategory", (object)(object)new { @checked = "checked", @class = "cl-test-category-filter", data_test_category_filter_id = @i.grpreq })@i.grpreq*@

        <input name="TestCategory" class="cl-test-category-filter" type="checkbox" data-test-category-filter-id="@i.grpreq" checked="checked" /> @i.grpreq
        
        </div>
    <div class="panel-body">
        @{
            var reqtests = from t in i.reqs
                           group t by t.TestGroupOBR41 into tg
                           select new { req = tg.Where(fr=>fr.Results.Any()).ToList() };
        }
        <div class="col-md-10">
            @*@(await Html.PartialAsync("Filter_RequestedTests", (object)reqtests.SelectMany(ns => ns.req)).ToList())*@
            @(await Html.PartialAsync("Filter_RequestedTests", (object)i.reqs.ToList()))
        </div>
    </div>
    <div class="panel-footer"></div>
</div>
}
