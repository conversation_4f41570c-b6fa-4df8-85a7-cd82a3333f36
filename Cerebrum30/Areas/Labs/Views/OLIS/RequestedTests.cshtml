@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISRequestedTest>
@using Cerebrum30.Utility;
@{ 
    string testCategory = string.Empty;
    string status = string.Empty;
    var fst = from mg in Model
              group mg by mg.TestCategory into tg
              select new { tg.Key, Other = tg };

}
@foreach (var mi in fst)
{
<div style="border-bottom: 5px solid #a2bfcb;"></div> 
<table class="table olis-tbl" style="border: 0px solid #ff8800 !important">
    <tr><td colspan="8" style="text-align:left;" class="hdr-lbl-hg">@mi.Key</td> </tr>
    <tr style="color: #8598a0;">
        @*<th></th>*@
        <th class="olis-heading">Specimen Type</th>
        <th class="olis-heading">Site Modifier</th>
        <th class="olis-heading">Collection Volumn</th>
        <th class="olis-heading">Collection DateTime</th>
        <th class="olis-heading">Number Of Sample Containers</th>
        <th class="olis-heading">Specimen Collected By</th>
        <th class="olis-heading">Parent Result</th>
        <th class="olis-heading">PORT</th>
        <th class="olis-heading test-sort-key">Sort key
            <button class="pull-right btn btn-default btn-condensed hide-column"  data-toggle="tooltip" data-placement="bottom" title="Hide Column">
            <i class="glyphicon glyphicon-eye-close"></i></button></th>
    </tr>

@{ bool resultHeaderFlag = false;}
@foreach (var item in mi.Other.ToList().OrderBy(o => o.testRequestSortKey))
{
   
    <tr style="padding-top:35px" class="_234534564 A">
        @*<td></td>*@
        <td>@item.SpecimenTypeOBR15 </td>
        <td>@item.SiteModifier</td>
        <td>@item.CollectionVolumnOBR9</td>
        <td>@item.CollectionDateTimeOBR7</td>
        <td>@item.NumberOfSampleContainersOBR37</td>
        <td>@item.SpecimenCollectedBy</td>
        <td>@item.ParentResult</td>
        <td>@item.pointOfCareTestIdentifierOBR30</td>
        <td class="test-sort-key">@item.testRequestSortKey</td>
    </tr>


    <tr class="B">
        <td colspan="9" id="@item.TestIdentifier" class="filter_TestRequest" data-test-request-code="@item.TestIdentifier" data-test-request-status="@item.testRequestStatus">
            <h4>  <input class="" name="olis-test-requests" type="checkbox" value="@item.TestIdentifier" /> @item.TestGroupOBR41 
                @if (item.testRequestStatus != AwareMD.Cerebrum.Shared.Enums.OLISTestRequestStatus.F)
                {
                    <span class="text-warning">(@item.testRequestStatus.DisplayName())</span>
                }
            </h4>
        </td>
    </tr>


    if (item.Diagnosis.Any())
    {
        foreach (var di in item.Diagnosis)
        {
    <tr>
        <td class="dont-break-out fixedWidthfont" colspan="9">
            <strong>Diagnosis:</strong>   @Html.Raw(di.FormatHL7Report())
        </td>
    </tr>
        }
    }
    <tr>
        <td class="dont-break-out fixedWidthfont" colspan="9">
            @Html.Raw(item.CollectorComment.FormatHL7Report())
        </td>
    </tr>
    <tr>
        <td class="dont-break-out fixedWidthfont" colspan="9">
            @Html.Raw(item.SpecimenComment.FormatHL7Report())
        </td>
    </tr>

    if (item.Results != null && item.Results.Count() > 0)
    {
    <tr>
        <td colspan="9" style="padding: 0px">
            <table class="table Result-Table">
                @if (!resultHeaderFlag)
                    {

                        { resultHeaderFlag = true; }

                    }
                <tr>
                    <td class="td-parent-requested-test" data-parent-requested-test="@item.TestIdentifier" style="padding: 0px">
                        @(await Html.PartialAsync("Results", (object)item.Results))
                    </td>
                </tr>
            </table>

        </td>
    </tr>

    }

    <tr><td colspan="9" style="border-bottom: 3px solid #b0acac;"><div >&nbsp;</div> </td></tr>   @*b0acac*@
}@*\foreach*@



</table>
}