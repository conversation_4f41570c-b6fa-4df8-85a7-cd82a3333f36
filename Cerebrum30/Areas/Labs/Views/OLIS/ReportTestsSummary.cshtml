@model Cerebrum.ViewModels.OLIS.VMOLISPreviewReport
@using Cerebrum30.Utility
@using Newtonsoft.Json
@{
    ViewBag.Title = "Report Tests Summary/Details"; //OLIS
    ViewBag.ModuleName = "Report Tests Summary/Details";
    //Layout = "~/Views/Shared/_Layout.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.18/css/jquery.dataTables.css">
<style>
    .strikeText {
        text-decoration: line-through;
    }

    .AbnormalResult {
        color: #ff0040;
    }

    .AbnormalFlag {
        color: #ff0040;
    }

    .NormalResult {
        color: #333;
    }

    .NormalFlag {
    }

    .customSearchCtrl {
        border: 1px solid rgb(204, 204, 204);
        padding: 1px 3px;
        color: rgb(85, 85, 85);
    }

    .panel-heading span {
        padding-right: 50px;
    }

    .buttons-print span {
        /*color: #ff0000;*/
    }

    .dt-print-view > h1 {
        display: none !important;
    }

    .popover-container-custom {
        font-family: monospace !important;
        white-space: pre-wrap;
    }

    @@media print {
        body {
            /*transform: scale(.90);*/
            /*page-break-before: avoid;*/
            font-size: 9px !important;
        }

        .hidden-print {
            display: none !important;
        }

        #ft-appName {
            display: none !important;
        }
    }

    .hid {
        visibility: hidden; /* hidden  | visible*/
    }
</style>
<div style="padding-top: 15px">
    <div class="row">
        <div class="form-group form-group-sm col-sm-12">
            Ministry of Health and Long-Term Care<br />
            Printed from Ontario Laboratories Information System (OLIS)
        </div>
    </div>
    @if (Model != null && !Model.Reports.Any())
    {
        <p>
            <h2 class="alert-danger">No results have been found in OLIS.</h2>
        </p>
    }
    else
    {
        var patientgrp = from r in Model.Reports
                         group r by r.Patient.ONHealthNo into pg
                         select new { pg.Key, Other = pg };


        foreach (var p in patientgrp)
        {
            // Using JsonConvert instead of JavaScriptSerializer for ASP.NET Core compatibility

            var patient = p.Other.FirstOrDefault().Patient;

            int setid = 0;
            int.TryParse(patient.SetId, out setid);

            var consentmsg = p.Other.FirstOrDefault().PatientReportBlockConsentNote;
            var orderNote = p.Other.FirstOrDefault().ReportNotes;
            var reportsg = p.Other.SelectMany(tcm => tcm.TestCategories).SelectMany(sm => sm.RequestedTests).OrderBy(o => o.testRequestSortKey).ToList();



            var cats = p.Other;
            var detailreports = new List<Cerebrum.ViewModels.OLIS.VMReportDetails>();
            int count = 0;
            foreach (var c in cats)
            {
                ++count;
                var rpt = new Cerebrum.ViewModels.OLIS.VMReportDetails();
                List<Cerebrum.ViewModels.OLIS.VMOLISRequestedTest> reqtests = new List<Cerebrum.ViewModels.OLIS.VMOLISRequestedTest>();
                rpt.ReportId = setid+"_"+count;

                rpt.OrderNote = c.Patient.OrderNotes;
                rpt.FullReportURL = $"/labs/OLIS/ReportPreview/?report={Model.ReportId}";
                foreach (var tc in c.TestCategories)
                {
                    var trs = tc.RequestedTests.ToList();
                    reqtests.AddRange(trs);
                }
                reqtests.OrderBy(o => o.testRequestSortKey);
                rpt.RequestedTests = reqtests.ToList();

                detailreports.Add(rpt);
            }

            //var zzz = "{ name: '" + @Html.Raw(patient.PatientName.FullName) + "'}";

            var _patient = new
            {
                id = setid,
                name = patient.PatientName.FullName,
                ONHealthNo = patient.ONHealthNo,
                DOB = patient.DOB
            };

            var dataItem = new HtmlString(string.Format(
                "data=\"{0}\"",
                Html.Encode(JsonConvert.SerializeObject(_patient))
                ));
            <div class="panel panel-default p-cnt" @dataItem>
                <div class="panel-heading">
                    <span><strong>OHIP:</strong> @patient.ONHealthNo </span>
                    <span><strong>Name:</strong> @patient.PatientName.FullName  </span>
                    <span><strong>DoB:</strong> @patient.DOB </span>
                    <span><strong>Sex:</strong> @patient.Gender </span>
                    <div id="consent_@setid" class="alert-danger" style="text-align: center">@Html.Raw((@consentmsg).Replace("&lt;br/&gt;", "<br />"))</div>
                    <span class="pull-right">
                        <strong>Total Items: </strong>@reportsg.Count()@*<a href="#" class="show_all_results hidden-print">Show All Results</a>*@
                    </span>
                </div>

                <div class="panel-body">
                    <div class="nav-tabs1" id="tab_@setid">
                        @* nav-tabs id="myTab"*@
                        <a class="btn btn-default btn-xs active" data-toggle="tab" href="#Summary_@setid">Summary</a>
                        <a class="btn btn-default btn-xs" data-toggle="tab" href="#Test-Details_@setid">Details</a>
                    </div>

                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="Summary_@setid">
                            <br />
                            @(await Html.PartialAsync("RequestedTestsSummary", (object)(object)detailreports))
                        </div>
                        <div class="tab-pane fade" id="Test-Details_@setid">
                            <br />
                            @(await Html.PartialAsync("RequestedTestDetails", (object)(object)detailreports))
                        </div>
                    </div>

                </div>
                <div class="panel-footer">
                    <span><strong>OHIP:</strong> @patient.ONHealthNo </span>
                    <span><strong>Name:</strong> @patient.PatientName.FullName  </span>
                    <span><strong>DoB:</strong> @patient.DOB </span>
                    <span><strong>Sex:</strong> @patient.Gender </span>
                    @*<span class="alert-danger">@consentmsg</span>*@

                    <span class="pull-right">
                        <strong>Total Items: </strong>@reportsg.Count()
                        @*<a href="#" class="show_all_results hidden-print">Show All Results</a>*@
                    </span>
                    <div class="alert-danger" style="text-align: center">@Html.Raw((@consentmsg).Replace("&lt;br/&gt;", "<br />"))</div>
                </div>
            </div>
        }
    }
</div>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.18/js/jquery.dataTables.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.5.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.5.2/js/buttons.print.min.js"></script>
<script type="text/javascript">
    var screenPageTitle, printPageTitle;
    screenPageTitle = '@ViewBag.Title';
    //printPageTitle = 'PATIENT: ' + $('#lbl_FullName').html() + ' | HEALTH NUMBER: ' + $('#lbl_ONHealthNo').html();
    printPageTitle = '';

    //var setPrintPageTitle = function (nameArg, ohipArg) {
    //    printPageTitle = 'PATIENT: ' + nameArg + ' | HEALTH NUMBER: ' + ohipArg;
    //    return printPageTitle;
    //};

    var disclaimerTop = '<div class="row">' +
                           '<div class="form-group form-group-sm col-sm-12">Ministry of Health and Long-Term Care<br />' +
                                    'Printed from Ontario Laboratories Information System (OLIS)' +
                            '</div>' +
                        '</div>';

    function FilterTestCategory() {

    }

    $(document).ready(function () {

        $('.table').each(function () {
            var that = this;
            var uniqgroup = [];

            $('.testcategory').each(function () {
                uniqgroup.push($(this).data());
            });

            var dataObj = $(this).parents('.p-cnt').attr('data');
            var tableid = $(this).attr('id');
            var groupColumn = 2;

            var table = $('#' + tableid).DataTable(
                {
                    //"responsive": true,
                    //"displayLength": 100,
                    //"ordering": false,
                    "order": [[2, 'asc']],      //"order": [[2, 'asc'], [3, 'asc'],[0, 'desc']],  //sort by multiple columns:
                    // 2: groupColumn (group name), turned to hidden as requesed.
                    // 3: Test Request Name
                    // 0: and, date (first column)
                    "columnDefs": [
                        {
                            "visible": false,
                            "targets": groupColumn
                        }
                        ,
                        {
                            "targets": 3,
                            "render": function (data, type, row) {
                                var gc = '&nbsp;';
                                if (row[2].length > 0) {
                                    gc = row[2];
                                }
                                return data + ' <span class="hid" style="color: red">' + gc + '</span>';
                            }
                        }
                    ],
                    drawCallback: function (settings) {
                        $('[data-toggle="popover"]').css('overflow-y', 'auto').popover().map(function () {
                            $(this).data('bs.popover')
                              .tip()
                              .addClass('popover-container-custom')
                        });

                        var api = this.api();
                        var rows = api.rows({ page: 'current' }).nodes();
                        var last = null;
                        var grpvals = [];
                        api.column(groupColumn, { page: 'current' }).data().each(function (group, i) {
                            if (last !== group) {
                                grpvals.push(group);
                                var z = group;
                                if (z.length == 0) {
                                    z = '&nbsp;';
                                }
                                var cols = 19; //false: 19, true : 20
                                if (tableid.indexOf('testsSummary') != -1) {
                                    cols = 9;   //false: 9, true : 10
                                }

                                $(rows).eq(i).before(
                                    '<tr class="group "><td colspan="' + cols + '" style="background: #777; color: #fff">' + z + '</td></tr>'
                                );
                                last = group;
                            }
                        });
                    },
                    dom: 'lBfrtip',
                    buttons: [{
                        extend: 'print',
                        exportOptions: {
                            columns: ':not(.notForPrint)'
                        },
                        //autoPrint: true,
                        //messageTop: '',
                        customize: function (win) {
                            var arr = tableid.split('_');
                            var idx = arr[1];
                            var consentMsg = $('#consent_' + idx).html();

                            $(win.document).attr('title', 'PATIENT: ' + JSON.parse(dataObj).name + ' | HEALTH NUMBER: ' + JSON.parse(dataObj).ONHealthNo);// printPageTitle
                            $(win.document.body).find('table').before(disclaimerTop);
                            $(win.document.body).find('table').before('NOTE: \'Order Notes\' not displayed in Print Mode due to space considerations.  Please see the online report for notes.');
                            $(win.document.body).find('table').before(consentMsg);
                            $(win.document.body).find('table').addClass('compact').css('font-size', '9px');
                        },
                        name: 'print-' + tableid
                        //,
                        //action: function (e, dt, node, config) {
                        //    var _patientItem = $(node).parents('.p-cnt').attr('data');
                        //    console.log(JSON.parse(_patientItem).DOB);
                        //}
                    }
                    ],
                    fixedHeader: {
                        //header: true,
                        //footer: true
                    },
                    'initComplete': function (settings, json) {
                        $('.table').removeAttr('style');
                        $('.buttons-print, .dt-button').removeAttr('style');
                        $('.buttons-print').addClass('btn btn-default btn-xs').css('padding', 'unset').css('padding', '1px 15px').css('margin-left', '15px');
                        $('.buttons-print').on('click', function () { });
                    }
                }

            );


        });

        //$('#requested-tests-details').DataTable({
        //    drawCallback: function () {
        //        $('[data-toggle="popover"]').css("overflow-y", "auto").popover();
        //    }
        //});

        $('.popover-dismiss').popover({
            'trigger': 'hover',
            'placement': 'left',
            'container': '.panel-body'
        });

        $('.nav-tabs1 a').on('click', function () {
            $(this).tab('show');
            var parentID = $(this).parents('.nav-tabs1').attr('id');
            eval("$('#" + parentID + " .active').removeClass('active')");
            $(this).addClass('active').blur();
        });

        $('.show_all_results').on('click', function (e) {
            e.preventDefault();
            $('.collapse').collapse('toggle');
        });
    });

    $.extend($.fn.dataTableExt.oStdClasses, {
        'sFilterInput': 'customSearchCtrl'
    });
</script>
