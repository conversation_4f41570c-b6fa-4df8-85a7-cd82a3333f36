@model Cerebrum.ViewModels.OLIS.VMOLISSearch
@using Cerebrum30.Utility;
@{
    ViewBag.Title = "Search Laboratory Results"; // "OLIS Search";
    ViewBag.ModuleName = "Search Laboratory Results"; // "OLIS Search";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
<style>
    #OLIS-search-container .text-danger {
        display: block;
        clear: both;
    }

    .checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio] {
        margin-left: 0px !important;
    }

    #ConsentComment {
        height: 76px !important;
    }
</style>
<link href="~/Content/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<div class="container-fluid" id="OLIS-search-container" style="padding-top: 15px">
    <h3>OLIS Query</h3>
    <h4><a href="#" id="OLIS-Config" data-url="/olis/Config/" data-toggle="modal" data-target="#olis-modal-box">Config</a></h4>
    @if (Model.QueryResponse.Count() > 0)
    {
        if (Model.QueryResponse.Count(a => a.ResponseReports.Any()) > 0)
        {
            @(await Html.PartialAsync("ResponseReport", (object)(object)Model.QueryResponse.SelectMany(s => s.ResponseReports)).ToList())
        }
        else
        {
            foreach (var qr in Model.QueryResponse)
            {
                <p>
                    <h3> @qr.Report</h3>
                </p>
            }
        }
    }


    <div class="row">
        @using (Html.BeginForm("Index", "OLIS", FormMethod.Post, new { id = "frm-OLISQuery" }))
        {
            <div class="col-lg-7">
                @Html.AntiForgeryToken()
                <div class="row">
                    <div class="form-group form-group-sm col-sm-12">
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.Query, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.fromDate, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.toDate, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.quantityLimit, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.consentToViewBlockedInfo, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.consentAutorizedBy, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.performingLaboratory, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.excludePerformingLaboratory, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.reportingLaboratory, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.excludeReportingLaboratory, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.patient, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.orderingPractitioner, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.copiedToPractitioner, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.attendingPratitioner, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.admittingPractitioner, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.testRequestStatus, "", new { @class = "text-danger" })
                        @Html.ValidationMessageFor(model => model.testRequestCode, "", new { @class = "text-danger" })

                        @Html.ValidationMessageFor(model => model.placerGroupNumber, "", new { @class = "text-danger" })

                    </div>
                </div>
                <div class="row">
                    <div class="form-group form-group-sm col-sm-2">
                        @*@Html.LabelFor(model => model.requestingPractitioner, htmlAttributes: new { @class = "control-label" })*@
                        <div class="truncate" title="Requesting Prac. HIC">Requesting Prac. HIC</div>
                        @Html.EditorFor(model => model.requestingPractitioner, new { htmlAttributes = new { @class = "form-control 555" } })
                        <button type="button" class="btn btn-default btn-sm btn-primary" data-toggle="modal" data-target="#doctorListModal">
                            <span class="glyphicon glyphicon-sunglasses"></span>
                        </button>
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Query Type">Query Type</div>
                        @*@Html.LabelFor(model => model.Query, htmlAttributes: new { @class = "control-label" })*@
                        @Html.DropDownListFor(model => model.Query, new SelectList(Model.QueryTypes, "QueryId", "Query", Model.Query), "Query Type...", new { @class = "form-control" })
                        <h6>Please visit <a tabindex="-1" href="@Url.Action("Index", "OLISCommunicationLog", new { area = "Labs" })" target="_blank">OLIS Log</a> for preload query result.</h6>
                        @*@Html.DropDownListFor(model => model.EMRQueryType, new SelectList(AwareMD.Cerebrum.Shared.Enums.EMRToOLISQueryType.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.EMRToOLISQueryType)).Cast<AwareMD.Cerebrum.Shared.Enums.EMRToOLISQueryType>().Select(x => new SelectListItem {Text=x.DisplayName(), Value= x.ToString() }).ToList(), "Value", "Text",-1), "Query Type...", new { @class = "form-control" })*@
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        @*@Html.LabelFor(model => model.fromDate, htmlAttributes: new { @class = "control-label" })*@
                        <div class="truncate" title="From Date">From Date</div>
                        @Html.EditorFor(model => model.fromDate, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        @*@Html.LabelFor(model => model.toDate, htmlAttributes: new { @class = "control-label" })*@
                        <div class="truncate" title="To Date">To Date</div>
                        @Html.EditorFor(model => model.toDate, new { htmlAttributes = new { @class = "form-control" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        @Html.LabelFor(model => model.patient, htmlAttributes: new { @class = "control-label  olis-patient-search", @title = "Patient" })
                        @Html.EditorFor(model => model.patient, new { htmlAttributes = new { @class = "form-control MM" } })
                        @Html.HiddenFor(model => (object)model.patientId)
                        @Html.HiddenFor(model => (object)model.dateOfBirth)
                        @Html.HiddenFor(model => (object)model.gender)
                        <input type="submit" value="Search" class="btn btn-default btn-sm btn-primary btn-olis-search" />
                    </div>
                </div>

                <div class="row">

                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Performing Laboratory">Performing Laboratory</div>
                        @*@Html.LabelFor(model => model.performingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                            @Html.Editor("performingLaboratorySearch", new { htmlAttributes = new { @class = "form-control" } })
                            @Html.HiddenFor(model => model.performingLaboratory, new { htmlAttributes = new { @class = "form-control" } })
                        *@
                        @Html.DropDownListFor(model => model.performingLaboratory, new SelectList(Model.Labs, "Value", "Text", Model.performingLaboratory), "Labs...", new { @class = "form-control" })

                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Exclude Performing Laboratory">Exclude Performing Laboratory</div>
                        @*@Html.LabelFor(model => model.excludePerformingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                            @Html.Editor("excludePerformingLaboratorySearch", new { htmlAttributes = new { @class = "form-control" } })
                            @Html.HiddenFor(model => model.excludePerformingLaboratory, new { htmlAttributes = new { @class = "form-control" } })*@
                        @Html.DropDownListFor(model => model.excludePerformingLaboratory, new SelectList(Model.Labs, "Value", "Text", Model.excludePerformingLaboratory), "Labs...", new { @class = "form-control" })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Reporting Laboratory">Reporting Laboratory</div>
                        @*@Html.LabelFor(model => model.reportingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                            @Html.Editor("reportingLaboratorySearch", new { htmlAttributes = new { @class = "form-control" } })
                            @Html.HiddenFor(model => model.reportingLaboratory, new { htmlAttributes = new { @class = "form-control" } })*@
                        @Html.DropDownListFor(model => model.reportingLaboratory, new SelectList(Model.Labs, "Value", "Text", Model.reportingLaboratory), "Labs...", new { @class = "form-control" })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Exclude Reporting Laboratory">Exclude Reporting Laboratory</div>
                        @*@Html.LabelFor(model => model.excludeReportingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                            @Html.Editor("excludeReportingLaboratorySearch", new { htmlAttributes = new { @class = "form-control" } })
                            @Html.HiddenFor(model => model.excludeReportingLaboratory, new { htmlAttributes = new { @class = "form-control" } })*@
                        @Html.DropDownListFor(model => model.excludeReportingLaboratory, new SelectList(Model.Labs, "Value", "Text", Model.excludeReportingLaboratory), "Labs...", new { @class = "form-control" })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="">&nbsp;</div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Ordering Practitioner HIC">Ordering Practitioner HIC</div>
                        @*@Html.LabelFor(model => model.orderingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @Html.EditorFor(model => model.orderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Copied-to Practitioner HIC">Copied-to Practitioner HIC</div>
                        @*@Html.LabelFor(model => model.copiedToPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @Html.EditorFor(model => model.copiedToPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Attending Practitioner HIC">Attending Practitioner HIC</div>
                        @*@Html.LabelFor(model => model.attendingPratitioner, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @Html.EditorFor(model => model.attendingPratitioner, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Admitting Practitioner HIC">Admitting Practitioner HIC</div>
                        @*@Html.LabelFor(model => model.admittingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @Html.EditorFor(model => model.admittingPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    @*<div class="form-group form-group-sm col-sm-2">
                            <div class="truncate" title="">OrderID/ReportID</div>
                            @Html.LabelFor(model => model.placerGroupNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                            @Html.EditorFor(model => model.placerGroupNumber, new { htmlAttributes = new { @class = "form-control" } })
                        </div>*@

                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="">&nbsp;</div>

                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-2">
                        <div class="truncate" title="Test Request Status">Test Request Status</div>
                        @{
                            var requeststatus = AwareMD.Cerebrum.Shared.Enums.OLISTestRequestStatus.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.OLISTestRequestStatus)).Cast<AwareMD.Cerebrum.Shared.Enums.OLISTestRequestStatus>().Select(x => new SelectListItem { Text = x.DisplayName(), Value = x.ToString() }).ToList();
                        }
                        @Html.ListBoxFor(model => model.testRequestStatus, new MultiSelectList(requeststatus, "Value", "Text"), new { multiple = "multiple", @class = "form-control" })
                    </div>
                    <div class="form-group form-group-sm col-sm-4">
                        <div class="truncate" title="Test Request Code">Test Request Code</div>
                        @*EditorFor*@
                        @Html.EditorFor(model => model.testRequestCode, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-4">
                        <div class="truncate" title="Test Result Code">Test Result Code</div>
                        @*EditorFor*@
                        @Html.EditorFor(model => model.testResultCode, new { htmlAttributes = new { @class = "form-control" } })
                        <button type="button" id="test-result-codes-lookup" class="btn btn-default btn-sm btn-primary" data-toggle="modal" data-url="@Url.Action("TestResultCodes","olis",new { area="Labs"})" data-target="#olis-modal-box">
                            <span class="glyphicon glyphicon-sunglasses"></span>
                        </button>
                    </div>
                    <div class="form-group col-sm-2">
                        <div class="truncate" title="Test Result Status">Result Status</div>
                        @*@Html.LabelFor(model => model.testRequestStatus, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @*@Html.EnumDropDownListFor(model=>model.testRequestStatus, "Select...", new { @class = "form-control" })*@
                        @{
                            var resultstatuses = AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.OLISResultStatus)).Cast<AwareMD.Cerebrum.Shared.Enums.OLISResultStatus>().Select(x => new SelectListItem { Text = x.DisplayName(), Value = x.ToString() }).ToList();

                        }
                        @Html.ListBoxFor(model => model.testResultStatus, new MultiSelectList(resultstatuses, "Value", "Text"), new { multiple = "multiple", @class = "form-control" })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <div class="truncate" title="Quantity Limit?">Quantity Limit?</div>
                        @*@Html.LabelFor(model => model.quantityLimit, htmlAttributes: new { @class = "control-label col-md-2" })*@
                        @Html.EditorFor(model => model.quantityLimit, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                </div>



                <div class="row" style="padding-top: 15px">
                    <div class="form-group form-group-sm col-sm-12">
                        <h4 style="display: inline-block">Patient Lab Results Temporary Consent  Reinstatement</h4>
                        <p>Patients whose consent is reinstated at the point of care will receive notice of access to their laboratory results.</p>
                        <p><strong>Access will be enabled for 4 hrs for anyone under the same authority.</strong></p>
                        <p>
                            If you have the patient's or substitue decision maker's (SDM) consent to view this patient's <br />
                            blocked laboratory results, enter or select the appropriate choices below and click <strong>Search</strong>
                        </p>
                        @*&nbsp; <a class="btn btn-default btn-sm" href="javascript:void(0)" id="_swicthSDM" style="margin-top: -4px;">&nbsp;</a>*@
                    </div>
                </div>

                <div id="cnt-SDM" style="display: block;">
                    <div class="row">
                        <div class="form-group form-group-sm col-sm-3">
                            <div title="Consent to View Blocked Information?">I have temporary consent from the patient?</div>
                            @*@Html.LabelFor(model => model.consentToViewBlockedInfo, htmlAttributes: new { @class = "control-label col-md-2" })*@
                            @Html.EditorFor(model => (object)model.consentToViewBlockedInfo)
                        </div>
                        <div class="form-group form-group-md col-md-4">
                            <div title="Authorized by">I have temporary consent from the patient's substitute decision maker (SDM)</div>
                            @*@Html.LabelFor(model => model.consentAutorizedBy, htmlAttributes: new { @class = "control-label col-md-2" })*@
                            @Html.EditorFor(model => (object)model.consentAutorizedBy)
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group form-group-sm col-sm-2">
                            <div class="truncate" title="SDMGivenName">SDM GivenName</div>
                            @Html.EditorFor(model => model.SDMGivenName, new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                        <div class="form-group form-group-sm col-sm-2">
                            <div class="truncate" title="SDMGivenName">SDM FamilyName</div>
                            @Html.EditorFor(model => model.SDMFamilyName, new { htmlAttributes = new { @class = "form-control" } })
                        </div>

                        <div class="form-group form-group-sm col-sm-4">
                            <div class="truncate" title="ReplationshipToPatient">Replationship To Patient</div>
                            @Html.DropDownListFor(model => model.ReplationshipToPatient, new SelectList(AwareMD.Cerebrum.Shared.Enums.OLISSDMRelationship.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.OLISSDMRelationship)).Cast<AwareMD.Cerebrum.Shared.Enums.OLISSDMRelationship>().Select(x => new SelectListItem { Text = x.DisplayName(), Value = x.ToString() }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @*@Html.EnumDropDownListFor(model=>model.ReplationshipToPatient, "Select...", new { @class = "form-control" })*@
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group form-group-sm col-sm-12">
                            <div class="truncate" title="ConsentComment">Consent Comment</div>
                            @*@Html.TextAreaFor(model => model.ConsentComment, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.TextAreaFor(model => model.ConsentComment, new { @class = "form-control" })
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-bottom:30px">
                    <div class="form-group form-group-sm col-sm-12">
                        @*<div class="truncate" title="">&nbsp;</div>*@
                        @Html.HiddenFor(model => (object)model.ExternalQuery)
                        @Html.HiddenFor(model => (object)model.IsDoctor)
                        @Html.HiddenFor(model => (object)model.PracticeDoctorId)
                        <input type="submit" value="Search" class="btn btn-default btn-sm btn-primary btn-olis-search" />
                    </div>
                </div>
                
            </div>

         }

        <div class="col-lg-5" style="background: ">
            @if (Model.QueryResponse.Count() > 0)
            {
                <div class="panel-heading1">Messages</div>
                <div class="row">

                    @{ string responsemessage = string.Empty;
                        string sentmessage = string.Empty;
                    }
                    @foreach (var q in Model.QueryResponse.SelectMany(s => s.ResponseReports))
                    {
                        sentmessage += q.SentMessage + "\n\n";
                        responsemessage += q.ResponseMessage + "\n\n";
                    }
                    <!-- form-group form-group-sm col-sm-2 -->
                    <div class="col-sm-4">@Html.TextArea("SentMessage", (object)sentmessage, 30, 100, new { @class = "form-control" })</div>
                    <div class="col-sm-8">@Html.TextArea("ResponseMessage", (object)responsemessage, 30, 100, new { @class = "form-control" })</div>
                </div>
                        }
        </div>
    </div>
</div>

<div class="modal fade" id="doctorListModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Practice Doctors</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="doctor-list">
                    @Html.Action("PracticeDoctors", "OLIS", new { area = "Labs" })
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="olis-modal-box" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<br />
<br />
<script src="~/Scripts/moment.min.js"></script>
<script src="~/Scripts/bootstrap-datetimepicker.min.js"></script>

<script type="text/javascript">
    var searchPatientByName = function () {
        $('#patient').autocomplete({
            source: function (request, response) {
                $.ajax({
                    type: 'GET',
                    data: { patientSearch: request.term },
                    url: '/patients/GetPracticePatients/',
                    contentType: 'application/json ; charset=utf-8',
                    dataType: 'json'
                })
                .done(function (d) {
                    response($.map(d, function (item) {
                        return {
                            'label': (item.LastName).toUpperCase() + ', ' + item.FirstName,
                            'value': item.PatientId
                        }
                    }))
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    alert('Error:' + errorThrown);
                    checkAjaxError(jqXHR, null);
                });
            },
            select: function (event, ui) {
                var lbl = ui.item.label;
                var val = ui.item.value;

                ui.item.value = lbl;
                $(this).attr('title', lbl).attr('data', val);

                $('#patientId').val(val);
            },
            minLength: 2
        });
    };

    var resetSearhValuesIfEmpty = function () {
        if (!$('#patient').val().length > 0) {
            $('#patient, #patientId').val('');
            $('#patient').attr('title', '').attr('data', '');
        }
    };

    function SearchTestRequestCode() {
        $('#testRequestCode').autocomplete({
            source: function (request, response) {
                $.ajax({
                    type: 'GET',
                    data: { Search: request.term },
                    url: '/LABS/OLIS/SearchTestRequestCodes',
                    contentType: 'application/json ; charset=utf-8',
                    dataType: 'json'
                })
                .done(function (d) {
                    response($.map(d, function (item) {
                        return {
                            'label': (item.Code).toUpperCase() + '; ' + item.Name1 + '; ' + item.Name2 + '; ' + item.Name3,
                            'value': item.Code
                        }
                    }))
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    alert('Error:' + errorThrown);
                    checkAjaxError(jqXHR, null);
                });
            },
            select: function (event, ui) {
                var lbl = ui.item.label;
                var val = ui.item.value;

                ui.item.value = lbl;
                $(this).attr('title', lbl).attr('data', val);

                $('#testRequestCode').val(val);
            },
            minLength: 2
        });
    }

    function SearchLabs(searchO, idO) {
        $(searchO).autocomplete({

            source: function (request, response) {
                $.ajax({
                    type: 'GET',
                    data: { Search: request.term },
                    url: '/LABS/OLIS/SearchLabs',
                    contentType: 'application/json ; charset=utf-8',
                    dataType: 'json'
                })
                .done(function (d) {
                    response($.map(d, function (item) {
                        //console.log(item);
                        return {
                            'label': item.Text,
                            'value': item.Value
                        }
                    }))
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    alert('Error:' + errorThrown);
                    checkAjaxError(jqXHR, null);
                });
            },
            select: function (event, ui) {
                var lbl = ui.item.label;
                var val = ui.item.value;
                //console.log(ui.item);
                ui.item.value = lbl;
                $(this).attr('title', lbl).attr('data', val);
                $(idO).val(val);
            },
            minLength: 2
        });
    }
    function SearchTestResultCode() {
        $('#testResultCode').autocomplete({
            source: function (request, response) {
                $.ajax({
                    type: 'GET',
                    data: { Search: request.term },
                    url: '/LABS/OLIS/SearchTestResultCodes',
                    contentType: 'application/json ; charset=utf-8',
                    dataType: 'json'
                })
                .done(function (d) {
                    response($.map(d, function (item) {
                        return {
                            'label': (item.LOINC) + '; ' + item.Name1 + '; ' + item.Name2 + '; ' + item.Name3,
                            'value': item.LOINC
                        }
                    }))
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    alert('Error:' + errorThrown);
                    checkAjaxError(jqXHR, null);
                });
            },
            select: function (event, ui) {
                var lbl = ui.item.label;
                var val = ui.item.value;

                ui.item.value = lbl;
                $(this).attr('title', lbl).attr('data', val);

                $('#testResultCode').val(val);
            },
            minLength: 2
        });
    }

    $('#olis-modal-box').on('show.bs.modal', function (event) {

        var modal = $(this);
        var button = event.relatedTarget;
        //alert(button);
        var URL = $("#" + button.id).data('url');
        $.ajax({
            type: 'GET',
            url: URL
        })
         .done(function (data) {
             var modalbody = modal.find('.modal-body');
             modalbody.html(data);
         }).fail(function (jqXHR, textStatus, errorThrown) {
             alert('Error:' + errorThrown);
             checkAjaxError(jqXHR, null);
         });
    });

    $(document).on('submit', '#frm-olis-config', function (e) {
        e.preventDefault();
        var modalID = "#olis-modal-box";
        $("#ajax-loader").show();

        $.ajax({
            url: this.action,
            type: this.method,
            data: $(this).serialize(),
            success: function (result) {
                $("#ajax-loader").hide();
                hideModal(modalID);
                showNotificationMessage("success", result.message);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                checkAjaxError(jqXHR);
            }
        });
    });

    // ////////////////////////////////////////////////
    // dependancy: \cerebrum30\areas\labs\views\olis\practicedoctorslookup.cshtml

    var SDMVisibility = false;
    var lbl = 'Show';
    var isPreload = 9;
    $('#_swicthSDM').html(lbl);

    var removeItemFromList = function (list, arg) {
        var strArray = list.split(',');
        for (var i = 0; i < strArray.length; i++) {
            if (strArray[i] === arg) {
                strArray.splice(i, 1);
            }
        }
        return strArray;
    };

    var lockFormIfPreload = function (arg) {
        if (arg == isPreload) {
            $('#frm-OLISQuery input, #frm-OLISQuery select, #frm-OLISQuery textarea').not('#Query, #fromDate, #toDate, .btn-olis-search').each(
                function (idx) {
                    $(this).attr('disabled', true);
                }
            );
        }
        else {
            $('#frm-OLISQuery input, #frm-OLISQuery select, #frm-OLISQuery textarea').each(
                function (idx) {
                    $(this).attr('disabled', false);
                }
            );
        }
    };

    var showHidePreloadItem = function (arg) {
        // reset, and unlock
        $('#Query').get(0).selectedIndex = 0;
        lockFormIfPreload(-1);

        if (JSON.parse(arg)) {
            $('#Query option[value="' + isPreload + '"]').show();
        }
        else {
            $('#Query option[value="' + isPreload + '"]').hide();
        }
    };

    var OpenReportReviewOnResponse = function () {
        $(".olis-report-preview").each(function () {
            var URL = $(this).attr('href');
           // window.open(URL, "_blank");
        });
    }

    $(document).ready(function () {

        var fd = $('#frm-OLISQuery #fromDate').val();
        var tdat = $('#frm-OLISQuery #toDate').val();

        $('#frm-OLISQuery #fromDate').datetimepicker({ defaultDate: fd, format: 'YYYY-MM-DD HH:mm:ss' });
        $('#frm-OLISQuery #toDate').datetimepicker({ defaultDate: tdat, format: 'YYYY-MM-DD HH:mm:ss' });

        $('#requestingPractitioner').attr('readonly', 'readonly');
        searchPatientByName();
        SearchTestRequestCode();
        //SearchTestResultCode();

        SearchLabs('#performingLaboratorySearch', '#performingLaboratory');
        SearchLabs('#excludePerformingLaboratorySearch', '#excludePerformingLaboratory');
        SearchLabs('#reportingLaboratorySearch', '#reportingLaboratory');
        SearchLabs('#excludeReportingLaboratorySearch', '#excludeReportingLaboratory');



        $('#patient').on('keyup', function (e) {
            resetSearhValuesIfEmpty();
        });

        //$('.olis-report').on('click', function (e) {
        //    e.preventDefault();
        //    //var did = $(this).data('file');
        //    var report = $(this).data('report');
        //    var URL = $(this).attr('href') + '?report=' + report;//+ '&file=' + did;

        //    window.open(URL, '_blank');
        //});

        // --------------------------------
        $('#_swicthSDM').on('click', function (e) {// show hide SDM bottom panel - might have to maintain state
            e.preventDefault();
            if (SDMVisibility) {
                lbl = 'Show';
                SDMVisibility = false;
                $('#cnt-SDM').fadeOut('easeInOutExpo');
            }
            else {
                lbl = 'Hide';
                SDMVisibility = true;
                $('#cnt-SDM').fadeIn('easeInOutExpo');
            }
            $('#_swicthSDM').html(lbl);
        });

        var checkedItems = [];
        //showHidePreloadItem(false);
        $('#tbl-doc7 :checkbox').on('click', function () {
            var isSingleItem = function () {
                var checkItemsCount = $('#tbl-doc7 :checkbox').filter(':checked').length;

                var isSingle = {}
                isSingle.status = false;
                isSingle.hic = null;
                isSingle.hicState = null;

                if (checkItemsCount == 1) {
                    isSingle.status = true;
                    isSingle.hic = $('input:checked').data().hic;
                    isSingle.hicState = $('input:checked').data().state;
                }
                return isSingle;
            }

            if ($(this).is(':checked')) {
                checkedItems.push($(this).data().hic);

            }
            else {//remove
                var hicItem = $(this).data().hic;
                checkedItems = $.grep(checkedItems, function (el, idx) {
                    return el == hicItem;
                }, true);
            }

            //var s = isSingleItem();
            //if (s.status && (s.hicState).toUpperCase() == 'OFF') {//hide
            //    showHidePreloadItem(true);
            //}
            //else {
            //    showHidePreloadItem(false);
            //}

            $("#requestingPractitioner").val(checkedItems);
        });

        $('#Query').on('change', function () {
            lockFormIfPreload($(this).val());
        });

        //OpenReportReviewOnResponse();
        // --------------------------------
    });
</script>
