@model Cerebrum.ViewModels.OLIS.VMLaboratory
<div>
    <dl class="dl-horizontal">
        <dt class="olis-heading">@Model.LabFullLabel</dt>
        <dd class="dont-break-out"><strong> @Html.DisplayFor(model => (object)model.LabName)</strong> <span style="color:darkgrey"> (@Html.DisplayFor(model => (object)model.FacilityName) @Html.DisplayFor(model => (object)model.LabLicenseNo))</span></dd>
    </dl>
    @if (Model.Address != null)
    {
     @(await Html.PartialAsync("OLISAddress", (object)(object)Model.Address))
    }
</div>

