@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOlisReport>
@using Cerebrum30.Utility;
<div>
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" href="#collapse_filters">Quick Filters</a>
            </h4>
        </div>
        <div id="collapse_filters" class="panel-collapse collapse">
            
            <div class="panel-body">
                @{
                    var tests = Model.Select(s => s.TestCategories).ToList();
                    var reqtests = (from t in tests.SelectMany(m => m.SelectMany(ms => ms.RequestedTests)).ToList()
                                   group t by t.TestGroupOBR41 into tg
                                   select new { req = tg.FirstOrDefault() }).ToList();
                    var testcategoryies = tests.SelectMany(ns => ns.Select(md => md.TestCategory)).Distinct().ToList();
                }

                <div class="col-md-5">
                    @(await Html.PartialAsync("Filter_TestCategorysRequestedTest", (object)tests.SelectMany(f => f.Select(fs => fs)).ToList()))
                </div>
            </div>
            <div class="panel-footer"></div>
        </div>
    </div>
</div>
