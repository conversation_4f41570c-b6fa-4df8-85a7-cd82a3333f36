@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISRequestedTest>
@using Cerebrum30.Utility;

<div style="border-bottom: 5px solid #a2bfcb;"></div>
<table class="table olis-tbl" style="border: 0px solid #ff8800 !important">

    <tr style="color: #8598a0;" class="request-specimen-collapse panel-collapse collapse">
        @*<th></th>*@
        <th class="olis-heading">Specimen Type</th>
        <th class="olis-heading">Site Modifier</th>
        <th class="olis-heading">Collection Volumn</th>
        <th class="olis-heading">Collection DateTime</th>
        <th class="olis-heading">Number Of Sample Containers</th>
        <th class="olis-heading">Specimen Collected By</th>
        <th class="olis-heading">Parent Result</th>
        <th class="olis-heading test-sort-key no-print">
            Sort key
            <button class="pull-right btn btn-default btn-condensed hide-column" data-toggle="tooltip" data-placement="bottom" title="Hide Column">
                <i class="glyphicon glyphicon-eye-close"></i>
            </button>
        </th>
    </tr>

    @{ bool resultHeaderFlag = false;}
    @foreach (var item in Model.ToList())
    {
        <tr style="padding-top:35px" class="_234534564 A request-specimen-collapse panel-collapse collapse" data-sort-report-testRequest="@item.TestGroupOBR41" data-sort-report-testRequest-status="@item.testRequestStatus" data-filter-testrequest-specimentype="@item.SpecimenTypeOBR15" data-filter-testrequest-siteModifier="@item.SiteModifier" data-filter-testrequest-speciemCollectedBy="@item.SpecimenCollectedBy">
            @*<td></td>*@
            <td>@item.SpecimenTypeOBR15 </td>
            <td>@item.SiteModifier</td>
            <td>@item.CollectionVolumnOBR9</td>
            <td>@item.CollectionDateTimeOBR7</td>
            <td>@item.NumberOfSampleContainersOBR37</td>
            <td>@item.SpecimenCollectedBy
                <span class="olis-heading">Specimen Received Date/Time</span>@item.SpecimenReceivedDateTimeStr
            </td>
            <td>@item.ParentResult</td>
            <td class="test-sort-key no-print">@item.testRequestSortKey</td>
        </tr>


        <tr class="B" data-test-request-hide="@item.TestIdentifier">
            <td colspan="8" id="@item.TestIdentifier" class="filter_TestRequest" data-test-request-code="@item.TestIdentifier" data-test-request-status="@item.testRequestStatus">
                <h4>  <input class="no-print" name="olis-test-requests" type="checkbox" value="@item.TestIdentifier" /> 
                @Html.Raw(item.TestGroupWithStatus)</h4>
                @if (!string.IsNullOrWhiteSpace(item.pointOfCareTestIdentifierOBR30))
                {
                    <span style="background: #a2bfcb; display: table">(@item.pointOfCareTestIdentifierOBR30)</span>
                }
                <strong>Collection DateTime:</strong> @item.CollectionDateTimeOBR7
            </td>
        </tr>

        if (item.Diagnosis.Any())
        {
            foreach (var dig in item.Diagnosis)
            {
            <tr data-test-request-hide="@item.TestIdentifier">
                <td class="dont-break-out fixedWidthfont" colspan="9">
                    <span class="control-label olis-heading">Diagnosis:</span>   @Html.Raw(dig.FormatHL7Report())
                </td>
            </tr>
            }
        }
        if (!string.IsNullOrWhiteSpace(item.CollectorComment))
        {
        <tr data-test-request-hide="@item.TestIdentifier">
            <td class="dont-break-out fixedWidthfont" colspan="8">@Html.Raw(item.CollectorComment)</td>
        </tr>
        }
        if (!string.IsNullOrWhiteSpace(item.SpecimenComment))
        {
        <tr data-test-request-hide="@item.TestIdentifier">
            <td class="dont-break-out fixedWidthfont" colspan="8">@Html.Raw(item.SpecimenComment.FormatHL7Report())</td>
        </tr>
        }
        if (item.AncillaryResults != null && item.AncillaryResults.Any())
        {
            <tr data-test-request-hide="@item.TestIdentifier">
                <td>
                    @foreach (var ancr in item.AncillaryResults)
                    {
                        @(await Html.PartialAsync("_OBX11Z", (object)(object)ancr))
                    }
                </td>
            </tr>
        }
        if (item.Results != null && item.Results.Count() > 0)
        {
            <tr data-test-request-hide="@item.TestIdentifier">
            
                <td colspan="8" style="padding: 0px">
                    <table class="table Result-Table">
                        @if (!resultHeaderFlag)
                    {

                        { resultHeaderFlag = true; }

                    }
                        <tr>
                            <td class="td-parent-requested-test" data-parent-requested-test="@item.TestIdentifier" style="padding: 0px">
                                @(await Html.PartialAsync("RequestResults", (object)(object)item))
                            </td>
                        </tr>
                    </table>

                </td>
            </tr>

        }

        <tr><td colspan="8" style="border-bottom: 3px solid #b0acac;"><div>&nbsp;</div> </td></tr>   @*b0acac*@
    }@*\foreach*@



</table>
