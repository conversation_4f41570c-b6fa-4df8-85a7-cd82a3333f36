@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISTestCategory>

@if (Model.Any())
{
   
    <div class="tc-category-container">
        @foreach (var tc in Model)
        {
            <div class="panel-test-category" data-sort-category-name="@tc.TestCategory">
            <div class="panel-group" role="tablist" data-filter-test-category-hide="@tc.TestCategory">
                <div class="panel panel-default">
                    <div class="panel-heading" role="tab" id="headingOne">
                        <h4 class="panel-title hdr-lbl-hg">@tc.TestCategory</h4>
                    </div>
                    <div class="panel-body">
                        @(await Html.PartialAsync("RequestedTestsNoCategory", (object)tc.RequestedTests))
                    </div>
                </div>
            </div>
            </div>
        }
    </div>
}
