@model Cerebrum.ViewModels.OLIS.VMOLISOrderingFacility
@if (!string.IsNullOrWhiteSpace(Model.organizationName))
{
<div>
    <h4>Ordering Facility</h4>
    <hr />
    <dl class="dl-horizontal">
        <dd class="dont-break-out">@Html.DisplayFor(model => (object)model.organizationName)</dd>
        <dt>Address</dt>
        @(await Html.PartialAsync("OLISAddress", (object)(object)Model.Address))
    </dl>
    
</div>
}
