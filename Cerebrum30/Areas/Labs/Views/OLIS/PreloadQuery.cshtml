@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISPreloadSummary>

@{
    ViewBag.Title = "Preload Query";
    ViewBag.ModuleName = "Preload Query";
    //Layout = "~/Views/Shared/_Layout.cshtml";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
<div id="OLIS-preload-container" style="padding-top: 15px">
    <div class="container-fluid">
       @(await Html.PartialAsync("Preloads", (object)(object)Model))
    </div>
</div>
    <script>
        $(document).ready(function () {
            $('.olis-report').on('click', function (e) {
                e.preventDefault();
                var report = $(this).data('report');
                var URL = $(this).attr('href') + '?report=' + report;

                window.open(URL, '_blank');
            });
        });
    </script>
