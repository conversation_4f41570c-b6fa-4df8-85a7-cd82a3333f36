@model Cerebrum.ViewModels.OLIS.VMOlisPatient
<div>
    <div class="hdr-lbl-hg">Patient</div>
    <hr />
    @(await Html.PartialAsync("PatientID", (object)(object)Model.PatientIDs))
    <dl class="dl-horizontal">
   
        <dt class="olis-heading">
            Name
        </dt>
        <dd class="dont-break-out">
            @{
                var name = Model.PatientName;
            }
            @name.FullName
        </dd>
        
        <dt class="olis-heading">
            @Html.DisplayNameFor(model => model.Gender)
        </dt>

        <dd class="dont-break-out">
            @Html.DisplayFor(model => (object)model.Gender)
        </dd>
        <dt class="olis-heading">
            @Html.DisplayNameFor(model => model.DOB)
        </dt>

        <dd class="dont-break-out">
            @Html.DisplayFor(model => (object)model.DOB)
        </dd>
    </dl>
    @(await Html.PartialAsync("OLISPatientAddress", (object)(object)Model.Addresses))
    @(await Html.PartialAsync("OLISPhoneNumbers", (object)(object)Model.Phones))
    @(await Html.PartialAsync("OLISEmailAddresses", (object)(object)Model.Emails))
</div>

