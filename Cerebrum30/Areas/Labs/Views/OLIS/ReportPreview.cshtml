@model Cerebrum.ViewModels.OLIS.VMOLISPreviewReport
@{
    ViewBag.Title = "Report Preview";
    ViewBag.ModuleName = "Report Preview";
    //Layout = "~/Views/Shared/_LayoutBase.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<style>
    .clinicalViewer {
        display: none;
    }

    .clearBoth {
        clear: both;
    }

    .dl-horizontal dt {
        text-align: left;
    }

    dt {
        font-weight: normal;
    }

    .olis-tbl th {
        font-weight: normal;
    }

    .strikeText {
        text-decoration: line-through;
    }

    .resultrow {
        border-top: 3px solid #b0acac;
    }

    @@page {
        size: 8.5in 11in; /* width height */
    }

    @@media print {
        .no-print, .no-print * {
            display: none !important;
        }

        div#pagewidth {
            display: inline;
        }

        .report-header {
            page-break-after: always;
            padding-top: 2em;
        }

        h1:first-child {
            page-break-before: avoid;
            counter-reset: page;
        }



        #print-foot {
            display: block;
            position: fixed;
            bottom: 0pt;
            left: 0pt;
            font-size: 200%;
        }

            #print-foot:after {
                content: counter(page);
                counter-increment: page;
            }
    }
</style>
<script src="~/Areas/Labs/Scripts/OLIS_Preview_Report.js"></script>
@if (Model == null)
{
    <p class="red">Report not found</p>
}
else if (!Model.Reports.Any())
{
    <p>
        <h2 class="alert-danger">No results have been found in OLIS.</h2>
    </p>
}
else
{
    
    @(await Html.PartialAsync("OLISReportFilter", (object)(object)Model.Filter));
    
    <div>
        @{
            var reports = Model.Reports.ToList();
        }
        @(await Html.PartialAsync("ReportFilters", (object)(object)reports))
    </div>
    <div class="form-horizontal">
        <div class="form-group">
            <label class="col-md-2 control-label" for="name">Sort By:</label>
            <div class="col-md-10">
                <div class="btn-group btn-group-sm" role="group">
                    <button data-btn-value="patient-name" type="button" class="btn btn-default btn-patient-sort">Patient Name</button>
                    <button data-btn-value="patient-gender" type="button" class="btn btn-default btn-patient-sort">Gender</button>
                    <button data-btn-value="patient-hc" type="button" class="btn btn-default btn-patient-sort">Healthcard</button>
                    <button data-btn-value="patient-provider" type="button" class="btn btn-default btn-patient-sort">Provider</button>
                    <button data-btn-value="patient-provider-attending" type="button" class="btn btn-default btn-patient-sort">Attending Provider</button>
                    <button data-btn-value="patient-provider-admitting" type="button" class="btn btn-default btn-patient-sort">Admitting Provider</button>
                    <button data-btn-value="patient-provider-cced" type="button" class="btn btn-default btn-patient-sort">CCed Provider</button>
                    <button data-btn-value="patient-lab" type="button" class="btn btn-default btn-patient-sort">Performing Lab</button>
                    <button data-btn-value="patient-orderdate" type="button" class="btn btn-default btn-patient-sort">Report Date</button>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label" for="name">Show Patient :</label>
            <div class="col-md-10">
                <div class="btn-group patient-exists" role="group" aria-label="Basic example">
                    <button type="button" class="btn btn-secondary" data-show="all">All</button>
                    <button type="button" class="btn btn-secondary" data-show="exists">Exists in Cerebrum</button>
                    <button type="button" class="btn btn-secondary" data-show="not_exists">Not exists in Cerebrum</button>
                </div>
            </div>
        </div>
    </div>

    <div id="hrm-report-container" style="padding-top: 15px">
        <div id="wrapper7567945764">
            @if (Model != null && Model.Reports.Count > 0)
            {
                if (Model.Reports.Any(a => a.OLISPatientReportStatus == AwareMD.Cerebrum.Shared.Enums.OLISPatientReportStatus.Received))
                {
                <div class="panel-heading report-header">
                    <div id="save-all-report-panel">
                        <button data-URL="/Labs/OLIS/SaveAll" data-reportReceivedId="@Model.ReportId" data-save="1" data-markseen="false" type="button" class="btn btn-default btn-sm btn-primary btn-preview-report-panel btn-save-all-report no-print">Save All [@Model.Reports.Count()]</button>
                        <button data-URL="/Labs/OLIS/SaveAll" data-reportReceivedId="@Model.ReportId" data-save="1" data-markseen="true" type="button" class="btn btn-default btn-sm btn-primary btn-preview-report-panel btn-save-all-report no-print">Save & Mark Seen All [@Model.Reports.Count()]</button>
                    </div>
                    <button id="ctrl_cv" class="btn btn-default btn-sm clinicalViewer">Clinical Viewer</button>
                </div>
                <br />
                }
                foreach (var r in Model.Reports)
                {
                    var orderingDoctor = (r.Provider != null && r.Provider.OrderedBy != null) ? r.Provider.OrderedBy.FullName : "";
                    var reportingLab = (r.ReportDetails.PrimaryReportingLab != null) ? r.ReportDetails.PrimaryReportingLab.LabName : "";
                    var attending = r.AttendingProvider != null ? r.AttendingProvider.FullName : "";
                    var admitting = r.AdmittingProvider != null ? r.AdmittingProvider.FullName : "";
                    string found = r.Patient.ExistsInCerebrum ? "(Exists in Cerebrum)" : "(Not Exists in Cerebrum)";
                    string patientExists = r.Patient.ExistsInCerebrum ? "patients-exists-in-Cerebrum" : "patients-not-exists-in-Cerebrum";
                    string abnormal = r.AnyAbnormalResult ? $"[<span class='red'>Check for Abnormal Result</span>]" : "";
                    string patientHeader = $"{r.Id} - {r.Patient.PatientName.FullName} <strong> {found}</strong> {abnormal} <strong>HCN :</strong> {r.Patient.ONHealthNo} <strong>Gender :</strong> {r.Patient.Gender} <strong>DOB:</strong> {r.Patient.DOB}  <strong>Ordered By: </strong> {orderingDoctor} <strong>Reporting Lab:</strong>{reportingLab}";

                    var ccedDocs = string.Join("," ,r.CCList.OrderBy(o=>o.familyName2).Select(s => s.familyName2 ));

                    <div class="patient-panel-container" id="<EMAIL>"
                         data-sort-patient-report-provider="@r.Provider.OrderedBy.FullName"
                         data-sort-patient-attending-provider="@attending"
                         data-sort-patient-admitting-provider="@admitting"
                         data-sort-patient-name="@r.Patient.PatientName.FullName"
                         data-sort-patient-gender="@r.Patient.Gender"
                         data-sort-patient-lab="@r.ReportDetails.PrimaryPerformingLab.LabName"
                         data-sort-patient-orderdate="@r.ReportDetails.OrderDateTime"
                         data-sort-patient-hcn="@r.Patient.RawONHealthNo"
                         data-sort-patient-cced="@ccedDocs">
                        <div class="panel panel-default @patientExists clearBoth" id="@r.Id" data-sort-patient-Name="@r.Patient.PatientName.FullName" data-sort-patient-dob="@r.Patient.BirthDate" data-sort-patient-gender="@r.Patient.Gender" data-sort-patient-hcn="@r.Patient.ONHealthNo">
                            @using (Html.BeginForm("SaveReport", "OLIS", FormMethod.Post, new { name = "frmSave", id = "frmSave-" + @r.Id }))
                            {
                                <div class="panel-heading report-header">
                                    
                                    @if (r.OLISPatientReportStatus == AwareMD.Cerebrum.Shared.Enums.OLISPatientReportStatus.Received)
                                    {
                                    <div class="panel-footer">@Html.Raw(patientHeader)     
                                   
                                    </div>
                                    <div id="<EMAIL>">
                                        @if (r.TestCategories.Any(ar => ar.RequestedTests.Any(rs => rs.Results.Any())))
                                        {
                                            <button data-reportReceivedId="@r.ReportReceivedId" data-setid="@r.Id" data-filename="@r.fullFileString" data-save="1" data-markseen="false" type="button" class="btn btn-default btn-sm btn-primary btn-preview-report-panel btn-save no-print">Save</button>
                                            <button data-reportReceivedId="@r.ReportReceivedId" data-setid="@r.Id" data-filename="@r.fullFileString" data-save="1" data-markseen="true" type="button" class="btn btn-default btn-sm btn-primary btn-preview-report-panel btn-save no-print">Save & Mark Seen</button>
                                        }
                                        <button id="btn-reject" data-toggle="modal" data-target="#rejectReasonModal" data-panelid="@r.Id" data-reportReceivedId="@r.ReportReceivedId" data-pidsetid="@r.Id" type="button" class="btn btn-default btn-sm btn-preview-report-panel btn-reject no-print">Reject</button>
                                    </div>
                                    <button id="<EMAIL>" class="btn btn-default btn-sm clinicalViewer">Clinical Viewer</button>
                                    }
                                    else
                                    {
                                        <div class="panel-footer">@Html.Raw(patientHeader + " - " + r.OLISReportStatus + abnormal) </div>
                                    }

                                </div>

                                <div class="panel-body Patinet-Report" data-sort-patient-report-ORC4OrderId="@r.ReportDetails.ORC4OrderId" data-report-id="@r.Id">@(await Html.PartialAsync("ReportCompact", (object)(object)r))</div>
                                <div class="panel-footer">@Html.Raw(patientHeader)</div>

                            }
                        </div>
                    </div>


                }
              
            }
        </div>
    </div>
    <div class="modal fade" id="rejectReasonModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Reject Reason/Comment</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <form>
                            <label for="message-text" class="col-form-label">Comment:</label>
                            <textarea class="form-control" id="reason"></textarea>
                            <input type="hidden" id="reportreceivedid" />
                            <input type="hidden" id="pidsetid" />
                            <input type="hidden" id="panelid" />
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="modalClose" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" id="modalProceed" class="btn btn-primary">Reject</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="olis-modal-box" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Summary</h4>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>

    <br />
    <script type="text/javascript">

        $('.patient-exists > button.btn').on("click", function () {

            var dt = $(this).data("show");
            console.log(dt);
            if (dt === 'exists') {
                $(".patients-exists-in-Cerebrum").show();
                $(".patients-not-exists-in-Cerebrum").hide();
            } else if (dt === 'not_exists') {
                $(".patients-exists-in-Cerebrum").hide();
                $(".patients-not-exists-in-Cerebrum").show();
            } else {
                $(".patients-not-exists-in-Cerebrum").show();
                $(".patients-exists-in-Cerebrum").show();
            }

        });

        $('#rejectReasonModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);

            var panelid = button.data('panelid');
            var reportreceivedid = button.data('reportreceivedid');
            var pidsetid = button.data('pidsetid');
            

            var modal = $(this);
            modal.find('#reason').val('');
            modal.find('.modal-body #reportreceivedid').val(reportreceivedid);
            modal.find('.modal-body #pidsetid').val(pidsetid);
            modal.find('.modal-body #panelid').val(panelid);
        });

        $('#modalProceed').on('click', function (event) {
            //console.log("Hello Reject");

            var modal = $("#rejectReasonModal");

            var reasonComment = modal.find('#reason').val();
            var reportreceivedid = modal.find('#reportreceivedid').val();
            var pidsetid = modal.find('#pidsetid').val();
            var panelid = modal.find('#panelid').val();

            var URL = '/Labs/OLIS/RejectOLISReport'
            var postdata = { reportReceivedId: reportreceivedid, pid_setid: pidsetid, reason: reasonComment };
            console.log(postdata);
            $.ajax({
                url: URL,
                type: 'POST',
                data: postdata
            }).done(function (resp) {
                $('#rejectReasonModal').modal('toggle');
                modal.find('#reason').val('');
                $("#" + panelid).remove();
            });
        });


        var getDomainWithProtocolandPort = function () {
            return window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
        };

        $(document).ready(function () {

            $(document).on('click', '#btn-olis-rpt-filter', function (e) {
                e.preventDefault();
                $('#ajax-loader').show();
                $('#frm-olis-rpt-filter').submit();
            });

            SearchTestRequestCode('#TestRequestName', '#TestRequestCode');
            SearchTestResultCode('#TestResultName', '#TestResultLOINC');

            SearchLabs('#LabName', '#LabLicense');

            $(document).on('click', '.btn-patient-sort', function (e) {

                e.preventDefault();
                var btn = $(this);
                var btnValue = btn.data('btn-value');
                var sortDataAttribute = '';

                switch (btnValue) {
                    case 'patient-name':
                        sortDataAttribute = 'sort-patient-name';
                        break;
                    case 'patient-dob':
                        sortDataAttribute = 'sort-patient-dob';
                        break;
                    case 'patient-gender':
                        sortDataAttribute = 'sort-patient-gender';
                        break;
                    case 'patient-hc':
                        sortDataAttribute = 'sort-patient-hcn';
                        break;
                    case 'patient-lab':
                        sortDataAttribute = 'sort-patient-lab';
                        break;
                    case 'patient-provider':
                        sortDataAttribute = 'sort-patient-report-provider';
                        break;
                    case 'patient-provider-attending':
                        sortDataAttribute = 'sort-patient-provider-attending';
                        break;
                    case 'patient-provider-admitting':
                        sortDataAttribute = 'sort-patient-provider-admitting';
                        break;
                    case 'patient-orderdate':
                        sortDataAttribute = 'sort-patient-orderdate';
                        break;
                    case 'patient-provider-cced':
                        sortDataAttribute = 'sort-provider-cced';
                        break;
                        
                    default:
                        break;
                }

                if (sortDataAttribute != null && sortDataAttribute != '') {
                    var panelsContainer = $('#wrapper7567945764');
                    sortPanels(panelsContainer, 'patient-panel-container', sortDataAttribute)
                }

            });

            $(document).on('click', '.btn-category-sort', function (e) {

                e.preventDefault();
                var btn = $(this);
                var btnValue = btn.data('btn-value');
                var sortDataAttribute = '';

                switch (btnValue) {
                    case 'tc-category':
                        sortDataAttribute = 'sort-category-name';
                        break;
                    default:
                        break;
                }

                if (sortDataAttribute != null && sortDataAttribute != '') {
                    var panelsContainer = btn.closest('.requested-tests').first().find('.tc-category-container').first();
                    sortPanels(panelsContainer, 'panel-test-category', sortDataAttribute)
                }

            });

            $(document).on('click', '.sort-column', function (e) {
                e.preventDefault();

                var column = $(this);
                var columnIndex = column.index();
                var table = column.closest('.tbl-request-results').first();
                var dataAttribute = 'sort-test-result';
                sortTable(table, columnIndex, dataAttribute);
            });

            //main filter parent
            $(document).on('change', '.cl-test-category-filter', function (e) {
                e.preventDefault();

                var cb = $(this);
                var filterId = cb.data('test-category-filter-id');
                var panelGroups = $('#hrm-report-container').find('.panel-group[data-filter-test-category-hide="' + filterId + '"]');

                if (cb.is(":checked")) {
                    panelGroups.show();
                }
                else {
                    panelGroups.hide();
                }
            });

            //first child level
            $(document).on('change', '.cl-test-request-filter', function (e) {
                e.stopPropagation();
                e.preventDefault();

                var cb = $(this);
                var testId = cb.data('test-request-filter-id');
                var tableRows = $('.olis-tbl').find('tr[data-test-request-hide="' + testId + '"]');

                if (cb.is(":checked")) {
                    tableRows.show();
                }
                else {
                    tableRows.hide();
                }
            });

            //second child level
            $(document).on('change', '.cl-test-result-loinc-filter', function (e) {
                e.stopPropagation();
                e.preventDefault();

                var cb = $(this);
                var loincId = cb.data('filter-test-result-loinc');
                var tableRows = $('#hrm-report-container').find('tr[data-test-result-status="' + loincId + '"]');

                if (cb.is(":checked")) {
                    tableRows.show();
                }
                else {
                    tableRows.hide();
                }
            });
            $('#dropdown-Patients').on('change', function () {
                var tid = $(this).val();
                var ele = document.getElementById(tid);
                //window.scrollTo(ele.offsetLeft, ele.offsetTop);
            });
            $('#dropdown-tests').on('change', function () {
                var tid = $(this).val();
                var ele = document.getElementById(tid);
                // window.scrollTo(ele.offsetLeft, ele.offsetTop);
            });

            $('.btn-save-all-report').on('click', function (e) {
                e.preventDefault();
                $('#ajax-loader').show();
                
                var URL = $(this).data("url");
                //console.log($(this).data());

                var reportReceivedId_ = $(this).data("reportreceivedid");

                //console.log(reportReceivedId_);
                var savereportModal = $("#olis-modal-box");
                $("#olis-modal-box .close").click(function () {
                    window.close();
                });
                var markseen_ = $(this).data("markseen");
                var postdata = { reportReceivedId: reportReceivedId_, markseen: markseen_ };
                $.ajax({
                    url: URL,
                    type: 'POST',
                    data: postdata
                }).done(function (data) {
                    $('#ajax-loader').hide();                                       
                    $("save-all-report-panel").hide();
                    var modalbody = savereportModal.find('.modal-body');
                    modalbody.html(data);
                    savereportModal.modal();
                   
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    checkAjaxError(jqXHR, null);
                });
            });

            $('.btn-save').on('click', function (e) {
                e.preventDefault();
                $('#ajax-loader').show();
                var dt = $(this).data();
                var setid = $(this).data("setid");
                var markseen_ = $(this).data("markseen");
                
                var reportReceivedid = $(this).data("reportreceivedid");
                var URL = '/labs/OLIS/SaveOLISReport';

                var testResults = {};
                $('input:checked[name="olis-test-results"]').each(function (index) {
                    var cb = $(this);
                    var tdRequestedTest = cb.closest('.td-parent-requested-test').first();
                    var tdRequestedValue = tdRequestedTest.data('parent-requested-test');

                    testResults[index] = cb.val();
                    $('input[name="olis-test-requests"][value="' + tdRequestedValue + '"]').prop("checked", true);
                });

                var requestedTests = {};
                $('input:checked[name="olis-test-requests"]').each(function (index) {
                    requestedTests[index] = $(this).val();
                });

                var postdata = [];
                if (dt.save == '1') {
                    postdata = { reportReceivedId: reportReceivedid, pid_set: dt.setid, filename: dt.filename, requestedtests: requestedTests, results: testResults, markseen: markseen_ };
                } else if (dt.save = '0') {
                    postdata = { reportReceivedId: reportReceivedid, pid_set: dt.setid };
                    URL = '/labs/OLIS/RejectOLISReport/';
                    //alert('Reject: '+JSON.stringify(postdata));
                }
                
                $.ajax({
                    url: URL,
                    type: 'POST',
                    data: postdata
                })
                .done(function (data) {
                    
                    $('#ajax-loader').hide();

                    if (data.Success) {
                        $("#save-report-panel-" + setid).hide();                       
                        if (data.ReportId > 0) {
                            $('#ctrl_cv_' + setid).show();
                            $('#ctrl_cv_' + setid).attr('data-link', data.ReportId);
                        }
                        showNotificationMessage("success", data.Message);
                    } else if (data.ReportAlreadyExists) {
                        showMessageModal("warning", data.Message, false);
                    }
                    else {
                        showMessageModal("error", data.Message, false);
                    }
                    
                }).fail(function (jqXHR, textStatus, errorThrown) {                    
                    checkAjaxError(jqXHR, null);
                });


            });

            $('.hide-column').click(function (e) {
                var $btn = $(this);
                var $cell = $btn.closest('th,td');
                var $table = $btn.closest('table');

                var cellIndex = $cell[0].cellIndex + 1;

                $table.find(".show-column-footer").show();
                $table.find("tbody tr, thead tr")
                      .children(":nth-child(" + cellIndex + ")")
                      .hide();
            });

            $(".show-column-footer").click(function (e) {
                var $table = $(this).closest('table');
                $table.find(".show-column-footer").hide();
                $table.find("th, td").show();

            });
        });

        $(document).on('click', '.clinicalViewer', function (e) {
            e.preventDefault();
            var url = getDomainWithProtocolandPort() + '/Labs/OLIS/clinicalviewer/' + $(this).attr('data-link');
            window.open(url, '_blank');
        });

        function sortPanels(panelContainer, panelClass, dataAttribute) {

            var panels = panelContainer.find('.' + panelClass).toArray().sort(comparerPanel(dataAttribute));
            this.asc = !this.asc;
            if (!this.asc) { panels = panels.reverse() };
            for (var i = 0; i < panels.length; i++) { panelContainer.append(panels[i]) };

        }

        function comparerPanel(dataAttribute) {
            return function (a, b) {
                var valA = getPanelDataAttribuleValue(a, dataAttribute), valB = getPanelDataAttribuleValue(b, dataAttribute);
                return isNumber(valA) && isNumber(valB) ? valA - valB : valA.localeCompare(valB);
            }
        }
        function getPanelDataAttribuleValue(row, dataAttribute) {
            var val;
            if (dataAttribute != null && dataAttribute != '' && dataAttribute.length > 0) {
                val = $(row).data(dataAttribute);
            }

            if (val == null || val == undefined)
                val = '';

            return val;
        };

    </script>
            }