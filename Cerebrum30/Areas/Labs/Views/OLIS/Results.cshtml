@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISResult>
@using Cerebrum30.Utility;
@{
    var Results = Model.Where(w=>w.ResultStatus != AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.Z).OrderBy(o => o.testRequestSortKeyZBR11).ToList(); ;
    string flagheader = string.Empty;
    bool anyflag = Model.ToList().All(a => string.IsNullOrWhiteSpace(a.Flag));
    bool flag = Model.ToList().Count() == Model.ToList().Count(a => (string.IsNullOrWhiteSpace(a.Flag)) || a.Flag.Equals("N"));
    if (flag)
    {
        flagheader = "text-primary";
    }
    else
    {
        flagheader = "text-danger";
    }

}

        <table class="table">
            @*style="border: 3px solid #b0acac !important"*@
            <tr style="color:#a4a27b;">
                <!-- 11 columns -->
                <th style="">TestName</th>
                <th style="width:15%">Result</th>
                <th style="width:5%" class="@flagheader">
                    @if (!anyflag)
                    {
                        <span>Flag</span>
                    }
                </th>
                <th style="width:5%">ReferenceRange</th>
                <th style="width:5%">Units</th>
                @*<th style="width:5%">Result Status</th>*@
                <th style="width:10%">NatureOfAbnormal</th>
                <th style="width:5%">ResultDateTime</th>
                <th style="width:10%">ObservationMethod</th>
                <th style="width:5%">Block Consent </th>
                <th style="width:5%" class="test-result-sort-key">
                    --
                    <button class="pull-right btn btn-default btn-condensed hide-column" data-toggle="tooltip" data-placement="bottom" title="Hide Column">
                        <i class="glyphicon glyphicon-eye-close"></i>
                    </button>
                </th>
            </tr>
            
            @foreach (var item in Results)
            {
                string cls = item.ResultStatus == AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.W ? "strikeText" : "";
                <tr class="filter_test_results resultrow" data-filter-resultLOINC="@item.LOINC" data-test-result-flag="@item.Flag">
                    <td class="idx_1">
                        <input class="no-print" name="olis-test-results" type="checkbox" value="@item.LOINC" /><span class=""> @Html.Raw(item.TestNameWithStatus)</span>
                    </td>
                    @if (item.ValueType.Equals("FT") || item.ValueType.Equals("TX") || ( item.ValueType.Equals("ST") && ((string.IsNullOrWhiteSpace(item.ReferenceRange) && string.IsNullOrWhiteSpace(item.Units)))))
                    {
                        <td colspan="7" class="dont-break-out fixedWidthfont fixedWidthChr">@Html.Raw(item.Result.FormatHL7Report())</td>
                        <td>@item.ResultBlockConsent</td>
                        <td class="test-result-sort-key">@item.testRequestSortKeyZBR11</td>
                    }
                    else if (item.ValueType.Equals("ED"))
                    {
                        <td colspan="7" class="dont-break-out fixedWidthfont"><a target="_blank" href="@item.AttachedReport.URL" class="glyphicon glyphicon-file"></a></td>
                        <td>@item.ResultBlockConsent</td>
                        <td class="test-result-sort-key">&nbsp;</td>
                    }
                    else
                    {
                        var flagcss = ((string.IsNullOrWhiteSpace(item.Flag)) || item.Flag.Equals("N")) ? "text-primary" : "text-danger";
                        if (item.ResultStatus == AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.P)
                        {
                            flagcss = "text-danger";
                        }
                        var resultcss = (new AwareMD.Cerebrum.Shared.Enums.OLISResultStatus[] { AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.F }).Contains(item.ResultStatus) ? "text-primary" : "text-danger";
                        <td class="@flagcss _234234 dont-break-out fixedWidthfont">@Html.Raw(item.Result.FormatHL7Report())</td>

                        <td class="@flagcss @cls">
                            @if (!anyflag) 
                            { 
                                @Html.Raw(item.Flag)
                            }
                        </td>

                        <td class="@flagcss @cls">@Html.Raw(item.ReferenceRange)</td>
                        <td class="@flagcss @cls">@Html.Raw(item.Units)</td>
                        @*<td class="@resultcss">@Html.Raw(item.ResultStatusOBX11)</td>*@
                        <td>@item.NatureOfAbnormal</td>
                        <td>@item.ResultDateTime</td>
                        <td>@item.ObservationMethod</td>
                        <td>@item.ResultBlockConsent</td>
                        <td class="test-result-sort-key">@item.testRequestSortKeyZBR11</td>
                    }
                </tr>
                
                if (!string.IsNullOrWhiteSpace(item.SpecimenResultComment))
                {
                    <tr>
                        <td class="dont-break-out fixedWidthChr" colspan="10">@Html.Raw(item.SpecimenResultComment.FormatHL7Report())</td>
                    </tr>
                }
                if (item.SubResult != null)
                {
                    <tr>
                        <td>&nbsp;</td>
                        <td class="dont-break-out" colspan="10">
                            @(await Html.PartialAsync("ResultAntibiotics", (object)item.SubResult))
                        </td>
                    </tr>
                }
            }
        </table>
