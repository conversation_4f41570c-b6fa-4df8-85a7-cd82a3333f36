@*@model IEnumerable<Cerebrum30.Areas.Daysheet.Mappers.ViewModels.VMAppointmentItem>

<table class="table">
    <tr>
        <th style="width:25%;">
            Appointment
        </th>
        <th style="width:15%;">
            Tests
        </th>
        <th style="width:5%;"> 
            
        </th>
        <th style="width:25%;">
            Patient
        </th>
        <th style="width:5%;">
            VP
        </th>
        <th style="width:10%;">
            Billing
        </th>
        <th style="width:15%;">
            Referral Doctor
        </th>       
    </tr>

@foreach (var item in Model) {
    
    @(await Html.PartialAsync("_appointmentItem", (object)(object)item))
}

</table>*@
