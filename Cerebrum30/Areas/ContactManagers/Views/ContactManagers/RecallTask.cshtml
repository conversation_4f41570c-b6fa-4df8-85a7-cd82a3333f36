@model Cerebrum.ViewModels.ContactManagerNew.ContactManagerNewTaskViewModel

<script type="text/javascript" src="~/Areas/ContactManagers/Scripts/RecallTask.js"></script>
<script type="text/javascript">
        var recallPatientRecordIds = "@Model.patientRecordId";
        var practiceDoctorId = "@Model.practiceDoctorId";

        var userTypes = [];
        var users = [];
        var preSelectedRecipients = "";
        @if (string.IsNullOrEmpty(Model.errorMessage))
        {
            if (Model.userTypes != null)
            {
                foreach (var userType in Model.userTypes)
                {
                    @:userTypes.push({ value: "@userType.value", text: "@userType.text" });
                }
            }
            if (Model.users != null)
            {
                foreach (var user in Model.users)
                {
                    @:users.push({userType: "@user.userType.ToString()", officeId: "@user.officeId", userName: "@user.name", userId: "@user.id"});
                }
            }
        }
</script>

<div class="modal-dialog" role="document" style="width: 800px;">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">Send Tasks</h4>
        </div>
        <div class="modal-body ">
            <div class="row" style="font-size: 12px;">
                <div class="col-sm-6 form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Due Date
                        </label>
                        <div class="col-sm-8" style="padding-left: 0px; padding-right: 0px;">
                            <input type="text" class="form-control" id="contactManagerDueDate" name="contactManagerDueDate" value="@DateTime.Now.ToString("MM'/'dd'/'yyyy")" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Urgency
                        </label>
                        <div class="col-sm-8" style="padding-left: 0px; padding-right: 0px;">
                            @Html.DropDownList("contactManagerUrgency", new SelectList(Model.urgencies, "value", "text"), htmlAttributes: new { @class = "form-control " })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Subject
                        </label>
                        <div class="col-sm-8" style="padding-left: 0px; padding-right: 0px;">
                            <input type="text" id="contactManagerSubject" name="contactManagerSubject" class="form-control" value="" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Message
                        </label>
                        <div class="col-sm-8" style="padding-left: 0px; padding-right: 0px;">
                            <textarea id="contactManagerMessage" name="contactManagerMessage" required rows="8" class="form-control" style="height: auto;"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Send email
                        </label>
                        <div class="col-sm-8" style="padding-top: 6px; padding-left: 0px;">
                            <input type="checkbox" id="contactManagerEmail" name="contactManagerEmail" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">
                            Office
                        </label>
                        <div class="col-sm-8" style="padding-left: 0px;">
                            @Html.DropDownList("contactManagerOffice", new SelectList(Model.offices, "value", "text"), "All offices", htmlAttributes: new { @class = "form-control " })
                        </div>
                    </div>
                    <div class="form-group" style="padding-right: 15px;">
                        <label class="col-sm-4 control-label">
                            Recipients
                        </label>
                        <div id="contactManagerRecipient" name="contactManagerRecipient" class="col-sm-8" style="height: 256px; padding-left: 0px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="col-md-6 col-md-offset-6">
                <button class="btn btn-default btn-sm" style="width: 64px;" id="btn-recall-task-send" onclick="sendTasks();">Send</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px;">Cancel</button>
            </div>
        </div>
    </div>
</div>
