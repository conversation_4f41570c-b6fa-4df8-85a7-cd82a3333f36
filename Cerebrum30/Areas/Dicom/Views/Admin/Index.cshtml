
@{
    ViewBag.Title = "Dicom Study";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<script src="~/Areas/Dicom/Scripts/c3-dicom-study.js"></script>
<h2>Manage Dicom Exam</h2>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4> Step 1 : Find DICOM Exam to move</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("FindDicomExam", (object)new { area = "Dicom" }))
            </div>
        </div>
        
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4> Step 2 : Find Appointment / Test to move</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("FindAppointment", (object)new { area = "Dicom" }))
            </div>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4> Step 3 : Attach/Reattach Exam to correct test</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("AttachReattachDicomExam", (object)new { area = "Dicom" }))
            </div>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4> Step 4 : Import Dicom Measurements</h4> </div>
            <div class="panel-body">
                @Html.RenderPartialAsync("ImportDicomMeasurements");}
            </div>
        </div>
    </div>
</div>
<hr />
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4> Extract Dicom Measurements from SR file</h4> </div>
            <div class="panel-body">
                @Html.RenderPartialAsync("UploadDICOMSRFile");}
            </div>
        </div>
    </div>
</div>
<div id="dicom-file-measurements">

</div>
<br />
<br />

