@model IEnumerable<Cerebrum.ViewModels.RadDicom.VMDicomFile>

@{ int count = 0;}
@foreach (var item in Model)
{
    ++count;
    <div class="panel-group">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" href="#collapse-@count" >Measurement List#@count</a>
                </h4>
            </div>
            <div id="collapse-@count" class="panel-collapse collapse">
                @{ await Html.RenderPartialAsync("_dicomStudyHeaderInfo", (object)item); }
            </div>
        </div>
    </div>
                       
 }