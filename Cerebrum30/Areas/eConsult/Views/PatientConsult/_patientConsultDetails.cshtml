@model Cerebrum.ViewModels.Econsult.Models.MetadataConsult
@{
    var header = "Consult Details";
}

@Html.ModalHeader((object)header)
<table class="table table-condensed table-responsive borderless">
    <tr>
        <td class="col-md-4" style="vertical-align:top;">
            @(await Html.PartialAsync("_patientConsultInfoMain", (object)(object)Model))
        </td>
        <td class="col-md-8" style="vertical-align:top;">
            <table class="table-no-pointer">
                @(await Html.PartialAsync("_patientConsultInfoNotes", (object)(object)Model))
            </table>
        </td>
    </tr>
</table>

<div class="modal-footer">
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>
</div>