@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
    @{ 
        var status = Model.ConsultStatus.ToLower();
    }
<script src="~/Areas/eConsult/Scripts/upload-file-common.js"></script>
<input type="hidden" id="hdCaseId" value="@Model.CaseId" />
<input type="hidden" id="hdClinicFileIds" value="" />
<input type="hidden" id="hdClinicFileNames" value="" />
<input type="hidden" id="hdClinicFileUrls" value="" />
<input type="hidden" id="hdClinicOfficeIds" value="" />

<table class="table table-condensed table-responsive borderless">
    <tr>
        <td class="col-md-4" style="vertical-align:top;">
            @(await Html.PartialAsync("_consultInfoMain", (object)(object)Model))
        </td>
        <td class="col-md-8" style="vertical-align:top;">
            <table class="table-no-pointer">
                <tr>
                    <td class="col-md-6 align-text-top" style="vertical-align:top;">
                        <div class="row">
                            <div class="col-md-12 div-note" style="margin-bottom:10px;">
                                <div id="accordion-waiting-for-response" class="accordion">
                                    <h3>Add Note</h3>
                                    <div>
                                        <textarea class="form-control consult-note" id="waiting-for-response-add-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                        <div style="margin-top:6px;" id="div-waiting-for-response-add-note-spanError">
                                            <span id="waiting-for-response-add-note-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnAddNote" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                            <br />
                                            <label for="FilesAddNote" class="custom-file-upload">
                                                <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                            </label>
                                            <input style="margin-top:3px" type="file" id="FilesAddNote" name="FilesAddNote" class="form-control-file generate-description-box" multiple />
                                            @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                            {
                                                <label for="btn" class="custom-file-upload">
                                                    <input type="button" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                </label>
                                            }
                                        </div>
                                        <div class="col-md-12 text-left div-upload-file-description" id="div-FilesAddNote"></div>
                                        <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files"></div>
                                    </div>
                                    @if (status == "submitted" || status == "unassigned")
                                    {
                                        <h3>Re-Assign</h3>
                                        <div>
                                            <div style="margin-top:6px;">
                                                Are you sure you want to re-assign the case to a different recipient?
                                                <br /><br />
                                                <h5>The initial case data will be copied into a new draft. You will have an opportunity to review it and select a new recipient before submitting draft.</h5>
                                                <br />
                                                <h5>The current case will be cancelled.</h5>
                                                <input type="button" class="btn btn-primary pull-right" id="btnReAssign" value="Re-Assign" />
                                            </div>
                                        </div>
                                    }
                                    <h3 id="tabCancel">Cancel</h3>
                                    <div>
                                        <div style="margin-bottom:6px;">
                                            Are you sure you want to cancel the case?
                                        </div>
                                        <textarea class="form-control" id="consult-cancel-note" rows="5" placeholder="Add note..." maxlength="4000"></textarea>
                                        <div style="margin-top:6px;">
                                            <span id="consult-cancel-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" style="min-width:80px;" id="btnCancelConsult" value="Confirm Cancel" /><input type="button" class="btn btn-default pull-right" style="min-width:80px;" id="btnDiscardCancel" value="Abort Cancel" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                @(await Html.PartialAsync("_consultInfoNotes", (object)(object)Model))
            </table>
        </td>
    </tr>
</table>
<br /><br />
