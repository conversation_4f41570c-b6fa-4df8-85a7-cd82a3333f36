@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
<script src="~/Areas/eConsult/Scripts/upload-file-common.js"></script>

<input type="hidden" id="hdCaseId" name="hdCaseId" value="@Model.CaseId" />
<input type="hidden" id="hdMainAnswer" />
<input type="hidden" id="hdExtAnswer1" />
<input type="hidden" id="hdExtAnswer2" />
<input type="hidden" id="hdValidTimeSpent" value="false" />
<input type="hidden" id="hdClinicFileIds" value="" />
<input type="hidden" id="hdClinicFileNames" value="" />
<input type="hidden" id="hdClinicFileUrls" value="" />
<input type="hidden" id="hdClinicOfficeIds" value="" />

<table class="table table-condensed table-responsive borderless">
    <tr>
        <td class="col-md-4" style="vertical-align:top;">
            @(await Html.PartialAsync("_consultInfoMain", (object)(object)Model))
        </td>
        <td class="col-md-8" style="vertical-align:top;">
            <table class="table-no-pointer">
                @{ 
                if (Model.ConsultStatus != "Completed")
                {
                    <tr>
                        <td class="col-md-6 align-text-top" style="vertical-align:top;">
                            <div class="row">
                                <div class="col-md-12 div-note" style="margin-bottom:10px;">
                                    <div id="accordion-consult-provided-add-note" class="accordion">
                                        <h3>Add Note</h3>
                                        <div>
                                            <textarea class="form-control consult-note" id="consult-provided-add-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                            <div style="margin-top:6px;" id="div-consult-provided-add-note-spanError">
                                                <span id="consult-provided-add-note-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnConsultProvidedAddNote" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                                <br />
                                                <label for="FilesConsultProvidedAddNote" class="custom-file-upload">
                                                    <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                                </label>
                                                <input style="margin-top:3px" type="file" id="FilesConsultProvidedAddNote" name="FilesConsultProvidedAddNote" class="form-control-file generate-description-box" multiple />
                                                @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                                {
                                                    <label for="btn" class="custom-file-upload">
                                                        <input type="button" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                    </label>
                                                }
                                            </div>
                                            <div class="col-md-12 text-left div-upload-file-description" id="div-FilesConsultProvidedAddNote"></div>
                                            <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    }
                }
                @(await Html.PartialAsync("_consultInfoNotes", (object)(object)Model))
            </table>
        </td>
    </tr>
</table>
<br /><br />
