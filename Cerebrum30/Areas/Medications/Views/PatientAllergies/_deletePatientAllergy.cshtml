@model Cerebrum.ViewModels.Medications.VMPatientAllergyInt


@using (Html.BeginForm("deletepatientallergy", "patientallergies", new { area = "medications" }, FormMethod.Post))
{
    @Html.Modal<PERSON>eader("Delete Patient Allergy")
    <div class="modal-body">
        <h3>Are you sure you want to delete this?</h3>
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)

        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.MedicationName)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.MedicationName)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Severity)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.ReactionType)
            </dd>            

            <dt>
                @Html.DisplayNameFor(model => model.DateStarted)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.DateStarted)
            </dd>

        </dl>


    </div><!--Modal body end-->
    @Html.ModalFooter("Delete", "red")
}


