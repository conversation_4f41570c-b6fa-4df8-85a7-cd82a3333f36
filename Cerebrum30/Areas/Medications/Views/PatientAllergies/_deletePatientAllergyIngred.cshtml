@model Cerebrum.ViewModels.Medications.VMPatientAllergyIngredient


@using (Html.BeginForm("DeleteAllergyIngredient", "patientallergies", new { area = "medications" }, FormMethod.Post))
{
    @Html.ModalHeader("Delete Allergic Ingredient")
    <div class="modal-body">
        <h3>Are you sure you want to delete this?</h3>
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)

        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.Ingredient)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.Ingredient)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.IngredientCode)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.IngredientCode)
            </dd>            
                       

        </dl>


    </div><!--Modal body end-->
    @Html.ModalFooter("Delete", "red")
}


