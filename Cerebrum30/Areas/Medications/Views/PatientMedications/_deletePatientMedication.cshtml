@model Cerebrum.ViewModels.Medications.VMPatientMedication

@using (Html.BeginForm("deletepatientmedication", "patientmedications", new { area = "medications" }, FormMethod.Post))
{
    @Html.ModalHeader("Delete Medication")
    <div class="modal-body">
        <h3>Are you sure you want to delete this?</h3>
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)        

        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.MedicationName)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.MedicationName)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.DIN)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.DIN)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Dose)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.Dose)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.SIG)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.SIG)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Route)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.Route)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.DateStarted)
            </dt>

            <dd>
                @Html.DisplayFor(model => (object)model.DateStarted)
            </dd>

        </dl>

       
    </div><!--Modal body end-->
    @Html.ModalFooter("Delete","red")
}

