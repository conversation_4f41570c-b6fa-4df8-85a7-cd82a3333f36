@model Cerebrum.ViewModels.Medications.VMPatientMedCreate

@{
    var canPrescribe = CerebrumUser.HasPermission("WritePrescription");
    var disableRXbtns = canPrescribe ? "btn-med-create" : "disabled";
}

@using (Html.BeginForm("createpatientmedication", "patientmedications", new { area = "medications" }, FormMethod.Post, true, new { @id= "frm-med-create" }))
{
    @Html.ModalHeader("Add from template")
    <div class="modal-body">        
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.PatientId)
        @Html.HiddenFor(model => (object)model.MedicationNoDinId)
        @Html.HiddenFor(model => (object)model.MedicationName)
        @*@Html.HiddenFor(model => (object)model.MedicationRoute)*@
        @*@Html.HiddenFor(model => (object)model.SIG)
        @Html.HiddenFor(model => (object)model.Mitte)
        @Html.HiddenFor(model => (object)model.MitteUnitId)
        @Html.HiddenFor(model => (object)model.Repeats)*@
        @Html.HiddenFor(model => (object)model.DIN)
        @Html.HiddenFor(model => (object)model.IsDin)
        @Html.HiddenFor(model => (object)model.Form)
        @Html.HiddenFor(model => (object)model.Ingredients)
        @Html.HiddenFor(model => (object)model.Classes)
        @Html.HiddenFor(model => (object)model.Instructions)
        @Html.HiddenFor(model => (object)model.SelectionType)
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MedicationName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <p class="form-control-static">
                        @Html.DisplayFor(model => (object)model.MedicationName)
                    </p>
                </div>
            </div>
            @if (Model.IsDin)
            {
                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.Ingredients, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        <p class="form-control-static">
                            @Html.DisplayFor(model => (object)model.Ingredients)
                        </p>
                    </div>
                </div>
            }
            <div class="form-group form-group-sm">
                <label class="control-label col-md-2">Start Date</label>
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DateStartedDay, new { htmlAttributes = new { @class = "form-control pull-left custom-input-xxs" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">-</div>
                    @Html.ValidationMessageFor(model => model.DateStartedDay, "", new { @class = "text-danger" })

                    @Html.EditorFor(model => model.DateStartedMonth, new { htmlAttributes = new { @class = "form-control pull-left custom-input-xxs" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">-</div>
                    @Html.ValidationMessageFor(model => model.DateStartedMonth, "", new { @class = "text-danger" })

                    @Html.EditorFor(model => model.DateStartedYear, new { htmlAttributes = new { @class = "form-control pull-left custom-input-xs" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">DD-MM-YYYY</div>
                    @Html.ValidationMessageFor(model => model.DateStartedYear, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.StartDate, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.StartDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
                </div>
            </div>            
           
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Dose, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Dose, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Dose, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Strength, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Strength, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Strength, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.SIG, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.SIG, new { htmlAttributes = new { @class = "form-control sig-codes" } })
                    @Html.ValidationMessageFor(model => model.SIG, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.MedicationRoute, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.MedicationRoute, new { htmlAttributes = new { @class = "form-control route-types" } })
                    @Html.ValidationMessageFor(model => model.MedicationRoute, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Mitte, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Mitte, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Mitte, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.MitteUnitId, new SelectList(ViewBag.MedFrequencyUnits, "Value", "Text"), "Choose One", new { @class = "form-control custom-input-sm" })
                    @Html.ValidationMessageFor(model => model.MitteUnitId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Repeats, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Repeats, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Repeats, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.TreatmentTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.TreatmentTypeId, new SelectList(ViewBag.TreatmentTypes, "Value", "Text"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.TreatmentTypeId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LU, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.LU, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.LU, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OutsideProviderFirstName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.OutsideProviderFirstName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OutsideProviderFirstName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.OutsideProviderLastName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.OutsideProviderLastName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OutsideProviderLastName, "", new { @class = "text-danger" })
                </div>
            </div>
            
        </div>
    </div><!--Modal body end-->
    @*@Html.ModalFooter("Add")*@
    <div class="modal-footer">
        
        <button data-select-type="1" class="btn btn-sm btn-primary btn-med-create"
           data-modal-url="@Url.Action("createpatientmedication", "patientmedications", new { area = "medications"})"          
           href="#">
            Add
        </button>

        <button data-select-type="2" data-modal-id="patientRxModal" data-modal-size="modal-lg"
           data-modal-url="@Url.Action("createpatientmedication", "patientmedications", new { area = "medications"})"           
           class="btn btn-sm btn-primary @disableRXbtns" href="#">
            Prescribe
        </button>
        <button data-select-type="3" data-modal-id="patientQuickRxModal" data-modal-size="modal-lg"
           data-modal-url="@Url.Action("createpatientmedication", "patientmedications", new { area = "medications"})"           
           class="btn btn-sm btn-primary @disableRXbtns" href="#">
            Quick Prescribe
        </button>         

        <button data-select-type="4" data-modal-id="patientAllergyModal" data-modal-size="modal-md"
                data-modal-url="@Url.Action("create", "patientallergies", new { area = "medications", din = Model.DIN ,patientId = Model.PatientId, medicationNoDinId = Model.MedicationNoDinId, isDin = Model.IsDin})"
                class="btn btn-sm btn-default cb-modal close-current-modal" href="#">
            Set Allergy
        </button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>

    </div>
            }

