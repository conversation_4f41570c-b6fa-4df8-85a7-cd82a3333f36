@model Cerebrum.ViewModels.Medications.VMPatientMedicationPreview

@{
    var medication = Model.Medication;
    var canPrint = 1;
    var doctor = Model.PrescriptionLetterHead.Doctor;
    var hasDoctor = doctor != null;
    var practiceDoctorId = doctor?.PracticeDoctorId;
    var practiceDoctorName = doctor?.FullName?.Replace("Dr. ", "") ?? "";
    var patient = Model.PrescriptionLetterHead.Patient;
    int? patientId = patient?.PatientId;
    var patientFirstName = patient?.FirstName;
    var patientLastName = patient?.LastName;
    var pharmacyFax = patient?.PharmacyFax;
    var canPrintMsg = "Cannot print medications that are discontinued.";
    var prescriptionSetId = medication == null ? null : medication.PrescriptionSetId;
    var prescriptionSetIdStr = medication == null ? null : medication.PrescriptionSetIdStr;
}
<table id="table-prescription"
       data-rx-set-id="@prescriptionSetId"
       data-rx-set-id-str="@prescriptionSetIdStr"
       data-practice-doctor-id="@practiceDoctorId"
       data-practice-doctor-Name="@practiceDoctorName"
       data-patient-id="@patientId"
       data-patient-firstname="@patientFirstName"
       data-patient-lastname="@patientLastName"
       data-patient-pharmacy-fax="@pharmacyFax"
       data-prescription-date="@System.DateTime.Now.ToShortDateString()"
       data-can-print="@canPrint"
       data-can-print-msg="@canPrintMsg"
       data-load-url="@Url.Action("printlog","patientmedications", new { area = "medications", prescriptionSetId = prescriptionSetId })">

    @{ await Html.RenderPartialAsync("_prescriptionLetterHead", (object)Model.PrescriptionLetterHead); }

    <tbody>
        <tr>
            <td colspan="2">
                <div id="prescription-body">
                    <table id="table-rx-body">
                        <tbody>
                            @if (Model.Medication != null)
                            {
                                await Html.RenderPartialAsync("_patientPrescription", (object)Model.Medication);
                            }
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
    </tbody>
    <tfoot>
        <tr>
            <td>
                <div id="prescription-footer">
                    <div id="prescription-footer-content">
                        @if (hasDoctor)
                        {
                            var signatureImg = "";
                            if (doctor.Signature != null)
                            {
                                var base64 = Convert.ToBase64String(doctor.Signature);
                                signatureImg = String.Format("data:image/gif;base64,{0}", base64);
                            }

                            if (!String.IsNullOrWhiteSpace(signatureImg))
                            {
                                <div><img style="width:200px;height:75px;" src="@signatureImg" /></div>
                            }
                            <div id="prescription-doctor-name">
                                @practiceDoctorName
                                @if (!String.IsNullOrWhiteSpace(doctor.Degress))
                                {<span>, @doctor.Degress</span>}
                            </div>

                        }
                    </div>
                </div>
            </td>
            <td><div id="page-counter"></div></td>
        </tr>
    </tfoot>
</table>