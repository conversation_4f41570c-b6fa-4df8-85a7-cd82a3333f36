@model Cerebrum.ViewModels.Medications.VMPatientPrescription

@using (Html.BeginForm("createpatientprescription", "patientmedications", new { area = "medications" }, FormMethod.Post, true, new { @class = "frm-create-rx" }))
{
    @*@Html.ModalHeader("Prescription preparation for " + Html.GetPatientName())*@
    @Html.Modal<PERSON>eader("Create Script for " + Html.GetPatientName())
    <div class="modal-body content-height500">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.EditorFor(model => (object)Model)
    </div><!--Modal body end-->
    @Html.ModalFooter("Create Script", btnColor: "green", ccsClasses: "", isInfoModal: false, addAnother: false, showMedCoverAndSadieLinks: true)
    @*@Html.ModalFooter("Prescribe")*@
}

