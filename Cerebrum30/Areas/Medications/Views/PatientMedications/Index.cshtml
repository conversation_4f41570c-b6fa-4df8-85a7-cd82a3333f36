@using Cerebrum.ViewModels.Medications;

@model VMPatientMedHistory

@{
    var canPrescribe = CerebrumUser.HasPermission("WritePrescription");
    var disableRXbtns = canPrescribe ? "" : "disabled";
    var isDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;
    var interactionsCheck = CerebrumUser.DoInterActionsCheck;
    var interactionsOnMsg = "Interactions Check: On";
    var interactionsOffMsg = "Interactions Check: Off";
    var interactionCheckMsg = interactionsCheck ? interactionsOnMsg : interactionsOffMsg;
    var interactionsColor = interactionsCheck ? "text-success" : "";

    string page = "Patient Medication History"; //Medication
    ViewBag.Title = page;
    ViewBag.ModuleName = page;
    Layout = "~/Areas/Medications/Views/Shared/_LayoutMedications.cshtml";
    var isOhip = Model.Patient.HealthCardProvince.ToLower().Equals("caon");
    bool eConsultActive = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["eConsultActive"]);

}
<style>
    @@media screen and (min-width: 768px) {
        #prescriptionSet-modal-container .modal-lg {
            width: 85% !important;
        }
    }

    @@media screen and (min-width: 768px) {
        #print-rx-modal-container .modal-lg {
            width: 85% !important;
        }
    }

    @@media screen and (min-width: 300px) {
        #print-patientmedications-modal-container .modal-lg {
            width: 85% !important;
        }
    }
</style>

<script>
$(document).ready(function () {
setHeaderTitle('@@page');
});
</script>

@section patientinfo{

    @Html.GetPatientInfo((object)Model.Patient.PatientId)

}
<div class="row">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12">
                <div class="float_l">
                    <a class="btn btn-default btn-sm" id="btn-view-prescriptions" href="#">Prescriptions <span id="prescription-set-count"></span></a>
                    @*@if (Cerebrum.DHDR.Services.Config.GetConfiguration?.CerebrumConfig.Enabled == true)*@
                    @if (CerebrumUser.IsDhdrEnabled && eConsultActive)
                    {
                        @Html.ActionLink("DHDR", "Index", "DHDR", new { patientId = Model.Patient.PatientId }, new { target = "blank_", @class = "btn btn-default btn-sm" })
                    }
                    <a class="btn btn-default btn-sm" href="Help/Drugs_Renal_Failure_AmFamPhysician2007.pdf" style="margin-left:4px !important;" target="_blank">Renal Dose Adjustments</a>
                    <a class="btn btn-default btn-sm" href="http://www.lucodes.ca/LU.html" target="_blank">LU Codes</a>
                    <a class="btn btn-default btn-sm" href="https://www.canada.ca/en/health-canada/services/drugs-health-products/drug-products/drug-product-database.html" target="_blank">Dormant Dins</a>
                    <a href="#" data-modal-url="@Url.Action("createnodinmedication","medications", new { area="medications" })" id="btn-noDin" class="btn btn-sm btn-default">Add No Din Medication</a>
                    @if (isDoctor)
                    {
                        <a class="btn btn-default btn-sm" id="btn-interation-settings" href="#">Interactions Settings</a>
                    }

                    <a class="btn btn-default btn-sm" id="btn-print-medications" href="#">Print Medications</a>

                    <a data-interactions-off="@interactionsOffMsg"
                       data-interactions-on="@interactionsOnMsg"
                       data-load-url="@Url.Action("ChangeInteractionsCheck", "patientmedications", new { area = "medications" })"
                       class="btn btn-default btn-sm" id="btn-interation-check" href="#"><span id="interactions-check-msg" class="@interactionsColor">@interactionCheckMsg</span></a>

                </div>
                <div class="float_r">
                    @if (!String.IsNullOrWhiteSpace(Model.MedicationDBLastUpdated))
                    {
                        <span>Drug Database Last Updated: @Model.MedicationDBLastUpdated</span>
                    }
                    <a href="@Html.GetReferrerUrl()" class="btn btn-default btn-sm">Go Back</a>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row spacer-top-7">
    <div class="col-md-7">
        <div id="med-search-container">
            @(await Html.PartialAsync("getmedicationsearchform", (object)new { area = "medications", patientId = Model.Patient.PatientId }))
        </div>
        @*<br />
            @if (Cerebrum.DHDR.Services.Config.GetConfiguration?.CerebrumConfig.Enabled == true)
            {
            if (!string.IsNullOrWhiteSpace(Model.Patient.Ohip))
            {
            <div id="dhdr-med-search-container">
            Ohip being searched @Model.Patient.Ohip
            @{Html.RenderAction("searchDHDR", "DHDR", new { area = "medications", patientId = Model.Patient.PatientId }); }
            </div>
            }
            else
            {
            <div id="dhdr-med-cannot-search">
            Ohip is missing, DHDR is not avaiable.
            </div>
            }
            }*@
    </div>
    <div class="col-md-2 text-right">
        @*<button data-modal-url="@Url.Action("createnodinmedication","medications", new { area="medications" })" id="btn-noDin" class="btn btn-sm btn-default">Add No Din Medication</button>*@
    </div>
    <div class="col-md-3 text-right">
        @if (canPrescribe)
        {
            <div class="dropdown hover-menu">
                <button class="btn btn-default btn-sm" type="button">
                    Medication Templates
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-right med-templ-lst __345326">
                    @foreach (var item in Model.MedicationTemplateClasses)
                    {
                        if (item.TotalTemplates > 0)
                        {
                            double menuColsCount = Math.Ceiling((item?.UserTemplates?.Count ?? 0) / 30.0);
                            string menuCols = string.Empty;
                            if (menuColsCount > 1) { menuCols = $"columns: {menuColsCount}; -webkit-columns: {menuColsCount}; -moz-columns: {menuColsCount};"; }

                            <li class="dropdown-submenu">
                                <a href="#" class="submenu-header" tabindex="-1">@Html.DisplayFor(model => (object)item.ClassName) (@Html.DisplayFor(model => (object)item.TotalTemplates)) <span class="caret"></span></a>
                                <ul class="dropdown-menu drp-right-align" style="@menuCols">
                                    @foreach (var subItem in item.UserTemplates)
                                    {
                                        <li>
                                            @*<a href="#">*@

                                            <span class="cb-modal glyphicon glyphicon-pencil c-pointer" data-modal-id="editMedTempateModal"
                                                  data-result-div="" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Edit @subItem.Name"
                                                  data-modal-url="@Url.Action("edit", "medicationtemplates", new { area = "medications", id = subItem.Id})">
                                                &nbsp;
                                            </span>

                                            <span class="cb-modal c-pointer" data-modal-id="createMedModal"
                                                  data-result-div="patient-history-content" data-bind-form="0"
                                                  data-modal-url="@Url.Action("createfromtemplate", "patientmedications", new { area = "medications", id = subItem.Id, patientId = Model.Patient.PatientId })">
                                                @subItem.Name
                                            </span>


                                            @*</a>*@

                                        </li>
                                    }
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="disabled">
                                <a href="#">
                                    @Html.DisplayFor(model => (object)item.ClassName) (@Html.DisplayFor(model => (object)item.TotalTemplates))
                                </a>
                            </li>
                        }
                    }

                </ul>
            </div>
        }
    </div>
</div>

<!-- ########### Allergies ###########-->
<div style="margin-top:15px;" id="patient-allergy-history-content" data-load-url="@Url.Action("getpatientAllergymain", "patientalleriess", new { area = "medications", patientId= Model.Patient.PatientId })">
    @(await Html.PartialAsync("_patientAllergyViewList", (object)(object)Model.PatientAllergyGet))
</div>
@*@if (Model.PatientAllergies.Any(a => a.IsDin == false) || Model.PatientAllergyIngredients.Any())
    {
    <div style="padding-top:15px;" class="row">
    <div class="col-md-12">
    <div ><!-- class="alert alert-danger" role="alert" -->
    <div ><strong>Allergies</strong></div><!-- class="text-danger" -->
    @if (Model.PatientAllergies.Any(a=> a.IsDin == false))
    {
    <div id="allergyWarning" style="margin-bottom:0;">
    @(await Html.PartialAsync("_patientAllergyList", (object)(object)Model.PatientAllergies))
    </div>
    }
    @if (Model.PatientAllergyIngredients.Any())
    {
    var margin = Model.PatientAllergies.Any() ? "0px" : "7px";

    <div id="allergyIngredientWarning" style="margin-top:@margin;">
    @(await Html.PartialAsync("_patientAllergyIngredList", (object)(object)Model.PatientAllergyIngredients))
    </div>
    }
    </div>
    </div>
    </div>
    }*@

<!-- ########### interaction and Medication ###########-->

<div id="interactionsWarning" data-patient-id="" data-medication-ids="" data-reload-interactions="0"></div>

<div style="margin-top:15px;" id="patient-history-content" data-load-url="@Url.Action("getpatientmedications", "patientmedications", new { area = "medications", patientId= Model.Patient.PatientId })">
    @(await Html.PartialAsync("_patientMedicationList", (object)(object)Model.PatientMedications))
</div>

<div data-patient-id="@Model.Patient.PatientId" data-load-url="@Url.Action("getprescriptionsets", "patientmedications", new { area = "medications"})" class="modal fade" id="prescriptionSet-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">@Model.Patient.FullName</h4>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="printMedications-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Print Medications</h4>
            </div>
            <div class="modal-body">
                <div class="form-group form-group-sm">
                    <div>                        
                        <div>
                            @Html.Label("ddlViewPatientMedicationFilter", "Medications", htmlAttributes: new { @class = "control-label col-md-5" })
                            @Html.DropDownList("ddlViewPatientMedicationFilter", (object)new List<SelectListItem>
                                {
                                new SelectListItem {Selected = true, Text = "Active", Value = "1"},
                                new SelectListItem {Text = "Discontinued", Value = "2"},
                                new SelectListItem {Text = "All", Value = "3"}
                                }, htmlAttributes: new { @class = "form-control", @style = "width: 80pt" })
                        </div>
                        <div>
                            @Html.Label("txtViewPatientMedicationsDateFrom", "From", htmlAttributes: new { @class = "control-label col-md-5 required-label" })
                            @Html.TextBox("txtViewPatientMedicationsDateFrom", String.Format("{0:MM/dd/yyyy}", String.IsNullOrEmpty(Model.Patient.DOB) ? DateTime.Now.ToString("MM/dd/yyyy") : Convert.ToDateTime(Model.Patient.DOB).ToString("MM/dd/yyyy")), new { @class = "form-control date-picker input-sm", @style = "width: 60pt" })
                        </div>
                        <div>
                            @Html.Label("txtViewPatientMedicationsDateTo", "To", htmlAttributes: new { @class = "control-label col-md-5 required-label" })
                            @Html.TextBox("txtViewPatientMedicationsDateTo", String.Format("{0:MM/dd/yyyy}", DateTime.Now.ToString("MM/dd/yyyy")), new { @class = "form-control date-picker input-sm", @style = "width: 60pt" })
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="btn-view-printmedications" class="btn btn-default btn-sm" data-load-url="@Url.Action("ViewPrintMedications","patientmedications", new { area="medications", patientId = Model.Patient.PatientId, dateFrom = "pDateFrom", dateTo = "pDateTo", filterType = "pMedicationFilterType" })">View</button>
                    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                </div>
            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>
</div>

<div class="modal fade" id="print-rx-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="print-rx-title">Prescription Print Preview</span></h4>
            </div>

            <div class="modal-body">
                <div id="print-rx-message-header"></div>
                <div id="divPrescriptionPrint">
                    <iframe id="print-rx-frame" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                @if (CerebrumUser.HasPermission("ReprintOrRefaxPrescription"))
                {
                    <button id="btn-fax-prescription" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-phone-alt"></span> Fax</button>
                    <button id="btn-print-prescription" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                }
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="print-patientmedications-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="patient-medication-title">Medications Print Preview</span></h4>
            </div>

            <div class="modal-body">
                <div id="print-patientmedications-message-header"></div>
                <div id="divPatientMedicationsPrint">
                    <iframe id="print-patientmedications-frame" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-patientmedications" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>


<div class="modal fade" id="print-med-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="saved-title">Dose History Print</span></h4>
            </div>

            <div class="modal-body">
                <div id="divMedicationPrint">
                    <iframe id="print-med-frame" style="width:100%;background-color:#ffffff;border:none; height:100%;" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-medication-view" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="fax-rx-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span>Fax Prescription for: @Model.Patient.FullName</span></h4>
            </div>

            <div class="modal-body">
                <div id="divPrescriptionFax">
                    <form id="frm-fax-rx" method="post" role="form" action="@Url.Action("faxprescription", "patientmedications", new { area = "medications" })">
                        <div class="form-horizontal">
                            @Html.AntiForgeryToken()
                            @Html.Hidden("PracticeDoctorId", (object)0)
                            @Html.Hidden("PracticeDoctor", "")
                            @Html.Hidden("PatientId", (object)Model.Patient.PatientId)
                            @Html.Hidden("PatientFirstName", Model.Patient.FirstName)
                            @Html.Hidden("PatientLastName", (object)Model.Patient.LastName)
                            @Html.Hidden("PrescriptionDate", "")
                            @Html.Hidden("FaxPrescriptionSetId", (object)0)
                            @Html.Hidden("FaxPrescriptionSetIdStr", "")
                            @Html.Hidden("FaxHtml", "")
                            @Html.Hidden("FaxHtmlHeader", "")
                            @Html.Hidden("FaxHtmlFooter", "")

                            <div class="form-group form-group-sm">

                                @Html.Label("Fax Number", "", new { @class = "control-label col-md-2 required-label" })
                                <div class="col-md-2">
                                    @Html.TextBox("FaxPharmacyNumber", Model.Patient.PharmacyFax)
                                </div>
                            </div>
                            <div class="form-group form-group-sm">
                                <div class="col-md-offset-2 col-md-10">
                                    <div id="rxfaxerror" class="text-danger"></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-submit-fax-prescription" class="btn btn-default btn-sm modal-submit-btn"><span class="glyphicon glyphicon-phone-alt"></span> Fax</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>


@if (isDoctor)
{
    <!--interactions settings modal-->
    <div data-patient-id="@Model.Patient.PatientId" class="modal fade" id="interactions-settings-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <form id="frm-interaction-settings" class="" method="post" role="form" action="@Url.Action("EditInterationSettings", "patientmedications", new { area = "medications" })">
                    @Html.ModalHeader("Interaction Settings")
                    <div class="modal-body">
                        @{
                            var settings = new Cerebrum.ViewModels.Medications.VMUserVisibilityMain();
                            await Html.RenderPartialAsync("_interactionSettings", (object)settings);
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Save Settings</button>
                        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                    </div>
                </form>

            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>
}
