@model Cerebrum.ViewModels.Medications.VMUserVisibilityMain

@Html.AntiForgeryToken()
@Html.HiddenFor(model => (object)model.Description)

<div class="form-horizontal">

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })   

    @if (CerebrumUser.HasRole($"Admin,Practice Admin,Super Admin"))
    {
        <div class="form-group form-group-sm">
            <div class="col-md-2"></div>
            <div class="col-md-10">
                <div class="checkbox">
                    <label>
                        @Html.EditorFor(model => (object)model.SetForPractice) <span class="checkbox-text">@Html.DisplayNameFor(model => model.SetForPractice)</span>
                    </label>
                    @Html.ValidationMessageFor(model => model.SetForPractice, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    }

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.VisibiltiyTypeId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-10">
            @{ var visLoadUrl = Url.Action("GetInteractionVisibilityOptions", "patientmedications", new { area = "medications" });}
            @Html.DropDownListFor(model => model.VisibiltiyTypeId, new SelectList(ViewBag.VisibilityTypes, "Value", "Text", Model.VisibiltiyTypeId), "Choose One", new { @class = "form-control", data_load_url=visLoadUrl })
            @Html.ValidationMessageFor(model => model.VisibiltiyTypeId, "", new { @class = "text-danger" })
        </div>
    </div>   

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.UserVisibilities, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-10">
            <div id="interaction-visibility-container">
                @{ await Html.RenderPartialAsync("_interactionsVisibilityList", (object)Model.UserVisibilities);}
            </div>
            @Html.ValidationMessageFor(model => model.UserVisibilities, "", new { @class = "text-danger" })
        </div>
    </div>   

   
</div>
