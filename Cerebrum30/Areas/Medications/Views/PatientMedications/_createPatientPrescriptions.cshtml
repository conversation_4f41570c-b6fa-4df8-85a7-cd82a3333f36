@model IEnumerable<Cerebrum.ViewModels.Medications.VMPatientPrescription>

@{ 
    var isReprescribe = ViewBag.IsRePrescribe != null && (bool)ViewBag.IsRePrescribe == true ? true : false;
}

@using (Html.BeginForm("createpatientprescriptions", "patientmedications", new { area="medications"}, FormMethod.Post, true, new { @class = "frm-create-rx" }))
{   
    @Html.ModalHeader("Create Script for " + Html.GetPatientName())
    <div class="modal-body content-height500">        
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.Hidden("IsRePrescribe", (object)isReprescribe)
        @Html.EditorFor(model => (object)Model)    
        
    </div><!--Modal body end-->
    @Html.ModalFooter("Create Script", btnColor:"green", ccsClasses: "", isInfoModal:false, addAnother:false, showMedCoverAndSadieLinks:true)    
}

