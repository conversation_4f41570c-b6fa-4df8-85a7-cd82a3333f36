@model Cerebrum.ViewModels.Medications.VMMedDiscontinue

@using (Html.BeginForm("creatediscontinuereason", "patientmedications", new { area = "medications" }, FormMethod.Post))
{
    @Html.<PERSON>("Discontinuation Reason")
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.PatientMedicationId)
        @Html.HiddenFor(model => (object)model.PatientId)
        @Html.HiddenFor(model => (object)model.MedicationName)
        @Html.HiddenFor(model => (object)model.IsDin)
        @Html.HiddenFor(model => (object)model.Ingredients)

        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MedicationName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <p class="form-control-static">@Model.MedicationName</p>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Ingredients, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <p class="form-control-static">@Model.Ingredients</p>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DateStop, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DateStop, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.DateStop, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReasonId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.ReasonId, new SelectList(ViewBag.DiscontinueReasons, "Value", "Text"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ReasonId, "", new { @class = "text-danger" })
                </div>
            </div>



            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OutsideProviderFirstName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.OutsideProviderFirstName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OutsideProviderFirstName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.OutsideProviderLastName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.OutsideProviderLastName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OutsideProviderLastName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DiscontinuedByFirstName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DiscontinuedByFirstName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.DiscontinuedByFirstName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.DiscontinuedByLastName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DiscontinuedByLastName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.DiscontinuedByLastName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Comments, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Comments, new { @class = "form-control", @rows = 4 })
                    @Html.ValidationMessageFor(model => model.Comments, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div><!--Modal body end-->
    
<div class="modal-footer">
    <button type="submit" value="save" class="btn btn-sm modal-submit-btn btn-spacing btn-success" >Save</button>
    <button type="button" class="btn btn-sm btn-default" data-dismiss="modal">Cancel</button>
</div>
}

