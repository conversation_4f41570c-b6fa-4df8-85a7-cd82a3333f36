@model IEnumerable<Cerebrum.ViewModels.Medications.VMPatientMedication>

@{
var isDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;
var canPrescribe = CerebrumUser.HasPermission("WritePrescription");
var disableRXbtns = "cb-modal";//canPrescribe ? "cb-modal" : "disabled grey-links";
var disableRXFooterbtns = canPrescribe ? "btn-rxcheck" : "disabled";
var drpClasses = new List<Cerebrum.ViewModels.Common.VMLookupItem>();
var drpTreatmentTypes = new List<Cerebrum.ViewModels.Common.VMLookupItem>();
var medicationDins = "";
var therapeuticClasses = Model.Cast<dynamic>().Where((System.Func<dynamic, bool>)(d=> !d.IsDiscontinued)).SelectMany(t => t.TherapeuticClasses.Select((System.Func<dynamic, object>)(c => c))).ToList();
var treatmentTypes = Model.Cast<dynamic>().Select((System.Func<dynamic, object>)(t => t.TreatmentType)).ToList();
var therGroup = therapeuticClasses.GroupBy(o => o).ToList();
var treatGroup = treatmentTypes.GroupBy(o => o).ToList();
var tclassCount = 0;
var tclassString = "";
var noneSelected = "None Selected";
var medsForInteractions = Model.Cast<dynamic>().Where((System.Func<dynamic, bool>)(m => m.IsDin && !m.IsDiscontinued)).ToList();
var isSortMedications = ViewBag.IsSortMedications != null ? (bool)ViewBag.IsSortMedications : false ;
var isReloadInteractions = ViewBag.IsReloadInteractions != null ? (bool)ViewBag.IsReloadInteractions : true;
var reloadInteractions = isReloadInteractions ? 1 : 0;
var interactionsRequestList = new List<Cerebrum.ViewModels.Medications.VMInteractionRequest>();

therapeuticClasses = therapeuticClasses.OrderBy((System.Func<dynamic, object>)(o => o)).ToList();
foreach (var item in therGroup)
{
tclassCount = item.Count();
tclassString = item.Key + "(" + tclassCount + ")";
var lookUp = new Cerebrum.ViewModels.Common.VMLookupItem();
lookUp.Value = item.Key;
lookUp.Text = tclassCount.ToString();
drpClasses.Add(lookUp);
}

foreach(var item in treatGroup)
{
var lookUp = new Cerebrum.ViewModels.Common.VMLookupItem();
lookUp.Value = String.IsNullOrWhiteSpace(item.Key) ? noneSelected : item.Key;
lookUp.Text = item.Count().ToString();
drpTreatmentTypes.Add(lookUp);
}

if (medsForInteractions.Count() > 1)
{
//this is for when we use our interactions database
medicationDins = string.Join(",", Model.Cast<dynamic>().Where((System.Func<dynamic, bool>)(m => m.IsDin && !m.IsDiscontinued)).Select((System.Func<dynamic, object>)(m =>  m.DIN + "!" + m.MedicationName )).ToList());
foreach(var item in medsForInteractions)
{
var request = new Cerebrum.ViewModels.Medications.VMInteractionRequest();
request.Din = item.DIN;
request.MedicationName = item.MedicationName;

interactionsRequestList.Add(request);
}

}


//for sorting
var sortMedName = "med_name";
var sortMedForm = "med_form";
var sortMedDose = "med_dose";
var sortMedSig = "med_sig";
var sortMedStrength = "med_strength";
var sortMedRoute = "med_route";
var sortMedLast = "med_last_prescribed";
var sortMedStart = "med_start_date";
var sortMedClass = "med_class";
var sortMedCPP = "med_cpp";
}

<!-- ############## Medication (interactions) ############## -->

@if (interactionsRequestList.Any())
{
var patId = Model.Cast<dynamic>().First().PatientId;
using (Html.BeginForm("GetMedicationsInteractions", "patientmedications", new { area = "medications" }, FormMethod.Post, true, new { @class = "form-horizontal", @id = "frm-get-interactions" }))
{
@Html.AntiForgeryToken()
@Html.Hidden("patientId", (object)(object)patId)
for (int i = 0; i < interactionsRequestList.Count(); i++)
{
@Html.EditorFor((System.Func<dynamic, object>)(model => (object)interactionsRequestList[i]), "VMHiddenInteractionRequest", String.Format("{0}[{1}]", "interactionRequestList", i))
}
}

<script type="text/javascript">
$(function () {
var interactionsDiv = $('#interactionsWarning');
interactionsDiv.data('reload-interactions', @reloadInteractions);
loadInterActions();
});
</script>

}

@*@if (!String.IsNullOrWhiteSpace(medicationDins))
{
var patId = Model.Cast<dynamic>().First().PatientId;
<div id="interactionsWarning" data-patient-id="@patId" data-medication-ids="@medicationDins" data-reload-interactions="@reloadInteractions"></div>
<script type="text/javascript">
$(function () {
//setTimeout(function () { loadInterActions(); }, 0);
var interactionsDiv = $('#interactionsWarning');
interactionsDiv.data('patient-id', @patId);
//interactionsDiv.data('medication-ids', '@medicationDins');
interactionsDiv.data('medication-ids', ''+JSON.stringify(@Html.Raw(Json.Encode(interactionsRequestList)));
interactionsDiv.data('reload-interactions', @reloadInteractions);
loadInterActions();
});
</script>
}*@
<!-- ############## /interactions ############## -->

<div>
<h3 class="panel-title spacer-top-35">Medications <span id="patientmedication-count" class="badge cbadge">@Model.Count()</span></h3>




<div class="pull-right">
@*@Html.DropDownList("drpTherapeuticClasses", new SelectList(drpClasses), "All", new { @class = "form-control" })*@
@{if (drpClasses.Any()) {
var allClasses = new Cerebrum.ViewModels.Common.VMLookupItem();
allClasses.Value = "All Classes";
allClasses.Text = drpClasses.Count().ToString();
drpClasses.Insert(0,allClasses);

<div id="drpTherapeuticClasses" class="dropdown hover-menu">
<button class="btn btn-default btn-sm" type="button">
<span id="drpTherapeuticClasses-title">@allClasses.Value</span>
<span class="caret"></span>
</button>
<ul class="dropdown-menu dropdown-menu-right _34534">
@foreach (var item in drpClasses)
{
var active ="";
var classCountColor = "text-primary";
if (item.Text.Equals(allClasses.Text))
{
active = "active";
//classCountColor = "text-warning";
}
<li class="@active">
<a data-therapeutic-class-value="@item.Value" class="therapeutic-class" href="#">@item.Value <span class="therapeutic-class-count @classCountColor">(@item.Text)</span></a>
</li>
}

</ul>
</div>
@*<div style="padding-top:5px;" class="pull-right">Class:</div>*@
}
}
</div>

<div class="pull-left" style="margin-top: 3px; margin-bottom:3px">
Visit <a href="https://www.rxlist.com/" target="_blank">RxList.com</a> to find prescription drugs, mediations, etc.
@{if (drpTreatmentTypes.Any())
{
var allTypes = new Cerebrum.ViewModels.Common.VMLookupItem();
allTypes.Value = "All Treatment Types";
allTypes.Text = drpTreatmentTypes.Count().ToString();
drpTreatmentTypes.Insert(0, allTypes);

<div id="drpTreatmentTypes" style="margin-right:5px;" class="dropdown hover-menu">
<button class="btn btn-default btn-sm" type="button">
<span id="drpTreatmentTypes-title">@allTypes.Value</span>
<span class="caret"></span>
</button>
<ul class="dropdown-menu dropdown-menu-left">
@foreach (var item in drpTreatmentTypes)
{
var active = "";
var classCountColor = "text-primary";
if (item.Text.Equals(allTypes.Text))
{
active = "active";
}
<li class="@active">
<a data-treatmentype-value="@item.Value" class="therapeutic-class" href="#">@item.Value <span class="therapeutic-class-count @classCountColor">(@item.Text)</span></a>
</li>
}

</ul>
</div>
@*<div style="padding-top:5px;" class="pull-right">Treatment Type:</div>*@
}
}
</div>
@if (Model.Cast<dynamic>().Any())
{
int patientId = Model.Cast<dynamic>().First().PatientId;
<table data-patient-id ="@patientId" data-sort-load-url="@Url.Action("getpatientmedications", "patientmedications", new { area = "medications"})" id="tbl-patientmedications" class="table spacer-top-4">

<tr class="medication-code">
<th class="hidden"></th>
<th class="hidden"></th>
<th class="th-expand-row"></th>
<th>
<input name="selectAllMedications" id="selectAllMedications" type="checkbox" /> All
</th>
<th style="width:20%" class="th-sortable-header" data-sort-key="@sortMedName">
@Html.DisplayNameFor(model => model.MedicationName)
</th>
<th style="width:15%" class="th-sortable-header" data-sort-key="@sortMedClass">
@Html.DisplayNameFor(model => model.Classes)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedForm">
@Html.DisplayNameFor(model => model.Form)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedDose">
@Html.DisplayNameFor(model => model.Dose)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedStrength">
@Html.DisplayNameFor(model => model.Strength)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedSig">
@Html.DisplayNameFor(model => model.SIG)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedRoute">
@Html.DisplayNameFor(model => model.Route)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedLast">
@Html.DisplayNameFor(model => model.DateLastPrescribed)
</th>
<th style="width:8%" class="th-sortable-header" data-sort-key="@sortMedStart">
@Html.DisplayNameFor(model => model.DateStarted)
</th>
<th>
@Html.DisplayNameFor(model => model.DateStartedStr)
</th>
<th style="width:8%">
@Html.DisplayNameFor(model => model.DateExpired)
</th>
<th>
@Html.DisplayNameFor(model => model.DateDiscontinued)
</th>
@if (isDoctor)
{
<th>
@Html.DisplayNameFor(model => model.CPPVisibility)
</th>
}
<th >Action</th>
</tr>
@foreach (var item in Model)
{
string treatType = String.IsNullOrWhiteSpace(item.TreatmentType) ? noneSelected : item.TreatmentType;
string itemClasses = !item.IsDiscontinued ? String.Join(",", item.TherapeuticClasses) : "" ;
bool showExpandLink = item.DiscontinuedMedications.Any() ? true : false;
string cssDiscontinue = item.IsDiscontinued ? "text-danger" : "text-success";
<tr id="<EMAIL>" class="patient-med-item @cssDiscontinue">
<td class="hidden">@itemClasses</td>
<td class="hidden">@treatType </td>
<td>
@if (showExpandLink)
{<a href="#" class="expand-btn"><small><span class="glyphicon glyphicon-plus"></span></small></a>}
</td>
<td>
@if (!item.IsDiscontinued)
{ <input class="cb-med-select" name="PatientMedicationIds" type="checkbox" value="@item.Id" />}
</td>
<td>
<span class="@cssDiscontinue">@item.MedicationName</span>
@*@item.MedicationName*@
<div>Ingredients: @item.Ingredients</div>
@if (!String.IsNullOrWhiteSpace(item.LU))
{
<div>LU: @item.LU</div>
}
</td>
<td>
@*@item.Classes*@@Html.Truncate((object)item.Classes, 50,true,false)
</td>
<td>
@item.Form
</td>
<td>
@item.Dose
</td>
<td>
@item.Strength
</td>
<td>
@item.SIG
</td>
<td>
@item.Route
</td>
<td>
@if (!String.IsNullOrWhiteSpace(item.DateLastPrescribedPartial))
{
<span>@item.DateLastPrescribedPartial</span>
}
else if (item.DateLastPrescribed != null)
{
<span>@item.DateLastPrescribed.Value.ToShortDateString()</span>
}


@if (item.HasOutidePrescriber)
{
<small> by @item.OutsideProviderFirstName @item.OutsideProviderLastName</small>
}
else if(!String.IsNullOrWhiteSpace(item.LastPrescribedName))
{
<small> by @item.LastPrescribedName</small>
}
</td>
<td>
@item.DateStarted
</td>
<td>
@item.DateStartedStr
</td>
<td>
@item.DateExpired
</td>
<td class="td-discontinue">
@if (item.IsDiscontinued || item.DateDiscontinued != null)
{

var changeCommentItem = "";

if (!String.IsNullOrWhiteSpace(item.DiscontinueComment))
{
changeCommentItem = item.DiscontinueComment;
}
else if (!String.IsNullOrWhiteSpace(item.DoseChangeComment))
{
changeCommentItem = item.DoseChangeComment;
}
//else
//{
//    changeCommentItem = item.InternalPrescriptionNotes;
//}

@item.DateDiscontinued
if (!String.IsNullOrWhiteSpace(item.DiscontinuedByFirstName) || !String.IsNullOrWhiteSpace(item.DiscontinuedByLastName))
{
<small> by @item.DiscontinuedByFirstName @item.DiscontinuedByLastName</small>
}

<a data-toggle="tooltip" data-placement="bottom" title="Edit Discontinued Medication" data-modal-id="discontinueReasonModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("editdiscontinuereason", "patientmedications", new { area = "medications", id= item.Id})"
class="btn btn-xs btn-default cb-modal" href="#"><span class="glyphicon glyphicon-edit text-primary"></span></a>
if (!String.IsNullOrWhiteSpace(changeCommentItem))
{
<div class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<div class="btn-popover-title">
Comments
</div>
<div class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">
@changeCommentItem
</div>
</div>

</div>
}
}
else
{

<a data-modal-id="discontinueReasonModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("creatediscontinuereason", "patientmedications", new { area = "medications", id= item.Id})" class="italic cb-modal btn btn-default btn-xs" href="#"><small>Discontinue</small></a>

}

@if (isDoctor)
{
<td>
<div>
@{
var cppVisibilityColor = item.CPPVisibility ? "btn-cpp-visibility-active" : "";
var cppVisiibiltyText = item.CPPVisibility ? "On" : "Off";
}
<div data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="CPP Visibility" data-pat-med-id="@item.Id" data-url="@Url.Action("ChangeCPPVisibility", "patientmedications", new { area = "medications" })" class="c-pointer btn-cpp-visibility @cppVisibilityColor">@cppVisiibiltyText</div>
</div>
</td>
}
<td class="td-edit-med">
<div class="btn-popover-container">
<button type="button" class="btn btn-default btn-xs popover-btn">
<span class="glyphicon glyphicon-option-vertical text-primary"></span>
</button>
<div class="btn-popover-title">
Medication Menu
</div>
<div class="btn-popover-content">
<ul class="ul-app-menu">
<li>
<a data-modal-id="editDoseModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("editdose", "patientmedications", new { area = "medications", id= item.Id})"
class="@disableRXbtns" href="#"><span class="glyphicon glyphicon-pencil text-primary"></span> Edit Medication</a>
</li>
<li>
@if (item.CanDelete)
{
<a data-modal-id="deleteMedicationModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("delete", "patientmedications", new { area = "medications", id = item.Id })"
class="@disableRXbtns" href="#"><span class="glyphicon glyphicon-trash text-danger"></span> Delete Medication</a>
}
else
{
<a data-toggle="tooltip" data-placement="bottom" title="Cannot Delete because there are prescriptions"
class="disabled grey-links" href="#">
<span class="disabled glyphicon glyphicon-trash text-danger"></span> Delete Medication
</a>
}
</li>
<li>
<a data-modal-id="allergyModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("create", "patientallergies", new { area = "medications", din = item.DIN, patientId = item.PatientId, medicationNoDinId = item.MedicationNoDinId, isDin = item.IsDin})"
data-modal-size="modal-md"
class="@disableRXbtns" href="#"><span class="glyphicon glyphicon-warning-sign text-danger"></span> Set as allergy</a>
</li>
<li>
<a data-modal-id="templateModal" data-modal-url="@Url.Action("create", "medicationtemplates", new { area = "medications", id= item.Id})"
class="@disableRXbtns" href="#"><span class="glyphicon glyphicon-floppy-save"></span> Save as template</a>
</li>
<li>
<a data-patient-medication-id="@item.Id" data-medication-set-id="@item.MedicationSetId" data-modal-url="@Url.Action("printmedication", "patientmedications", new { area = "medications" })"
class="btn-medication-print" href="#"><span class="glyphicon glyphicon-print"></span> Dose History Print</a>
</li>
</ul>
</div>

</div>

@if (!String.IsNullOrWhiteSpace(item.OutsideProviderFirstName) || !String.IsNullOrWhiteSpace(item.OutsideProviderLastName))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-tag"></span>
<span class="btn-popover-title">
Outside Provider
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@item.OutsideProviderFirstName @item.OutsideProviderLastName
</span>
</span>

</span>
}

@*@if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Internal Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@item.InternalPrescriptionNotes
</span>
</span>

</span>
}*@

@{
var changeReasonMain = "";

if (!String.IsNullOrWhiteSpace(item.DiscontinueReason))
{
changeReasonMain = item.DiscontinueReason;
}
else if (!String.IsNullOrWhiteSpace(item.DoseChangeReason))
{
changeReasonMain = item.DoseChangeReason;
}
}

@if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes) || !String.IsNullOrWhiteSpace(changeReasonMain))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes))
{
<strong>Internal Notes: </strong> @item.InternalPrescriptionNotes

}
@if (!String.IsNullOrWhiteSpace(changeReasonMain))
{
<strong>Reason: </strong> @changeReasonMain
}
</span>
</span>

</span>
}

@if (!String.IsNullOrWhiteSpace(item.ResidualData))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer">R</span>
<span class="btn-popover-title">Residual Data</span>
<span class="btn-popover-content"><div style="width:200px;max-height:200px;overflow-y:auto;">@item.ResidualData</div>
</span>
</span>
}
</td>
</tr>
foreach (var disconItem in item.DiscontinuedMedications)
{
var disconItemColor = "text-danger";//disconItem.IsDiscontinued ? "text-danger" : "";

<tr id="<EMAIL><EMAIL>" class="expand collapse child-row @disconItemColor">
<td class="child-row-td-noborder"></td>
<td></td>
<td>
@disconItem.MedicationName
<div>Ingredients: @disconItem.Ingredients</div>
@if (!String.IsNullOrWhiteSpace(disconItem.LU))
{
<div>LU: @disconItem.LU</div>
}
</td>
<td>
@*@disconItem.Classes*@
@Html.Truncate((object)disconItem.Classes, 50, true, false)
</td>
<td>
@disconItem.Form
</td>
<td>
@disconItem.Dose
</td>
<td>
@disconItem.Strength
</td>
<td>
@disconItem.SIG
</td>
<td>
@disconItem.Route
</td>
<td>
@if (disconItem.DateLastPrescribed != null)
{
<span>@disconItem.DateLastPrescribed.Value.ToShortDateString()</span>
}
@if (disconItem.HasOutidePrescriber)
{
<small> by @disconItem.OutsideProviderFirstName @disconItem.OutsideProviderLastName</small>
}
else if (!String.IsNullOrWhiteSpace(disconItem.LastPrescribedName))
{
<small> by @disconItem.LastPrescribedName</small>
}
</td>
<td>
@disconItem.DateStarted
</td>
<td>
@disconItem.DateStartedStr
</td>
<td>
@disconItem.DateExpired
</td>
<td class="td-discontinue">
@disconItem.DateDiscontinued
@if (!String.IsNullOrWhiteSpace(disconItem.DiscontinuedByFirstName) || !String.IsNullOrWhiteSpace(disconItem.DiscontinuedByLastName))
{
<small> by @disconItem.DiscontinuedByFirstName @disconItem.DiscontinuedByLastName</small>
}
@{
var changeComment = "";

if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueComment))
{
changeComment = disconItem.DiscontinueComment;
}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeComment))
{
changeComment = disconItem.DoseChangeComment;
}
//else
//{
//    changeComment = disconItem.InternalPrescriptionNotes;
//}
}
@if (!String.IsNullOrWhiteSpace(changeComment))
{
<div class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<div class="btn-popover-title">
Comments
</div>
<div class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">
<div>@changeComment</div>
@*@if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueComment))
{
<div>@disconItem.DiscontinueComment</div>
}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeComment))
{
<div>@disconItem.DoseChangeComment</div>
}*@
</div>
</div>

</div>
}
</td>
@if (isDoctor)
{
<td>
<div>
@{
var cppVisiibiltyTextDis = disconItem.CPPVisibility ? "On" : "Off";
}
<div data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="CPP Visibility">@cppVisiibiltyTextDis</div>
</div>
</td>
}
<td class="td-edit-med">
@{
var changeReason = "";

if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueReason))
{
changeReason = disconItem.DiscontinueReason;
}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeReason))
{
changeReason = disconItem.DoseChangeReason;
}
}

@if (!String.IsNullOrWhiteSpace(disconItem.InternalPrescriptionNotes) || !String.IsNullOrWhiteSpace(changeReason))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@if (!String.IsNullOrWhiteSpace(disconItem.InternalPrescriptionNotes))
{
<strong>Internal Notes: </strong> @disconItem.InternalPrescriptionNotes

}
@if (!String.IsNullOrWhiteSpace(changeReason))
{
<strong>Reason: </strong> @changeReason
}
</span>
</span>

</span>
}

@if (!String.IsNullOrWhiteSpace(disconItem.ResidualData))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer">R</span>
<span class="btn-popover-title">Residual Data</span>
<span class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">@disconItem.ResidualData</div> <!-- or use 'pre' tag-->
</span>
</span>
}
</td>
</tr>
}

}

</table>
<div class="panel-footer">
<a data-is-print="0" data-patient-id="@patientId" data-modal-url="@Url.Action("PrescriptionsCheck", "patientmedications", new { area = "medications" })" class="btn btn-primary btn-sm @disableRXFooterbtns">Prescribe</a>
<a data-is-print="1" data-patient-id="@patientId" data-modal-url="@Url.Action("PrescriptionsCheck", "patientmedications", new { area = "medications" })" class="btn btn-primary btn-sm @disableRXFooterbtns">Quick Prescribe</a>

@*<a data-patient-id="@patientId" data-modal-url="@Url.Action("createpatientprescriptions", "patientmedications", new { area = "medications" })" id="btn-prescribe" class="btn btn-primary btn-xs @disableRXbtns">Prescribe</a>
<a data-patient-id="@patientId" data-modal-url="@Url.Action("printprescriptions", "patientmedications", new { area = "medications" })" id="btn-quick-prescribe" class="btn btn-primary btn-xs @disableRXbtns">Quick Prescribe</a>*@

</div>
<div style="margin-bottom:30px"></div>
}
else
{
<div class="">@*panel-body*@
<h5 class="text-info">
<span>No medications.</span>
</h5>
</div>
}
</div>

