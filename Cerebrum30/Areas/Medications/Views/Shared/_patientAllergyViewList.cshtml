@model Cerebrum.ViewModels.Medications.VMPatientAllergyGet

@{ 
    var importedMeds = Model.PatientAllergies.Cast<dynamic>().Where((System.Func<dynamic, bool>)(i => i.IsImported == true)).ToList();
    var importedIngrs = Model.PatientAllergyIngredients.Cast<dynamic>().Where((System.Func<dynamic, bool>)(i => i.IsImported == true)).ToList();

    var notImportedMeds = Model.PatientAllergies.Cast<dynamic>().Where((System.Func<dynamic, bool>)(i => i.IsImported == false)).ToList();
    var notImportedIngr = Model.PatientAllergyIngredients.Cast<dynamic>().Where((System.Func<dynamic, bool>)(i => i.IsImported == false)).ToList();

}

@if (Model.PatientAllergies.Cast<dynamic>().Any((System.Func<dynamic, bool>)(a => a.IsDin == false || a.IsImported)) || Model.PatientAllergyIngredients.Cast<dynamic>().Any())
{
    <div style="padding-top:15px;" class="row">
        <div class="col-md-12">
            <div>
                <!-- class="alert alert-danger" role="alert" -->
                <div><strong>Allergies</strong></div><!-- class="text-danger" -->
                @if (Model.PatientAllergies.Cast<dynamic>().Any((System.Func<dynamic, bool>)(a => a.IsDin == false || a.IsImported)))
                {
                    <div id="allergyWarning" style="margin-bottom:0;">
                        @(await Html.PartialAsync("_patientAllergyList", (object)(object)Model.PatientAllergies))
                    </div>
                }
                @if (Model.PatientAllergyIngredients.Cast<dynamic>().Any((System.Func<dynamic, bool>)(a=> a.IsImported == false)))
                {
                    var margin = Model.PatientAllergies.Cast<dynamic>().Any() ? "0px" : "7px";
                    var noImportedIngr = Model.PatientAllergyIngredients.Cast<dynamic>().Where((System.Func<dynamic, bool>)(a => a.IsImported == false)).ToList();
                    <div id="allergyIngredientWarning" style="margin-top:@margin;">
                        @(await Html.PartialAsync("_patientAllergyIngredList", (object)(object)noImportedIngr))
                    </div>
                }
            </div>
        </div>
    </div>
}