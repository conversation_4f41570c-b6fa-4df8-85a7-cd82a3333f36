@model IEnumerable<Cerebrum.ViewModels.Medications.VMInteraction>

@if (Model.Any())
{
    var canManageInteractions = CerebrumUser.HasPermission("Medication", "Manage Interactions");
    @*<script type="text/javascript">
            $(function () {
               // $('#interactions-collapse').collapse('show');
            });
        </script>*@
    @*<div class="row">*@
    @*<div class="col-md-12">*@

    @*<button id="btn-toggle-interactions" class="btn btn-warning btn-sm" type="button" data-toggle="collapse" data-target="#interactions-collapse">
        <span class="toggle-view-text">Hide Interactions</span> 
    </button>*@
        <!--
            new color code: #f0efc2
        -->



    <h3 class="panel-title spacer-top-35">Interactions <span id="patientInteractions-count" class="badge cbadge">@Model.Count()</span>           
        <span id="_swictState" style="width:70px; font-weight: normal" class="btn btn-default btn-xs badge"></span></h3>
        <div class="row">
            <div class="col-md-12">
                <div id="interactions-collapse1">
                    <table class="table spacer-top-4" id="table-medic-interaction1">
                        <thead>
                            <tr class="interactions-code">
                                <th>
                                    @Html.DisplayNameFor(model => model.MedicationName)
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.InteratingMediationName)
                                </th>

                                <th>
                                    @Html.DisplayNameFor(model => model.Description)
                                </th>

                                <th>
                                    @Html.DisplayNameFor(model => model.SeverityDescription)
                                </th>

                                <th>
                                    @Html.DisplayNameFor(model => model.DocumentationDescription)
                                </th>

                                <th>
                                    @Html.DisplayNameFor(model => model.OnsetDescription)
                                </th>

                                @*<th style="width:4%">
                                    @Html.DisplayNameFor(model => model.Source)
                                </th>*@
                                @*<th>
                                    </th>*@
                                @*<th>
                                        @Html.DisplayNameFor(model => model.Type)
                                    </th>
                                    <th>
                                        @Html.DisplayNameFor(model => model.Risk)
                                    </th>*@
                                    @if (canManageInteractions)
                                    {
                                        <th>
                                            @Html.DisplayNameFor(model => model.Management)
                                        </th>
                                    }
                            </tr>
                        </thead>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    @item.MedicationName
                                </td>
                                <td>
                                    @item.InteratingMediationName
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @item.SeverityDescription
                                </td>
                                <td>
                                    @item.DocumentationDescription
                                </td>
                                <td>
                                    @item.OnsetDescription
                                </td>
                                @*<td>
                                    <a href="@item.Source" target="_blank">View</a>
                                </td>*@
                                @if (canManageInteractions)
                                {
                                <td>
                                        @using (Html.BeginForm("manageinteraction", "patientmedications", new { area = "medications" }, FormMethod.Post, true, new { @class = "frm-manage-interaction" }))
                                        {
                                            @Html.AntiForgeryToken()
                                            @Html.Hidden("PatientId", (object)(object)item.PatientId)
                                            @Html.Hidden("MedicationDin", item.nx_id1)
                                            @Html.Hidden("InteractingDin", (object)(object)item.nx_id2)
                                            <button  data-toggle="tooltip" data-placement="bottom" title="Hides interaction" class="btn btn-primary btn-xs">Manage</button>
                                        }
                                    </td>
                                }
                                @*<td>
                                        @item.Type
                                    </td>
                                    <td>
                                        @item.Risk
                                    </td>
                                    <td>
                                        @item.Management
                                    </td>*@
                            </tr>
                        }
                    </table>
                    @Html.Action("GetDrugDatabaseVersionInfo", new { areas = "Medications", controller = "Medications" })
                </div>
            </div>
        </div>

        <script type="text/javascript">
            var initHeight;
            
            var getInteractionsHeight = function () {
                if ($('#interactions-collapse1').height() != 0) {
                    initHeight = $('#interactions-collapse1').height();
                }
            }

            $(window).resize(function (r) {
                //console.log(r);
                getInteractionsHeight();
            });


            $(document).ready(function () {               
                getInteractionsHeight();
                var state = true;
                var lbl = 'Hide';
                $('#_swictState').html(lbl);
                
                $('#_swictState').on('click', function () {               
                 
                    if (state) {
                        lbl = 'Show';
                        $('#interactions-collapse1').fadeOut('easeInOutExpo');
                        state = false;
                    }
                    else {
                        lbl = 'Hide';
                        $('#interactions-collapse1').fadeIn('easeInOutExpo');
                        state = true;                       
                    }
                    $('#_swictState').html(lbl);
                });               
            });
        </script>
}
