@model Cerebrum.ViewModels.Medications.VMMedicationTemplate

@using (Html.BeginForm("edit", "medicationtemplates", new { area = "medications" }, FormMethod.Post))
{
    @Html.Mo<PERSON>eader("Edit Medication Template")
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        <div class="form-horizontal">
            @Html.HiddenFor(model => (object)model.Id)
            @Html.HiddenFor(model => (object)model.PatientMedicationId)
            @Html.HiddenFor(model => (object)model.MedicationName)
            @Html.HiddenFor(model => (object)model.MedicationNoDinId)
            @Html.HiddenFor(model => (object)model.DIN)
            @*@Html.HiddenFor(model => (object)model.Dose)
                @Html.HiddenFor(model => (object)model.SIG)
                @Html.HiddenFor(model => (object)model.Mitte)
                @Html.HiddenFor(model => (object)model.MitteUnitId)
                @Html.HiddenFor(model => (object)model.Repeats)
                @Html.HiddenFor(model => (object)model.Route)
                @Html.HiddenFor(model => (object)model.Strength)*@
            @Html.HiddenFor(model => (object)model.IsDin)
            @Html.AntiForgeryToken()

            @*<div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.MedicationName, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        <p class="form-control-static">
                            @Html.DisplayFor(model => (object)model.MedicationName)
                        </p>
                    </div>
                </div>*@
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.TemplateClassId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.TemplateClassId, new SelectList(ViewBag.TemplateClasses, "Value", "Text"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.TemplateClassId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Dose, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Dose, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Dose, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Strength, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Strength, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Strength, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.SIG, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.SIG, new { htmlAttributes = new { @class = "form-control sig-codes" } })
                    @Html.ValidationMessageFor(model => model.SIG, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Route, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Route, new { htmlAttributes = new { @class = "form-control route-types" } })
                    @Html.ValidationMessageFor(model => model.Route, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Mitte, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Mitte, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Mitte, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.MitteUnitId, new SelectList(ViewBag.MedFrequencyUnits, "Value", "Text"), "Choose One", new { @class = "form-control custom-input-sm" })
                    @Html.ValidationMessageFor(model => model.MitteUnitId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Repeats, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Repeats, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                    @Html.ValidationMessageFor(model => model.Repeats, "", new { @class = "text-danger" })
                </div>
            </div>


            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.TreatmentTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.TreatmentTypeId, new SelectList(ViewBag.TreatmentTypes, "Value", "Text"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.TreatmentTypeId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LU, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.LU, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.LU, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Instructions, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Instructions, new { @class = "form-control", @rows = 4 })
                    @Html.ValidationMessageFor(model => model.Instructions, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-2"></div>
                <div class="col-md-10">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.CanShare) <span class="checkbox-text">@Html.DisplayNameFor(model => model.CanShare)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.CanShare, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div><!--Modal body end-->
    <div class="modal-footer">
        <div class='row'>
            <div class='col-md-2'>
                <button class="btn btn-default btn-sm med-template-deactivate" data-modal-url='/Medications/drugs/medicationTemplates/Deactivate/'>Deactivate</button>
            </div>
            <div class='col-md-10'>
                <button type="submit" value="save" class="btn btn-sm modal-submit-btn btn-spacing btn-success">Save</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
}


