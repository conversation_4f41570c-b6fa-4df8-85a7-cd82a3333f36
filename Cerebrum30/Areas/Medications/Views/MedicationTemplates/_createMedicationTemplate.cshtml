@model Cerebrum.ViewModels.Medications.VMMedicationTemplate

@using (Html.BeginForm("create", "medicationtemplates", new { area = "medications" }, FormMethod.Post))
{
    @Html.Mo<PERSON>eader("Create Medication Template")
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        <div class="form-horizontal">
            @Html.HiddenFor(model => (object)model.PatientMedicationId)
            @Html.HiddenFor(model => (object)model.MedicationName)
            @Html.HiddenFor(model => (object)model.MedicationNoDinId)
            @Html.HiddenFor(model => (object)model.DIN)
            @Html.HiddenFor(model => (object)model.Dose)
            @Html.HiddenFor(model => (object)model.SIG)
            @Html.HiddenFor(model => (object)model.Mitte)
            @Html.HiddenFor(model => (object)model.MitteUnitId)
            @Html.HiddenFor(model => (object)model.Repeats)
            @Html.HiddenFor(model => (object)model.Route)
            @Html.HiddenFor(model => (object)model.Strength)
            @Html.HiddenFor(model => (object)model.IsDin)            
            @Html.AntiForgeryToken()
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.TemplateClassId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.TemplateClassId, new SelectList(ViewBag.TemplateClasses, "Value", "Text"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.TemplateClassId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LU, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.LU, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.LU, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Instructions, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Instructions, new { @class = "form-control", @rows = 4 })
                    @Html.ValidationMessageFor(model => model.Instructions, "", new { @class = "text-danger" })
                </div>
            </div>

            

            <div class="form-group form-group-sm">
                <div class="col-md-2"></div>
                <div class="col-md-10">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.CanShare) <span class="checkbox-text">@Html.DisplayNameFor(model => model.CanShare)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.CanShare, "", new { @class = "text-danger" })
                    </div>
                </div>                
            </div>
        </div>
        </div><!--Modal body end-->
    @Html.ModalFooter("Save")
}


