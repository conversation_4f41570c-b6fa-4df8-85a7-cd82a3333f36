@model IEnumerable<Cerebrum.ViewModels.Medications.VMPatientMedCreate>

@{ 
    bool isNoDinSearch = Model.Cast<dynamic>().Any((System.Func<dynamic, bool>)(n => n.IsDin == false));
}

@Html.Modal<PERSON>eader("Medication Search Results")
<div id="medSearchResultContainer" class="modal-body">
    @if (isNoDinSearch)
    {
        @(await Html.PartialAsync("_noDinWarning"))
    }
    <h3 class="panel-title">Search Results <span class="badge cbadge">@Model.Count()</span></h3>
    <div class="content-height300">
        <table class="table table-bordered table-hover table-condensed table-input">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.MedicationName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Dose)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.SIG)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.MedicationRoute)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Repeats)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Mitte)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.StartDate)
                </th>
                <th></th>
            </tr>

            @foreach (var item in Model)
            {                
                <tr class="med-search-result">
                    @(await Html.PartialAsync("_createPatientMedication", (object)(object)item))
                </tr>
            }

        </table>
    </div>    
</div>

@Html.ModalFooter(isInfoModal: true)
