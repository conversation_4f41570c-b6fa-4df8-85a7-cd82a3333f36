@model Cerebrum.ViewModels.Medications.VMMedicationNoDin


@using (Html.BeginForm("createnodinmedication", "medications", new { area = "medications" }, FormMethod.Post))
{
    @Html.<PERSON>eader("Add New No Din Medication")
    <div class="modal-body">
        @(await Html.PartialAsync("_noDinWarning"))
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MedicationName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.MedicationName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MedicationName, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div><!--Modal body end-->
    @Html.ModalFooter("Add")
}