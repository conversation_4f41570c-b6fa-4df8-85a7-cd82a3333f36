@using Cerebrum.DHDR.Services.Models
@using Cerebrum.ViewModels.MedicationDespense
@using Cerebrum.ViewModels.Patient;
@model Cerebrum.ViewModels.MedicationDespense.VMPatientMedSearchDHDR<DrugDispenseSummary, DrugDispense<Patient>, Patient>

<div id="div-med-dhdr-search-result">
    @using (Html.BeginForm("GetDHDR", "DHDR", new { area = "medications" }, FormMethod.Post, true, new { @id = "frm-search-med-dhdr" }))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">
            <h4>Patient Medication Despense Search</h4>
            <hr />
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group">
                @Html.LabelFor(model => model.fromDate, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.fromDate, new { htmlAttributes = new { @class = "date form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.fromDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.toDate, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.toDate, new { htmlAttributes = new { @class = "date form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.toDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    @Html.HiddenFor(model => (object)model.patientRecordId)
                    <input type="submit" value="Search" class="btn btn-default" id="btn-med-dhdr-search" />
                </div>

            </div>

        </div>
        if (Model.medications != null) { await Html.RenderPartialAsync("GetDHDR", (object)Model.medications); }
    }

</div>
<script type="text/javascript">
    $(".datepicker").datepicker({
        format: "mm/dd/yyyy",
    });

</script>
