@model IEnumerable<Cerebrum.ViewModels.Triage.VMWaitListItem>

@Html.RenderPartialAsync("_waitlistPaging");}

<div class="panel panel-info">
    <div class="panel-heading">
        <h3 class="panel-title"> Results <span id="triage-list-count" class="badge cbadge">@Model.Count()</span></h3>
    </div>
    <div class="div-table-container">
        <table id="table-waitlist" class="table table-bordered table-condensed">
            <thead>
                <tr>
              <th></th>
                    <th>
                        @Html.DisplayNameFor(model => model.Patient)
                    </th>
                    <th>Info</th>
                    <th>Note</th>
                    <th>
                        @Html.DisplayNameFor(model => model.Doctor)
                    </th>

                    <th>
                        @Html.DisplayNameFor(model => model.Office)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.AppointmentDateTime)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.AppointmentType)
                    </th>

                    <th>
                        @Html.DisplayNameFor(model => model.RequestedTests)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.AddedTests)
                    </th>                    
                    <th>
                        @Html.DisplayNameFor(model => model.DateEntered)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.EnteredBy)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.TriageDisposition)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.TriageUrgency)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.TriageStatus)
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @{ 
                    int pageNumber = ViewBag.PageNumber!=null? (int)ViewBag.PageNumber:1;
                    int count = pageNumber==1?pageNumber: (25*(pageNumber-1))+1;
                }
                @foreach (var item in Model)
                {
                    <tr id="<EMAIL>">
                        <td>@count</td>
                        @{ await Html.RenderPartialAsync("_waitListItem", (object)item); }
                    </tr>
                                ++count;
                            }
            </tbody>
        </table>

        <div id="requisitionFormDialogforexternal" name="requisitionFormDialogforexternal" style="display:none;"></div>
        <div id="requisitionFormDialogforbloodandurine" name="requisitionFormDialogforbloodandurine" style="display:none;"></div>

    </div>
</div>

@Html.RenderPartialAsync("_waitlistPaging");}

    <script type="text/javascript">

        var globalparam;
        function getRequisitionPatientData() {

         //   alert("getRequisitionPatientData");
          //  alert("appointment id: " + globalparam[0] + " patient record id: " + globalparam[1] + "practice doctor id: " + globalparam[2] );

            var data = { appointmentId: globalparam[0], requisitionPatientId: -1, patientRecordId: globalparam[1], practiceDoctorId: globalparam[2] };
     
            return data;
        }



        function LoadExternal(rowhiddenid)
        {
            var id = "hiddenwaitlistrow_" + rowhiddenid;
     
            var value = document.getElementById(id).value;

            var param = value.split(",");


            globalparam = param;

            var data = { requisitionTemplateId: 0, requisitionPatientId: -1, requisitionId: 0, appointmentId: param[0], practiceDoctorId: param[2], patientRecordId: param[1], officeId: param[3], viewData: false };

            $("#requisitionFormDialogforexternal").load("Requisition/External", data, function () { showRequisitionFormforexternal("External", "1250", "1240", "auto"); });

        }


        function LoadBloodandUrine(rowhiddenid)
        {
            
            var id = "hiddenwaitlistrow_" + rowhiddenid;

            var value = document.getElementById(id).value;

            var param = value.split(",");


            globalparam = param;
     
            var data = {
                requisitionTemplateId: 0,
                requisitionPatientId: -1,
                requisitionId: 0,
                appointmentId: param[0],
                //newForm: 1,
                practiceDoctorId: param[2],
                patientRecordId: param[1],
                officeId: param[3],
                viewData: false
            };
            $("#requisitionFormDialogforbloodandurine").load("Requisition/BloodAndUrine", data, function () { showRequisitionFormforbloodandurine("Blood & Urine", "1250", "1240", "auto"); });

        }




        function showRequisitionFormforexternal(title, width, height, scrollBarY) {
            var screenWidth = window.innerWidth;
            var screenHeight = window.innerHeight - 120;
            var scrollBarX = "hidden";
            if (screenWidth < width) {
                scrollBarX = "auto";
                width = screenWidth;
            }
            if (screenHeight < height) {
                scrollBarY = "auto";
                height = screenHeight;
            }
            $("#requisitionFormDialogforexternal").dialog({
                title: title,
                width: width,
                height: height,
                modal: true,
                close: closeDialog(false),
                open: function (event, ui) {
                    $(this).css("overflow-x", scrollBarX);
                    $(this).css("overflow-y", scrollBarY);
                    $(this).scrollTop("0")
                    $("#imageLoading").hide();
                    $("#datepicker").css("z-index", "9999");
                    $("#timepickerId").css("z-index", "9999");
                },
                draggable: true,
                resizable: true
            });
        }

        function closeDialog(flag) {
            if (flag) {
                $("#requisitionFormDialogforexternal").dialog("close");
            }
        }

        function showRequisitionFormforbloodandurine(title, width, height, scrollBarY) {
            var screenWidth = window.innerWidth;
            var screenHeight = window.innerHeight - 120;
            var scrollBarX = "hidden";
            if (screenWidth < width) {
                scrollBarX = "auto";
                width = screenWidth;
            }
            if (screenHeight < height) {
                scrollBarY = "auto";
                height = screenHeight;
            }
            $("#requisitionFormDialogforbloodandurine").dialog({
                title: title,
                width: width,
                height: height,
                modal: true,
                close: closeDialog2(false),
                open: function (event, ui) {
                    $(this).css("overflow-x", scrollBarX);
                    $(this).css("overflow-y", scrollBarY);
                    $(this).scrollTop("0")
                    $("#imageLoading").hide();
                    $("#datepicker").css("z-index", "9999");
                    $("#timepickerId").css("z-index", "9999");
                },
                draggable: true,
                resizable: true
            });
        }

        function closeDialog2(flag) {
            if (flag) {
                $("#requisitionFormDialogforbloodandurine").dialog("close");
            }
        }


    </script>
