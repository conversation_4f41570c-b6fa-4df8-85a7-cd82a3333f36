@model Cerebrum.ViewModels.Triage.VMWaitlistMain
@{
    ViewBag.Title = "To Do/Triage (Waiting List - Cancellation List)";
    ViewBag.ModuleName = "To Do/Triage (Waiting List - Cancellation List)";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";

}

@section customcss{
    @Styles.Render("~/Areas/Triage/Content/css")
}

@section scripts{
    <script src="~/Areas/Schedule/Scripts/appointments.js"></script>
    <script src="~/Areas/Triage/Scripts/triage.js"></script>

    @*<script src="~/Areas/Schedule/Scripts/schedule.js"></script>*@
    @*<script src="~/Areas/Schedule/Scripts/sharedFunctions.js"></script>*@

}


<div style="margin-left:auto;margin-right:auto;margin-top:10px;" id="div-searchWaitList">
    @{ await Html.RenderPartialAsync("_waitlistSearch", (object)Model.WaitListSearch); }
</div>
<hr/>

@*<div class="text-right" id="waitlist-paging-contrainer">
        @(await Html.PartialAsync("_waitlistPaging", (object)(object)Model.WaitListSearch))
    </div>*@

<div id="waitlist-container">
    @{ await Html.RenderPartialAsync("_waitingList", (object)Model.WaitList); }
</div>
<br />

@*<div id="waitlist-triage-urgencies" class="hidden">

        <ul class="ul-appstatus">
            @foreach (var item in wlTriageUrgencies)
            {
                <li class="app-wailist-item" data-triage-urgency-id="@item.Value">
                    <div>@item.Text</div>
                </li>
            }

        </ul>
        <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
    </div>*@