@model Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCreate

@Html.AntiForgeryToken()
@Html.HiddenFor(model => (object)model.AdmissionActionId)
@Html.HiddenFor(model => (object)model.DateServiced)
@Html.HiddenFor(model => (object)model.PatientName)
@Html.HiddenFor(model => (object)model.IsMultiDate)
@Html.HiddenFor(model => (object)model.DateServicedStr)
@Html.HiddenFor(model => (object)model.IsCreate)
@Html.HiddenFor(model => (object)model.BundleName)
@Html.HiddenFor(model => (object)model.PracticeDoctorId)
@Html.HiddenFor(model => (object)model.BillStatusId)
@Html.HiddenFor(model => (object)model.PaymentMethod)
@Html.HiddenFor(model => (object)model.PatientName)

@{
    for (int i = 0; i < Model.AdmissionActionIds.Count; i++)
    {
        @Html.HiddenFor(model => (object)Model.AdmissionActionIds[i])
    }
}

<div class="form-horizontal">        
       
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @*<div class="form-group form-group-sm">
        @Html.Label("Patient", htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <span class="service-patient-name">@Html.DisplayFor(model => (object)model.PatientName)</span>
        </div>
        @Html.LabelFor(model => model.DateServiced, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <span class="service-date">@Html.DisplayFor(model => (object)model.DateServiced)</span>
        </div>
    </div>*@    

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.BundleTypeId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.BundleTypeId, new SelectList(ViewBag.BundleTypes, "Value", "Text", Model.BundleTypeId),"Choose One", new { @class = "form-control BundleTypeId" })
            @Html.ValidationMessageFor(model => model.BundleTypeId, "", new { @class = "text-danger" })
        </div>       
    </div>
    
    @*<div class="form-group form-group-sm">
        @Html.LabelFor(model => model.BundleId, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.BundleId, new SelectList(ViewBag.Bundles, "Value", "Text", Model.BundleId), new { @class = "form-control BundleId" })
            @Html.ValidationMessageFor(model => model.BundleId, "", new { @class = "text-danger" })
        </div>
    </div>*@

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.BundleItems, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-10"> 
            <div class="multi-bundle-container">
                @{ await Html.RenderPartialAsync("_addBundleItemList", (object)Model.BundleItems);}
            </div>           
            @Html.ValidationMessageFor(model => model.BundleItems, "", new { @class = "text-danger" })
        </div>
    </div>
         
    @*<div class="form-group form-group-sm">
        @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId), new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
        </div>
    </div>
       
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.BillStatusId, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.BillStatusId, new SelectList(ViewBag.BillStatuses, "Value", "Text", Model.BillStatusId), new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.BillStatusId, "", new { @class = "text-danger" })
        </div>
        @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.PaymentMethod, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
        </div>
    </div>*@    

    @*<div class="hd-service-code-container">
        @Html.ValidationMessageFor(model => model.ServiceCodes, "", new { @class = "text-danger" })
        @{ await Html.RenderPartialAsync("_hdServiceCodesList", (object)Model.ServiceCodes); }
    </div>*@      
</div>


