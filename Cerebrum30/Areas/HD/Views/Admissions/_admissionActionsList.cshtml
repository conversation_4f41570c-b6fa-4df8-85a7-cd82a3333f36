@model IEnumerable<Cerebrum.ViewModels.HospitalDaysheet.VMHDAdmissionAction>
<style>
    .btn-primary{
         background-color: #428bca;
    }
    .admission-actions-panel
    {
        max-height: 400px;
        overflow:auto;
    }
    .td-date {
        cursor: pointer;
    }
    .selectedCell {
        background-color:#e6eaf7;
    }
    .admission-actions-panel td{
        /*vertical-align: bottom !important;*/       
    }

    .ul-hd-service .btn-popover-container {
        cursor: pointer;      
    }
</style>
<div class="panel11 panel-default11">
    <div class="panel-heading11">Admission Actions (@Model.Count())      
        
        <input type="text" class="" id="ctrl_dummy" style="width:1px !important; border:1px solid #fff !important " readonly />
    Click on a date below to mark the start date:    
    <input type="text" style=" height:23px;padding-left:3px; margin-right:27px; color: #428bca; border: 0px solid #fff !important" class="" id="ctrl_dateFilter" readonly /> 
        # of days (day range)
        @*<select id="ddl_dayRangeSelect" style="height:23px; margin-right:17px"></select>*@
      <input type="text"  id="ddl_dayRangeSelect" maxlength="3" style="width:30px;margin-right:27px;padding-left: 3px;"/>

    Select / Reset <input type="checkbox" id="chk_select" />
    </div>
    <div class="panel-body11 admission-actions-panel">
        <table class="table">
            <tr>
                <th>
                    @*<input name="selectAllSeviceDates" id="selectAllSeviceDates" type="checkbox" /> All*@
                    <button id="btn-checked-all-adm-act" type="button" class="btn btn-xs"><span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Select / Check all dates" class="glyphicon glyphicon-check"></span></button>
                    <button id="btn-unchecked-all-adm-act" type="button" class="btn btn-xs"><span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Deselect / Uncheck all dates" class="glyphicon glyphicon-unchecked"></span></button>
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.DateServiced)
                </th>
                <th>Services</th>
                <th>Bonus Code</th>
                <th>Billing</th>
            </tr>

            @foreach (var item in Model) {
                <tr>
                    <td class="td-adm-cb">
                        <input data-bill-status-id="@item.BillStatusId" data-service-date="@item.DateServiced.ToShortDateString()" class="cb-service-date-select" name="AdmissionActionIds" type="checkbox" value="@item.Id" id="<EMAIL>"/>
                    </td>

                    <td class="td-date" data-id="@item.Id">@item.DateServiced</td>

                    <td class="td-adm-services">
                        <ul class="ul-hd-service">
                            @foreach(var service in item.Services)
                            {
                                var hasBonusCode = !String.IsNullOrWhiteSpace(service.BonusCode) ? true : false;
                                <li class="li-hd-service">
                                    <div data-hd-service-id="@service.Id" class="hd-service-list-item">
                                        <div class="btn-popover-container">
                                            <span type="button" class="popover-btn">
                                                @if (hasBonusCode)
                                                { <span class="glyphicon glyphicon-xbt text-success"> </span> }
                                                <span style="color:@service.BillStatusColor"> @service.BundleName</span>
                                            </span>
                                            <div class="btn-popover-title">Menu</div>
                                            <div class="btn-popover-content">
                                                <ul class="ul-popover-list">
                                                    <li><a href="#" data-hd-service-id="@service.Id" class="btn-edit-hd-service">Edit</a></li>
                                                    <li><a href="#" data-hd-service-id="@service.Id" data-confirm-msg="Delete @service.BundleName for @service.DateServiced.ToShortDateString()?" class="btn-delete-hd-service">Delete</a></li>
                                                    <li style="color:@service.BillStatusColor;">Status: @service.BillStatus</li>
                                                    @if (hasBonusCode)
                                                    {
                                                        <li>Bonus Code: @service.BonusCode</li>
                                                        @*<li>Bonus Code: <span class="glyphicon glyphicon-gift text-success"> </span> @service.BonusCode</li>*@
                                                    }
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                          }
                        </ul>

                    </td>
                    <td class="td-bonus-code">
                        @{ 
                            if(String.IsNullOrWhiteSpace(item.BonusCode))
                            {
                                if(item.Services != null && item.Services.Any())
                                {
                                    if(item.BillStatusId <= 1) { 
                                    <div class="btn-modal-container">
                                    <button class="btn btn-xs btn-default">Add Bonus Code</button>
                                        <div class="btn-modal-content">
                                            <form data-admission-action-id="@item.Id" data-service-date="@item.DateServiced.ToShortDateString()" class="frm-bonus-code" method="post" role="form" action="@Url.Action("CreateHDBonusCode", "admissions", new { area = "hd" })">
                                                @Html.ModalHeader("Add Bonus Code for " + Html.DisplayFor(modelItem => (object)item.DateServiced))
                                                <div class="modal-body">
                                                @{ 
                                                    var bonusCode = new Cerebrum.ViewModels.HospitalDaysheet.VMHDBonusCode();
                                                    bonusCode.AdmissionActionId = item.Id;
                                                    bonusCode.Date = item.DateServiced;
                                                    bonusCode.Id = 0;
                                                    bonusCode.Services = item.Services.Select((System.Func<dynamic, object>)(s=> new Cerebrum.ViewModels.HospitalDaysheet.VMBonusCodeService() { ServiceId = s.Id, ServiceName = s.BundleName })).ToList();
                                                    await Html.RenderPartialAsync("_bonusCode", (object)bonusCode);
                                                  }
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Add Bonus Code</button>
                                                    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    }
                                }
                            }
                            else
                            {
                               <ul class="ul-hd-service">                                    
                                    <li class="li-hd-service">
                                        <div data-admission-action-id="@item.Id" class="hd-service-list-item">
                                            <div class="btn-popover-container">
                                                <span type="button" class="popover-btn"><span class="glyphicon glyphicon-xbt text-success"> </span> @item.BonusCode</span>
                                                <div class="btn-popover-title">Menu</div>
                                                <div class="btn-popover-content">
                                                    <ul class="ul-popover-list">                                                        
                                                        <li><a href="#" data-admission-action-id="@item.Id" data-confirm-msg="Delete @item.BonusCode for @item.DateServiced.ToShortDateString()?" class="btn-delete-hd-bonus-code">Delete</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </li>                                 
                                </ul>
                            }
                        }
                    </td>
                    <td style="white-space: nowrap;">
                        @{  string admissionActionClaimDisplay = "none";
                            string admissionActionBillDisplay = "none";
                            if (item.BillStatusId > 1) // billed
                            {
                                admissionActionClaimDisplay = "inline";
                            }
                            if (item.Services.Count > 0) // has service code
                            {
                                admissionActionBillDisplay = "inline";
                            }
                        }
                        <span style="display:@admissionActionBillDisplay;"><a id="btn-admission-action-claim-bill" data-admission-action-id="@item.Id" data-error-message="" href="#"><strong>Bill</strong></a>&nbsp;&nbsp;&nbsp;</span>
                        <span style="display:@admissionActionBillDisplay;"><a id="btn-admission-action-claim-unbill" data-admission-action-id="@item.Id" data-error-message="" href="#"><strong>Unbill</strong></a>&nbsp;&nbsp;&nbsp;</span>
                        <span style="color: @item.BillStatusColor; display:inline;"><a class="admission-action-claim" style="display:@admissionActionClaimDisplay;" data-admission-id="@item.AdmissionId" data-admission-action-id="@item.Id" data-modal-url="@Url.Action("appointmentclaim", "groupbill", new { area = "bill" })" href="#"><strong>Claims</strong></a>&nbsp;&nbsp;&nbsp;</span>
                        <span style="color: @item.BillStatusColor;"><a class="admission-action-claim-error text-danger" data-admission-action-id="@item.Id" data-error-message="" style="display:none;" href="#"><strong>Claims Error</strong></a></span>
                    </td>
                    @*<td class="td-hd-action-menu">

                        <div style="margin-right:5px;" class="pull-left bundle-add">
                            <div class="btn-modal-container">
                                <button class="btn-sm btn-default">Add Bundle</button>

                                <div class="btn-modal-content">
                                    <form data-admission-action-id="@item.Id" data-service-date="@item.DateServiced.ToShortDateString()" class="frm-hd-service-create" method="post" role="form" action="@Url.Action("CreateHDService", "admissions", new { area = "hd" })">
                                        @Html.ModalHeader("Add bundle for " + Html.DisplayFor(modelItem => (object)item.DateServiced))
                                        <div class="modal-body">
                                            @{ await Html.RenderPartialAsync("_hdServiceCreate", (object)new Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCreate());}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Add Bundle</button>
                                            <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div style="margin-right:5px;" class="pull-left code-add">
                            <div class="btn-modal-container">
                                <button class="btn-sm btn-default">Add Code</button>

                                <div class="btn-modal-content">
                                    <form data-admission-action-id="@item.Id" data-service-date="@item.DateServiced.ToShortDateString()" class="frm-hd-service-code-create" method="post" role="form" action="@Url.Action("CreateHDServiceCode", "admissions", new { area = "hd" })">
                                        @Html.ModalHeader("Add Code for " + Html.DisplayFor(modelItem => (object)item.DateServiced))
                                        <div class="modal-body">
                                            @{ await Html.RenderPartialAsync("_hdServiceCodeAdd", (object)new Cerebrum.ViewModels.HospitalDaysheet.VMHDAddCode());}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Add Code</button>
                                            <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>*@
                </tr>
}

        </table>
    </div>
    <div class="panel-footer11" style="margin-bottom: 21px;  margin-top: 3px;">
        <div style="margin-right:5px;" class="pull-left multi-date-select">
            <div class="btn-modal-container">
                <button class="btn btn-sm btn-default">Add Bundle</button>

                <div class="btn-modal-content">
                    <form data-multi-dates="1" class="frm-hd-service-create" method="post" role="form" action="@Url.Action("CreateHDService", "admissions", new { area = "hd" })">
                        @Html.ModalHeader("Add bundle for multiple dates")
                        <div class="modal-body">
                            @{ 
                                var multiDate = new Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCreate();
                                multiDate.IsMultiDate = true;
                                await Html.RenderPartialAsync("_hdServiceCreate", (object)multiDate);
                            }
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-primary btn-sm modal-submit-btn btn-spacing">Add Bundle</button>
                            <button type="button" class="btn btn-primary btn-sm modal-submit-btn btn-spacing btn-add-bundle-close">Add Bundle and Close</button>
                            <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div style="margin-right:3px;" class="pull-left multi-date-select-code">
            <div class="btn-modal-container">
                <button class="btn btn-sm btn-default">Add Code</button>

                <div class="btn-modal-content">
                    <form data-multi-dates="1" class="frm-hd-service-code-create" method="post" role="form" action="@Url.Action("CreateHDServiceCode", "admissions", new { area = "hd" })">
                        @Html.ModalHeader("Add code for multiple dates")
                        <div class="modal-body">
                            @{
                                var multiDateCode = new Cerebrum.ViewModels.HospitalDaysheet.VMHDAddCode();
                                multiDateCode.IsMultiDate = true;
                                multiDateCode.NumberOfServices = 1;
                                await Html.RenderPartialAsync("_hdServiceCodeAdd", (object)multiDateCode);
                            }
                            
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-primary btn-sm modal-submit-btn btn-spacing">Add Code</button>
                            <button type="button" class="btn btn-primary btn-sm modal-submit-btn btn-spacing btn-add-code-close">Add Code and Close</button>
                            <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div style="margin-right:3px;" class="pull-left multi-date-bonus-code">
            <div class="btn-modal-container">
                <button class="btn btn-sm btn-default _5534534">Add Bonus Code</button>

                <div class="btn-modal-content">
                    <form data-multi-dates="1" class="frm-bonus-code" method="post" role="form" action="@Url.Action("CreateHDBonusCode", "admissions", new { area = "hd" })">
                        @Html.ModalHeader("Add Bonus code for multiple dates")
                        <div class="modal-body">
                            @{
                                var multiBonus = new Cerebrum.ViewModels.HospitalDaysheet.VMHDBonusCode();
                                multiBonus.IsMultiDate = true;
                                multiBonus.Date = System.DateTime.Now;
                                await Html.RenderPartialAsync("_bonusCode", (object)multiBonus);
                            }
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Add Bonus</button>
                            <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    
    </div>
</div>
<script type="text/javascript" src="~/Scripts/moment.min.js"></script>
<script type="text/javascript">
    

    //var isWithinDateRange = function (startDate, EndDate, dateToCheck) {
    //    $('.td-date').each(function (i) {
    //        console.log($(this).html());
    //    });
    //};

    var buildDayRange = function (start, end) {
        for (i = start; i <= end; i++) {
            $('#ddl_dayRangeSelect').append($('<option/>', {
                value: i,
                text: i
            }));
        }
    };

    var resetCheckBoxSelect = function () {
        $('.cb-service-date-select').prop('checked', false);
        $("#ctrl_dateFilter").val('');
        $('#ddl_dayRangeSelect').val('');
        $('.td-date').removeClass('selectedCell');
    };
   

    $(document).ready(function () {
        // $("#ctrl_dateFilter").datepicker({});
        buildDayRange(1, 10);

        $('.td-date').on('click', function () {   
            $("#ctrl_dateFilter").val($(this).html());
            var id = $(this).attr('data-id');
            eval("$('#chk_" + id + "').prop('checked', true)");

            $('.td-date').removeClass('selectedCell');
            $(this).addClass('selectedCell');
        });



        $('#chk_select').on('click', function () {
            if ($(this).is(':checked') && $('#ctrl_dateFilter').val().length > 0 && $.isNumeric($('#ddl_dayRangeSelect').val()) {
                var dateStr = $('#ctrl_dateFilter').val();
                var dayRange = Math.floor($('#ddl_dayRangeSelect').val());

                var startDate = moment(dateStr, 'MM/DD/YYYY', true).format();
                var endDate = moment(dateStr, 'MM/DD/YYYY').add(dayRange, 'd').format();

                $('.td-date').each(function (i) {
                    if (moment($(this).html(), 'MM/DD/YYYY').isBetween(moment(startDate), moment(endDate), null, '[]')) {
                        var id = $(this).attr('data-id');
                        eval("$('#chk_" + id + "').prop('checked', true)");
                    }
                });                
            } 
            else if ($(this).is(':checked') && $('#ctrl_dateFilter').val().length == 0) {
                alert('Please select a date');
                return false;  
            }
            else if (!$.isNumeric($('#ddl_dayRangeSelect').val()) {
                $('#ddl_dayRangeSelect').focus();
                alert('Number of Days should be an Integer');
                return false;
            }
            else if (!$(this).is(':checked')){
                resetCheckBoxSelect();
            }
        });
    }); 
</script>