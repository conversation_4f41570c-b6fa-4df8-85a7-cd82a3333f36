@model IEnumerable<Cerebrum.ViewModels.HospitalDaysheet.VMHDAdmissionItem>
@{    
    var hdURL = Html.Raw((string)ViewBag.HDUrl);
    var hdSelectedDate = Convert.ToDateTime(ViewBag.HDSelectedDate);
}
<script type="text/javascript">
    $(function () {       
        var hdURL = '@hdURL';
        window.history.pushState("string", "Title", hdURL);
    });
</script>
<table id="tbl-hd-adm-list" class="table" data-hd-url="">
    <thead>
        <tr>
            
            <th>
                Patient Name
            </th>
            @*<th>
                    @Html.DisplayNameFor(model => model.DateOfBirth)
                </th>*@
            <th>
                @Html.DisplayNameFor(model => model.PatientOHIP)
            </th>
            @*<th>
                    @Html.DisplayNameFor(model => model.GenderDesc)
                </th>*@
            <th>
                @Html.DisplayNameFor(model => model.AdmissionType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DiagnosisCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateAdmitted)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateDischarge)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PaymentMethodDesc)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ReferralDoctor)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.FamilyDoctor)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Hospital)
            </th>

            <th style="width:25%;">
                Services (@hdSelectedDate.ToShortDateString())
            </th>
            @*<th>
                    @Html.DisplayNameFor(model => model.HospitalCode)
                </th>*@
            @*<th></th>*@
        </tr>
    </thead>
@foreach (var item in Model) {
    <tr>        
        <td class="td-hd-menu">
            @item.PatientLastName @item.PatientFirstName
            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn">
                    <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                </button>
                <div class="btn-popover-title">Menu</div>
                <div class="btn-popover-content">
                    <ul class="ul-popover-list">
                        <li><a class="btn-edit-patient" data-el="modal-lg" href="#" data-modal-url="@Url.Action("edit","patients",new { area="", patientId = item.PatientRecordId })">Edit Patient</a></li>
                        <li><a href="#" data-admission-id="@item.Id" class="btn-edit-admission">Edit Admission</a></li>
                        <li><a href="#" data-admission-id="@item.Id" class="btn-view-admission-actions">Admissions Page</a></li>
                        @if (item.AdmissionActionId > 0)
                        {
                            int paymentMethodId = (int)item.PaymentMethod;
                            <li class="btn-add-admission-bundle">
                                <div class="btn-modal-container">
                                    <a href="#"><span class="">Add Bundle</span></a>

                                    <div class="btn-modal-content">
                                        <form data-patient-name="@item.PatientLastName @item.PatientFirstName"
                                              data-patient-payment-method="@paymentMethodId"
                                              data-doctor-name="@item.PracticeDoctor"
                                              data-practice-doc-id="@item.PracticeDoctorId"
                                              data-admission-action-id="@item.AdmissionActionId"
                                              data-service-date="@item.SelectedDate.Value.ToShortDateString()"
                                              class="frm-hd-service-create" method="post" role="form" action="@Url.Action("CreateHDService", "admissions", new { area = "hd" })">
                                            @Html.ModalHeader("Add bundle for " + Html.DisplayFor(modelItem => (object)item.DateAdmitted))
                                            <div class="modal-body">
                                                @{ await Html.RenderPartialAsync("_hdServiceCreate", (object)new Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCreate());}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="submit" class="btn btn-primary btn-sm modal-submit-btn btn-spacing">Add Bundle</button>
                                                <button type="button" class="btn btn-primary btn-sm modal-submit-btn btn-spacing btn-add-bundle-close">Add Bundle and Close</button>
                                                <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </li>
                        }
                        <li class="divider"></li>
                        <li><a href="#" data-admission-id="@item.Id" data-confirm-msg="Delete admission for @item.PatientLastName @item.PatientFirstName?" class="btn-delete-admission">Delete Admission</a></li>
                        <li><a href="#" data-admission-id="@item.Id" data-discharge-date="@hdSelectedDate" data-url="@Url.Action("Discharge","Admissions",new { area = "hd" })" class="btn-discharge-admission">Discharge</a></li>
                        <li><a class="" href="@Url.Action("index", "externaldocument", new { area="externaldocument", PatientRecordId=item.PatientRecordId })" target="_blank">External Documents</a></li>
                        <li><a class="" href="Bill/report?reportType=billinghistory&patientRecordId=@item.PatientRecordId" target="_blank">Billing History</a></li>
                    </ul>
                </div>
            </div>
        </td>
        @*<td>
            @item.DateOfBirth
        </td>*@
        <td>
            @item.PatientOHIP
        </td>
        @*<td>
            @item.GenderDesc
        </td>*@
        <td>
            @item.AdmissionType
        </td>
        <td>
            @item.DiagnosisCode
        </td>
        <td>
            @item.DateAdmitted
        </td>
        <td>
            @item.DateDischarge
        </td>
        <td>
            @item.PaymentMethodDesc
        </td>
        <td>
            @item.ReferralDoctor
        </td>
        <td>
            @item.FamilyDoctor
        </td>
        <td>
            @item.Hospital
        </td>
        <td class="td-adm-item-services">
            <ul class="ul-hd-service">
                @foreach (var service in item.Services)
                {
                    var hasBonusCode = !String.IsNullOrWhiteSpace(service.BonusCode) ? true : false;
                    <li class="li-hd-service">
                        <div data-hd-service-id="@service.Id" class="hd-service-list-item">
                            <div class="btn-popover-container">
                                <span type="button" class="popover-btn">
                                    @if (hasBonusCode)
                                    { <span class="glyphicon glyphicon-xbt text-success"> </span> }
                                    <span style="color:@service.BillStatusColor"> @service.BundleName</span>
                                </span>
                                <div class="btn-popover-title">Menu</div>
                                <div class="btn-popover-content">
                                    <ul class="ul-popover-list">
                                        <li><a href="#" data-hd-service-id="@service.Id" class="btn-edit-hd-service">Edit</a></li>
                                        <li><a href="#" data-hd-service-id="@service.Id" data-confirm-msg="Delete @service.BundleName for @service.DateServiced.ToShortDateString()?" class="btn-delete-hd-service">Delete</a></li>
                                        <li style="color:@service.BillStatusColor;" >Status: @service.BillStatus</li>
                                        @if (hasBonusCode)
                                        {
                                            <li>Bonus Code: @service.BonusCode</li>                                                        
                                        }
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                }
            </ul>

        </td>
        @*<td>
            @item.HospitalCode
        </td>*@       
        @*<td class="td-hd-menu">
            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn">
                    <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                </button>
                <div class="btn-popover-title">Menu</div>
                <div class="btn-popover-content">
                    <ul class="ul-popover-list">
                    <li><a href="#" data-admission-id="@item.Id" class="btn-edit-admission">Edit Admission</a></li>                        
                    <li><a href="#" data-admission-id="@item.Id" class="btn-view-admission-actions">Admissions Page</a></li>
                        @if (item.AdmissionActionId > 0)
                        {
                        <li class="btn-add-admission-bundle">
                            <div class="btn-modal-container">
                                <a href="#"><span class="">Add Bundle</span></a>

                                <div class="btn-modal-content">
                                    <form data-patient-name="@item.PatientLastName @item.PatientFirstName" 
                                          data-doctor-name="@item.PracticeDoctor" 
                                          data-practice-doc-id="@item.PracticeDoctorId" 
                                          data-admission-action-id="@item.AdmissionActionId" 
                                          data-service-date="@item.SelectedDate.Value.ToShortDateString()" 
                                          class="frm-hd-service-create" method="post" role="form" action="@Url.Action("CreateHDService", "admissions", new { area = "hd" })">
                                        @Html.ModalHeader("Add bundle for " + Html.DisplayFor(modelItem => (object)item.DateAdmitted))
                                        <div class="modal-body">
                                            @{ await Html.RenderPartialAsync("_hdServiceCreate", (object)new Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCreate());}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary btn-sm modal-submit-btn btn-spacing">Add Bundle</button>
                                            <button type="button" class="btn btn-primary btn-sm modal-submit-btn btn-spacing btn-add-bundle-close">Add Bundle and Close</button>
                                            <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal">Close</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </li>
                       }
                    <li class="divider"></li>
                    <li><a href="#" data-admission-id="@item.Id" data-confirm-msg="Delete admission for @item.PatientLastName @item.PatientFirstName?" class="btn-delete-admission">Delete Admission</a></li> 
                    <li><a href="#" data-admission-id="@item.Id" data-discharge-date="@hdSelectedDate" data-url="@Url.Action("Discharge","Admissions",new { area = "hd" })" class="btn-discharge-admission">Discharge</a></li> 
                    </ul>
                </div>
             </div>
        </td>*@
    </tr>
}
</table>
@{
    string xFrameOptionKey = "X-Frame-Options";
    string[] xFrameOptions = Response.Headers.GetValues(xFrameOptionKey);
    if (xFrameOptions != null && xFrameOptions.Length > 1)
    {
        List<string> xFrameOptionList = xFrameOptions.Distinct().ToList();
        if (xFrameOptionList.Count() != xFrameOptions.Length)
        {
            Response.Headers.Remove(xFrameOptionKey);

            foreach (var xFrameOption in xFrameOptionList)
            {
                Response.Headers.Add(xFrameOptionKey, xFrameOption);
            }
        }
    }
}
