@model Cerebrum.ViewModels.HospitalDaysheet.VMHDMainAction
@{ 
    int paymentMethodId = (int)Model.Admission.PaymentMethod;
}
@Html.ModalHeader("Admission Actions")
<style>
    .__893745 .form-control-static {
        color: #808080;
        /*margin-right: 10px;*/
    }

    @@media screen and (min-width: 768px) {
    #admissionactionsModal .modal-lg {
        width: 85% !important;    
    }
    }
</style>

<div class="modal-body __893745">   
    <div class="row">
        <div class="col-md-12">
            <span class="label-bold">Patient</span>
            <span id="patient-admission-action" class="form-control-static">@Model.Admission.PatientLastName @Model.Admission.PatientFirstName</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Gender</span>
            <span class="form-control-static">@Model.Admission.GenderDesc</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Type</span>
            <span class="form-control-static">@Model.Admission.AdmissionType</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Diagnosis Code</span>
            <span class="form-control-static">@Model.Admission.DiagnosisCode</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Hospital</span>
            <span class="form-control-static">@Model.Admission.Hospital</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Admitted</span>
            <span class="form-control-static">@Model.Admission.DateAdmitted.ToShortDateString()</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Discharged</span>
            <span class="form-control-static">@Model.Admission.DateDischarge.ToShortDateString()</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Payment</span>
            <span id="patient-payment-method" data-patient-payment-method="@paymentMethodId" class="form-control-static">@Model.Admission.PaymentMethodDesc</span>&nbsp;&nbsp;&nbsp;
            <span class="label-bold">Doctor</span>
            <span id="doctor-admission-action" data-practice-doc-id="@Model.Admission.PracticeDoctorId" class="form-control-static">@Model.Admission.PracticeDoctor</span>
            @*<div class="form-inline">
                <div class="form-group form-group-sm">
                    @Html.Label("Patient", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p id="patient-admission-action" class="form-control-static">@Model.Admission.PatientLastName @Model.Admission.PatientFirstName</p>
                    </div>
                    @Html.Label("Gender", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.GenderDesc</p>
                    </div>
                    @Html.Label("Type", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.AdmissionType</p>
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    @Html.Label("Diagnosis Code", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.DiagnosisCode</p>
                    </div>
                    @Html.Label("Hospital", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.Hospital</p>
                    </div>
                    @Html.Label("Hospital Code", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.HospitalCode</p>
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    @Html.Label("Admitted", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.DateAdmitted.ToShortDateString()</p>
                    </div>
                    @Html.Label("Discharged", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p class="form-control-static">@Model.Admission.DateDischarge.ToShortDateString()</p>
                    </div>  
                    
                    @Html.Label("Doctor", new { @class = "control-label col-md-2" })
                    <div class="col-md-2">
                        <p id="doctor-admission-action" data-practice-doc-id="@Model.Admission.PracticeDoctorId" class="form-control-static">@Model.Admission.PracticeDoctor</p>
                    </div>                 
                </div>
            </div>*@            
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="col-md-12 text-right">
            @if (CerebrumUser.HasPermission("Billing,BillingAdmin"))
            {
                <a data-admission-ids="@Model.Admission.Id" data-admission-action-ids="" class="btn btn-default btn-sm" id="btn-adm-prebill-hospital-all" style=""><span class="glyphicon glyphicon-usd default-text-color"> </span> Bill Admission</a>
                <a data-admission-ids="@Model.Admission.Id" data-admission-action-ids="" class="btn btn-default btn-sm" id="btn-adm-view-prebill-hospital" style=""><span class="glyphicon glyphicon-usd default-text-color"> </span> Bill Date(s)</a>
                <a data-admission-ids="@Model.Admission.Id" data-admission-action-ids="" class="btn btn-default btn-sm" id="btn-adm-view-unbill-hospital" style=""><span class="glyphicon glyphicon-usd default-text-color"> </span> Unbill</a>
                <a data-admission-ids="@Model.Admission.Id" data-admission-action-ids="" class="btn btn-default btn-sm" id="btn-adm-view-claim-hospital" style=""><span class="glyphicon glyphicon-usd default-text-color"> </span> Claims</a>
            }
        </div>
    </div>
    <div data-admission-id="@Model.Admission.Id" id="admission-actions-list">
        @{ await Html.RenderPartialAsync("_admissionActionsList", (object)Model.Actions);}
    </div>
</div><!--Modal body end-->
@Html.ModalFooter(isInfoModal:true)