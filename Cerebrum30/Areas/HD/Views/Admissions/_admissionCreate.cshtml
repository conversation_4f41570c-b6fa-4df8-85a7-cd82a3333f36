@model Cerebrum.ViewModels.HospitalDaysheet.VMHDAdmission

@using (Html.BeginForm("create", "admissions", new { area = "hd" }, FormMethod.Post, true, new { @id = "frm-admission-create", @class = "admission-form" }))
{
    @Html.<PERSON>eader("Create Admission")
    <div class="modal-body">        
        @*@(await Html.PartialAsync("_validationSummary"))*@
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.PatientRecordId)
        @Html.HiddenFor(model => (object)model.ReferralDoctorId)
        @Html.HiddenFor(model => (object)model.DiagnosisCodeId)

        <div class="form-horizontal">

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PatientFullName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="min-width:250px;" class="pull-left">
                    @Html.EditorFor(model => model.PatientFullName, new { htmlAttributes = new { @class = "form-control" } })
                    
                    </div>
                    <div class="pull-left">
                        <a style="margin-left:5px;" class="btn btn-xs btn-default btn-add-patient" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add New Patient" href="#"><span class="glyphicon glyphicon-plus-sign text-primary"></span></a>
                    </div>
                    <div class="clearfix"></div>
                    <p class="help-block">Select patient from the list</p>
                    @Html.ValidationMessageFor(model => model.PatientFullName, "", new { @class = "text-danger" })
                    @Html.ValidationMessageFor(model => model.PatientRecordId, "", new { @class = "text-danger" })
                </div>
            </div>        
        
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">                    
                    @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId),"Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
                </div>
            </div>  

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AdmissionTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.AdmissionTypeId, new SelectList(ViewBag.AdmissionTypes, "Value", "Text", Model.AdmissionTypeId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AdmissionTypeId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DiagnosisCode, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DiagnosisCode, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.DiagnosisCode, "", new { @class = "text-danger" })
                </div>
            </div>            

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DateAdmitted, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DateAdmitted, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.DateAdmitted, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DateDischarge, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DateDischarge, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.DateDischarge, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReferralDoctor, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="min-width:250px;" class="pull-left">
                    @Html.EditorFor(model => model.ReferralDoctor, new { htmlAttributes = new { @class = "form-control" } })
                    
                     </div>
                    <div class="pull-left">
                        <a style="margin-left:5px;" class="btn btn-xs btn-default btn-add-ext-doctor" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add New Doctor" href="#"><span class="glyphicon glyphicon-plus-sign text-primary"></span></a>
                    </div>
                    <div class="clearfix"></div>
                    <p class="help-block">Select referral doctor from the list</p>
                    @Html.ValidationMessageFor(model => model.ReferralDoctor, "", new { @class = "text-danger" })
                </div>
            </div>           
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PaymentMethod, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.HospitalId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.HospitalId, new SelectList(ViewBag.Hospitals, "Value", "Text", Model.HospitalId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.HospitalId, "", new { @class = "text-danger" })
                </div>
            </div>

        </div><!-- end form horizontal-->
    </div><!--Modal body end-->
    @Html.ModalFooter("Add Admission", "blue")
}


