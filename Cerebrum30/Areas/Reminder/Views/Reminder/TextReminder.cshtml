@model Cerebrum.ViewModels.Reminder.ReminderResponse

<script type="text/javascript" src="~/Areas/Reminder/Scripts/TextReminder.js"></script>

<h2>Text Reminder</h2>
<hr />
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Office</label></div>
    <div class="col-sm-3">
        @if (Model.offices.Count() == 1)
        {
        @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm" })
        }
        else
        {
        @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "Please Select Office", htmlAttributes: new { @class = "form-control input-sm" })
        }
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label title="">Text</label></div>
    <div class="col-sm-6">
        <textarea id="body" name="body" rows="12" class="form-control"></textarea>
    </div>
    <div class="col-sm-4">
        <span style="font-weight: bold;">Keywords:</span><br />
        {{patientFirstName}} <span class="text-danger">*</span><br />
        {{patientLastName}} <span class="text-danger">*</span><br />
        {{doctorFirstName}}<br />
        {{doctorLastName}}<br />
        {{appointmentDate}}<br />
        {{appointmentTime}} <span class="text-danger">*</span><br />
        {{officeName}} <span class="text-danger">*</span><br />
        {{officeFullName}} <span class="text-danger">*</span><br />
        {{officeAddress}} <span class="text-danger">*</span><br />
        {{testName}}<br />
        {{testFullName}}<br />
        {{confirmedLink}} <span class="text-danger">*</span><br />
        {{multipleAppointmentsPerDay}} <span class="text-danger">*</span>
        <hr />
        <span class="text-danger">*</span> only these keywords can be used if {{multipleAppointmentsPerDay}} is applied

    </div>
</div>
<hr />
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Schedule Text 1</label></div>
    <div class="col-sm-1">
        <input class="form-control" id="schedule" name="schedule" type="text" value="">
    </div>
    <div class="col-sm-6">
        day(s) before appointment
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Schedule Text 2</label></div>
    <div class="col-sm-1">
        <input class="form-control" id="schedule2" name="schedule2" type="text" value="">
    </div>
    <div class="col-sm-6">
        day(s) before appointment
    </div>
</div>
<hr />
<br />
<div class="form-group col-sm-12">
    <button type="button" class="btn btn-default btn-sm btn-primary" id="buttonTextReminderSave" name="buttonTextReminderSave">Save</button>
</div>
<br />
<br />
<br />
<br />
<br />