@model Cerebrum.ViewModels.Schedule.VMStaffSchedule
@{
    ViewBag.Title = "Staff Schedule";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<script src="~/Areas/Schedule/Scripts/cerebrum-sch-api-service.js"></script>
<script src="~/Areas/Schedule/Scripts/staffSchedule.js"></script>
<script>
    $(document).ready(function () {
        $("#UserDropDown,#DoctorDropDown").change(function () {
            var userId = $(this).val();
            window.location.href = '@Url.Action("Index")' + '?user=' + userId;
        });
    });
</script>

<h2>Staff Schedule</h2>
<h4>@(Model != null && Model.SelectedUser != null ? Model.SelectedUser.FullName : "")</h4>
<div class="form-inline">
    @Html.DropDownList("UserDropDown", new SelectList(Model.Users, "UserId", "FullName", Model.SelectedUser != null ? Model.SelectedUser.UserId : 0), "Select Staff...", new { @class = "form-control btn btn-default" })
    @Html.DropDownList("DoctorDropDown", new SelectList(Model.Doctors, "UserId", "FullName", Model.SelectedUser != null ? Model.SelectedUser.UserId : 0), "Select Doctor...", new { @class = "form-control btn btn-default" })
</div>
<br />
@Html.ValidationSummary(false, "", new { @class = "text-danger" })
<br />
@if (Model != null)
{
    foreach (var item in Model.days)
    {
        <div class="panel panel-success">
            <div class="panel-heading">@item.dayOfWeek</div>
            <div class="panel-body">
                <div class="form-group pull-left">
                    <input type="submit" data-week="@item.dayOfWeek" value="Add New Office on @item.dayOfWeek" class="btn btn-default btn-sch-new-office col-xs-offset-4" />
                </div>
                <table id="<EMAIL>" class="table table-striped">
                    <tbody>
                        <tr>
                            <td>
                                @{
                                    var schedules = item.schedules.OrderBy((System.Func<dynamic, object>)(o => o.startTime)).ToList();
                                }
                                @foreach (var f in schedules)
                                {

                                    @(await Html.PartialAsync("_userScheduleDay", (object)(object)f))
                                }
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    }
}
<br />
<br />
<!-- Modal -->
<div class="modal fade" id="sch-alert-modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h2 class="modal-title"><span class=""></span>Schedule Change Warning</h2>
            </div>
            <div class="modal-body">
                <blockquote>
                    <p class="modal-body-text">
                    </p>
                </blockquote>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger confirmclosed" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>