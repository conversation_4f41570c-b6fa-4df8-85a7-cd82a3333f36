@model Cerebrum.ViewModels.Schedule.VMSchedule
@{
    string ptNewColor = "#DC143C"; //crimson
    string ptNewTriageColor = "#228B22"; //forestgreen
    string ptNew2PtColor = "#FF00FF"; //fuchsia
    string ptNewRegularColor = "#FFD700"; //Gold
    string ptRegularColor = "#1E90FF"; //dodgerblue
    string ptRegular2Color = "#0000FF"; //Blue
    string reservedSlotColor = "#581845";

    var newPatientAppTypes = new List<int>() { 12,13,14,15,16,17,18,19,20,21,22 };
    var appTypes = new List<Cerebrum.ViewModels.Schedule.VMAppointmentType>();
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 1, Name = "New Patient", Color = ptNewColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 2, Name = "New Triage Patient", Color = ptNewTriageColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 3, Name = "2 New Patients", Color = ptNew2PtColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 4, Name = "New + Regular", Color = ptNewRegularColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 5, Name = "Regular", Color = ptRegularColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 6, Name = "2 Regular", Color = ptRegular2Color });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 7, Name = "Reserved", Color = reservedSlotColor });
}
<div class="row row-bottom-padding">
    <div class="col-md-12">
        <ul class="top-legend pull-right">
            <li>
                <div style="padding-left:10px;" class="pull-left legend-label">Test Colors:&nbsp;</div>
            </li>
            @foreach (var item in Model.TestTypes)
            {
                <li class="li-color-desc">
                    <div class="pull-left legend-name">@item.Name</div>
                    <div class="pull-left legend-color-container" style="background-color:@item.Color;"></div>
                </li>
            }
        </ul>
        <ul class="top-legend pull-left">
            <li>
                <div class="pull-left legend-label">Visit Colors:&nbsp;</div>
            </li>            
            @foreach (var item in appTypes)
            {
                <li class="li-color-desc">
                    <div class="pull-left legend-name">@item.Name</div>
                    <div class="pull-left legend-color-container" style="background-color:@item.Color;"></div>
                </li>
            }
        </ul>
    </div>      
</div>

@*for the top scrolling div above the schedule*@
<div style="position:relative">
    <div class="schedule-top-scroll">
        <div class="scroll-time-col">&nbsp;</div>
        <div class="scroll-schedule">
            <div class="scroll-schedule-content">
                &nbsp;
            </div>
        </div>
    </div>
</div>

<div class="schedule-wrapper"> 
    <div class="schedule-timeslot-wrapper">
        <div class="border-right timeslot-container">            
            <div class="schedule-day-header" style="background-color:#808080; color:#f2f2f2; padding-top:5px">Time</div>  
            <div class="resources-info-wrapper">
                <div class="resource-header"></div>
            </div>           
            @foreach (var time in Model.TimeSlots)
            {
                <div class="cell-divider timeslot-cell"><span>@time.Time</span></div>
            }            
        </div>
    </div> 
    <div class="schedule-details-wrapper">     
    @{  var count = 0; }
    @foreach (var day in Model.ScheduleDays)
    {
        var dayWidth = 168; // 300: default for day column
        var resourceWidth = 28; // 50:default for rescource column
       
        <div style="display:table-cell;height:inherit;position:relative;" class="day-divider">
            @{ 
                var totalDayResources = day.Resources.Count();
                var columnWidth = (totalDayResources * resourceWidth);

                var missingSpots = totalDayResources < 6 ? (6-totalDayResources) : 0;
                var widthOfAllResources = columnWidth < dayWidth ? dayWidth+"px" : columnWidth + "px";

                var alt = count%2 ==0 ? "O" : "E";
            } 
            <div style="width:@widthOfAllResources;" class="schedule-day-header @alt"> 
                <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@String.Format("{0:dddd, MMMM d, yyyy}", day.Date)">
                    @String.Format("{0:dddd, MMMM d, yyyy}", day.Date)
                </span> <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Total Patients: @day.TotalPatients"><b>(@day.TotalPatients)</b></span>  <a href="@Url.Action("day","appointments",new { area="schedule", OfficeId=Model.Office.Id, Date=day.Date.ToShortDateString() })"><span><small data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Daysheet"> Daysheet</small></span></a>
            </div>               
            @if(totalDayResources > 0)
            {                
                <div style="width:@widthOfAllResources;">                    
                @foreach (var resource in day.Resources)
                {                    
                    string headerBgCSS = resource.IsDoctor ? "doc-header-bg" : "rs-header-bg";
                    <div class="resource-wrapper">
                        
                            <div class="resources-info-wrapper">
                                <div class="resource-header @headerBgCSS">
                                    <span data-toggle="tooltip" data-placement="bottom" title="@resource.ResourceType.Name - @resource.FullName">
                                        @(string.IsNullOrWhiteSpace(resource.FullName)?"": resource.FullName.Substring(0,3).ToUpper())
                                    </span>
                                </div>
                                @if(resource.IsDoctor)
                                {
                                    <ul class="doc-ul">
                                        <li><span data-cb-tp="tooltip" data-cb-tp-placement="right" data-cb-tp-title="Test Only">TO=@resource.TotalTO</span></li>
                                        <li><span data-cb-tp="tooltip" data-cb-tp-placement="right" data-cb-tp-title="Echo">EC=@resource.TotalEC</span></li>
                                        <li><span data-cb-tp="tooltip" data-cb-tp-placement="right" data-cb-tp-title="Stress Echo">SE=@resource.TotalSE</span></li>
                                        <li><span data-cb-tp="tooltip" data-cb-tp-placement="right" data-cb-tp-title="New Patient">NP=@resource.TotalNP</span></li>
                                        <li><span data-cb-tp="tooltip" data-cb-tp-placement="right" data-cb-tp-title="Consult">CO=@resource.TotalCO</span></li>
                                    </ul>
                                }
                            </div>                                         
                        @{
                            int index = 0;
                            var totalTimeSlots = Model.TimeSlots.Count();
                            while (index < totalTimeSlots)
                            {
                                var time = Model.TimeSlots[index];
                                var resourceTimeSlot = resource.TimeSlots.FirstOrDefault((System.Func<dynamic, bool>)(r => r.StartTime == time.Time));
                                //TODO: do a more accurate check for when the timeslot increment changes #00ff40-green, #6495ED-blue

                                var notAvailableMsg = "This time slot is not available. " +
                                                "Are you sure you want to continue booking at " + (resourceTimeSlot != null? resourceTimeSlot.StartTime:time.Time) +
                                                " on " + String.Format("{0:dddd, MMMM d, yyyy}", day.Date) +
                                                " for " + (resource.IsDoctor ? "Dr. " : "") + resource.FullName;

                                if (resourceTimeSlot != null)
                                {
                                    var slotHeight = 16; // default height of each slot 17.5
                                    var slotsOccupied = 1;// default number of slots occupied
                                    var hasResourceColor = !String.IsNullOrEmpty(resourceTimeSlot.Color) ? true : false;
                                    var divHeight = slotHeight + "px";
                                    var textColor = "#000000";
                                    var slotColor = hasResourceColor ? resourceTimeSlot.Color : "#FFFFFF";
                                    var cellBorderColor = !hasResourceColor ? "" : "";
                                    //var cellBorderColor = !hasResourceColor ? "white-border" : "white-border";
                                    if (resourceTimeSlot.SlotStatus == AwareMD.Cerebrum.Shared.Enums.SlotStatus.available)
                                    {
                                        <div data-toggle="tooltip" data-placement="bottom" title="@time.Time"                                             
                                             data-modal-url="@Url.Action("create","appointments",new { area = "schedule" })" 
                                             data-practice-id="@Model.Office.PracticeId"
                                             data-office-id="@Model.Office.Id"
                                             data-appointment-day="@day.Date.ToShortDateString()"
                                             data-timeslot-id="@resourceTimeSlot.Id"
                                             data-timeslot="@resourceTimeSlot.StartTimeAMPM"
                                             data-resource-id="@resource.Id"  
                                             data-resource-name="@resource.FullName"
                                             data-resource-type="@resource.ResourceType.Name"
                                             data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Normal"
                                             data-force-booking="1"                                           
                                             class="available-timeslot cell-divider schedule-cell-width @cellBorderColor" style="color:@textColor;height:@divHeight;background-color:@slotColor;">
                                            &nbsp;
                                        </div>

                                    }
                                    else if (resourceTimeSlot.SlotStatus == AwareMD.Cerebrum.Shared.Enums.SlotStatus.reserved && !resourceTimeSlot.Appointments.Any())
                                    {
                                        var reservedMsg = "This time slot is reserved. " +
                                                "Are you sure you want to continue booking at " + (resourceTimeSlot != null ? resourceTimeSlot.StartTime : time.Time) +
                                                " on " + String.Format("{0:dddd, MMMM d, yyyy}", day.Date) +
                                                " for " + (resource.IsDoctor ? "Dr. " : "") + resource.FullName;

                                        slotColor = reservedSlotColor;
                                        <div data-toggle="tooltip" data-placement="bottom" title="@time.Time"
                                             data-confirm-msg="@reservedMsg" 
                                             data-modal-url="@Url.Action("create","appointments",new { area = "schedule" })"
                                             data-practice-id="@Model.Office.PracticeId"
                                             data-office-id="@Model.Office.Id"
                                             data-appointment-day="@day.Date.ToShortDateString()"
                                             data-timeslot-id="@resourceTimeSlot.Id"
                                             data-timeslot="@resourceTimeSlot.StartTimeAMPM"
                                             data-resource-id="@resource.Id"
                                             data-resource-name="@resource.FullName"
                                             data-resource-type="@resource.ResourceType.Name"
                                             data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Reserved"
                                             data-force-booking="1"
                                             class="reserved-timeslot cell-divider schedule-cell-width @cellBorderColor" style="color:@textColor;height:@divHeight;background-color:@slotColor;">
                                            &nbsp;
                                        </div>
                                    }
                                    else if (resourceTimeSlot.SlotStatus == AwareMD.Cerebrum.Shared.Enums.SlotStatus.occupied || resourceTimeSlot.SlotStatus == AwareMD.Cerebrum.Shared.Enums.SlotStatus.overbooked)
                                    {
                                        if (resourceTimeSlot.Appointments.Any())
                                        {
                                            var longestAppointment = resourceTimeSlot.Appointments.OrderByDescending(o => o.TestDuration).First();

                                            int totalAppointments = resourceTimeSlot.Appointments.Count();
                                            int longestDuration = longestAppointment.TestDuration > 0 ? longestAppointment.TestDuration : Model.TimeSlotIncrement;
                                            var appTitle = totalAppointments > 1 ? "Appointments" : "Appointment";
                                            var appTotal = totalAppointments > 1 ? totalAppointments + "" : "";
                                            var testOnlys = resourceTimeSlot.Appointments.Where((System.Func<dynamic, bool>)(a => a.AppointmentTypeItem.AppointmentTypeId == 1)).ToList();
                                            var overBookingmsg = "This time slot is already booked. " +
                                                "Are you sure you want to continue booking at " + resourceTimeSlot.StartTime +
                                                " on " + String.Format("{0:dddd, MMMM d, yyyy}", day.Date) +
                                                " for " + (resource.IsDoctor ? "Dr. " : "") + resource.FullName;

                                            hasResourceColor = !String.IsNullOrEmpty(longestAppointment.Color) ? true : false;
                                            slotColor = hasResourceColor ? longestAppointment.Color : "#6495ED";
                                            cellBorderColor = "default-border-color";//"white-border";
                                            slotsOccupied = longestDuration / Model.TimeSlotIncrement;
                                            divHeight = (slotsOccupied * slotHeight) + "px";

                                            if (resource.IsDoctor)
                                            {
                                                if (totalAppointments == 1)
                                                {
                                                    var app = resourceTimeSlot.Appointments.First();
                                                    if (app.AppointmentTypeItem.AppointmentTypeId == 3) { slotColor = ptRegularColor; }
                                                    else if (app.AppointmentTypeItem.Id == 18) { slotColor = ptNewTriageColor; }
                                                    else if (app.AppointmentTypeItem.AppointmentTypeId == 2) { slotColor = ptNewColor; } // appointment type with new or consult 
                                                    //else if (newPatientAppTypes.Contains(app.AppointmentTypeItem.Id)) { slotColor = ptNewColor; }
                                                }
                                                else if (totalAppointments > 1)
                                                {
                                                    int ptFollowupTotal = resourceTimeSlot.Appointments.Where((System.Func<dynamic, bool>)(a => a.AppointmentTypeItem.AppointmentTypeId == 3)).Count();
                                                    int ptNewTotal = resourceTimeSlot.Appointments.Where((System.Func<dynamic, bool>)(a => a.AppointmentTypeItem.AppointmentTypeId == 2)).Count();//resourceTimeSlot.Appointments.Where((System.Func<dynamic, bool>)(a => newPatientAppTypes.Contains(a.AppointmentTypeItem.Id))).Count();

                                                    if (ptFollowupTotal > 1) { slotColor = ptRegular2Color; }
                                                    else if (ptNewTotal > 1) { slotColor = ptNew2PtColor; }
                                                    else if (ptFollowupTotal == 1 && ptNewTotal == 1) { slotColor = ptNewRegularColor; }
                                                }

                                            }
                                            <div class="occupied-timeslot cell-divider schedule-cell-width @cellBorderColor" 
                                                 style="color:@textColor;height:@divHeight;background-color:@slotColor;"  
                                                 data-slot-color="@slotColor"
                                                 data-confirm-msg="@overBookingmsg"                                                
                                                 data-modal-url="@Url.Action("create","appointments",new { area = "schedule" })" 
                                                 data-practice-id="@Model.Office.PracticeId"
                                                 data-office-id="@Model.Office.Id"
                                                 data-appointment-day="@day.Date.ToShortDateString()"
                                                 data-timeslot-id="@resourceTimeSlot.Id"
                                                 data-timeslot="@resourceTimeSlot.StartTimeAMPM"
                                                 data-resource-id="@resource.Id"  
                                                 data-resource-name="@resource.FullName"
                                                 data-resource-type="@resource.ResourceType.Name"
                                                 data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Overbooking"
                                                 data-force-booking="1">
                                                
                                                    <div data-resource-id="@resource.Id"
                                                         data-rescource-type-id="@resource.ResourceType.Id"
                                                         data-appointment-id=""
                                                         class="btn-popover-container">
                                                        <button type="button" class="btn btn-default btn-xs popover-btn" style="height:12px; width:28px; background-color:#c4c4c4; border:0px !important; opacity: .7; font-size: 7px !important; color: #000">
                                                            <span class="glyphicon glyphicon-user text-primary" style="font-size: 7px !important"></span> @appTotal                                               </button>
                                                        <div class="btn-popover-title">
                                                            @appTitle
                                                        </div>
                                                        <div class="btn-popover-content">
                                                            <ul class="appointment-info-menu">
                                                                @foreach (var appointment in resourceTimeSlot.Appointments)
                                                                {
                                                                    await Html.RenderPartialAsync("_appointmentInfo", (object)appointment);
                                                                }
                                                            </ul>
                                                        </div>
                                                    </div>  
                                                    @if(testOnlys.Any())
                                                    {
                                                        <span @*style="font-size:11px;"*@><small>TO</small></span>
                                                    }                                               
                                            </div>
                                        }

                                    }
                                    else
                                    {                                        
                                        <div data-toggle="tooltip" data-placement="bottom" title="@time.Time" 
                                             class="cell-divider schedule-cell-width default-cell-color notavailable-timeslot" style="height:@divHeight;" 
                                             data-confirm-msg="@notAvailableMsg"                                                
                                            data-modal-url="@Url.Action("create","appointments",new { area = "schedule" })" 
                                            data-practice-id="@Model.Office.PracticeId"
                                            data-office-id="@Model.Office.Id"
                                            data-appointment-day="@day.Date.ToShortDateString()"
                                            data-timeslot-id="@resourceTimeSlot.Id"
                                            data-timeslot="@resourceTimeSlot.StartTimeAMPM"
                                            data-resource-id="@resource.Id"  
                                            data-resource-name="@resource.FullName"
                                            data-resource-type="@resource.ResourceType.Name"
                                            data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.NotAvailable"
                                            data-force-booking="1">                                            
                                            &nbsp;
                                        </div>

                                    }

                            index = index + (int)slotsOccupied; // jump to the next index

                        }
                        else // nothing at this time slot so show default cell
                        {
                                <div data-toggle="tooltip" data-placement="bottom" title="@time.Time" class="cell-divider schedule-cell default-cell-color notavailable-timeslot"
                                    data-modal-url="@Url.Action("create","appointments",new { area = "schedule" })" 
                                    data-confirm-msg="@notAvailableMsg"
                                    data-practice-id="@Model.Office.PracticeId"
                                    data-office-id="@Model.Office.Id"
                                    data-appointment-day="@day.Date.ToShortDateString()"
                                    data-timeslot-id="0"
                                    data-timeslot="@time.TimeAMPM"
                                    data-resource-id="@resource.Id"  
                                    data-resource-name="@resource.FullName"
                                    data-resource-type="@resource.ResourceType.Name"
                                    data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.NotAvailable"
                                    data-force-booking="1">  
                                    &nbsp;
                                </div>
                                index++;
                         }

                        } // while loop ends
                     }

                    </div>
                }

                @if (missingSpots > 0)
                {
                    for (int i = 0; i < missingSpots; i++)
                    {
                        <div class="resource-wrapper">

                            <div class="resources-info-wrapper">
                                <div class="resource-header">&nbsp;</div>                               
                            </div>                         

                            @{
                                int index = 0;
                                var totalTimeSlots = Model.TimeSlots.Count();
                                while (index < totalTimeSlots)
                                {                                                                       
                                    // nothing at this time slot so show default cell                                   
                                    <div class="cell-divider schedule-cell default-cell-color">
                                        &nbsp;
                                    </div>
                                    index++;
                                } // while loop ends
                            }

                        </div>
                        }
                    }
            </div>
            }
            else{
                <div style="width:@(dayWidth+"px");" class="__abc"> @*style="width:300px;"*@
                 @for (int i = 0; i < 6; i++)
                    {
                       
                        <div class="resource-wrapper">

                            <div class="resources-info-wrapper">
                                <div class="resource-header">&nbsp;</div>                               
                            </div>

                            @{
                                int index = 0;
                                var totalTimeSlots = Model.TimeSlots.Count();
                                while (index < totalTimeSlots)
                                {
                                    var time = Model.TimeSlots[index];
                                   
                                    // nothing at this time slot so show default cell                                   
                                    <div class="cell-divider schedule-cell default-cell-color">
                                        &nbsp;
                                    </div>
                                     index++;                            

                                } // while loop ends
                            }

                        </div>
                        }
                </div>
                
                                  
            }
        </div> @* div day divider end*@
        { count++; }
    } @*foreach schedule day loop end*@
                      
    <div style="height:16px"></div>@*17.5*@
        
    </div> <!-- end schedule details wraaper-->
            
    <div class="clearfix"></div>
</div>