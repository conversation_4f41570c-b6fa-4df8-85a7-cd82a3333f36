@model Cerebrum.ViewModels.Patient.VMPatientMessageAlertMain
@using Cerebrum.ViewModels.Patient
@{
    //string strChecked = Model.PatientMessageAlertEdit.IsActive ? "checked= 'checked'" : string.Empty;
    PatientMessageAlert paModel = new PatientMessageAlert()
    {
        PatientId = Model.PatientId,
        PatientAlertId = 0,
        AlertMessage = string.Empty

    };
}
<link href="~/Areas/Schedule/Content/patient-alert.css" rel="stylesheet" />

@Html.ModalHeader("Alerts for " + Model.PatientLastName + ", " + Model.PatientFirstName)

<div class="modal-body" id="modal-body-patient-alert-new">
    @Html.HiddenFor(model => (object)model.AppointmentId)
    <div id="patient-alert-new">
        @(await Html.PartialAsync("_patientAlertAddEdit", (object)(object)paModel))
    </div>
    <div id="patientAlertList-container">
        @(await Html.PartialAsync("_patientAlertList", (object)(object)Model.PatientMessageAlerts))
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)
