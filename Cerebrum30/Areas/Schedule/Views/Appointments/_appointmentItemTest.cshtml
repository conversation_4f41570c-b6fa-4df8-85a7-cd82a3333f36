@model Cerebrum.ViewModels.Schedule.VMAppointmentItemTest

@{
    var popoverTitle = "";
    var isVP = Model.Name.ToLower() == "vp" ? true : false;
    var testUrl = isVP ? Url.Action("index", "vp", new { area = "vp", AppointmentID = Model.AppointmentId, AppointmentTestID = Model.AppointmentTestId, officeId = Model.OfficeId, date = Model.SelectedDate }) :
        Url.Action("Index", "Measurement", new { area = "Measurements", AppointmentID = Model.AppointmentId, TestID = Model.TestId, AppointmentTestID = Model.AppointmentTestId, officeId = Model.OfficeId, date = Model.SelectedDate });

    var reportUrl = isVP ? Url.Action("Letter_VP_Pdf", "PdfTest", new { area = "PdfConversions", appointmentId = Model.AppointmentId, appointmentTestId = Model.AppointmentTestId }) :
        Url.Action("Report_WS_Pdf", "PdfTest", new { area = "PdfConversions", AppointmentID = Model.AppointmentId, TestID = Model.TestId, appointmentTestId = Model.AppointmentTestId });
    //<span style="font-size:6px;"><span data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Change test status" class="glyphicon glyphicon-pencil btn-apptest-status c-pointer"></span></span>
    var appTestMenu = String.Format("<span style=\"display:inline-block; width:10px; margin-right:5px\"><span data-app-id=\"{0}\" data-apptest-id=\"{1}\" data-cb-tp=\"tooltip\" data-cb-tp-placement=\"top\" data-cb-tp-title=\"Change test status\" class=\"glyphicon glyphicon-pencil btn-apptest-status c-pointer 56\"></span></span>", Model.AppointmentId, Model.AppointmentTestId);
    switch (Model.TestStatus)
    {
        case Cerebrum.Shared.AppointmentTestStatuses.Test_Started:
            popoverTitle = "Start Test";
            //textColor = "#808080";
            break;
        case Cerebrum.Shared.AppointmentTestStatuses.ReadyToStart:
        case Cerebrum.Shared.AppointmentTestStatuses.Arrived:
        case Cerebrum.Shared.AppointmentTestStatuses.Not_Arrived:
        case Cerebrum.Shared.AppointmentTestStatuses.Appointed:
            popoverTitle = "Start Test";
            break;
        case Cerebrum.Shared.AppointmentTestStatuses.Test_Completed:
        case Cerebrum.Shared.AppointmentTestStatuses.ReadyForDoctor:
        case Cerebrum.Shared.AppointmentTestStatuses.Images_Transferred:
        case Cerebrum.Shared.AppointmentTestStatuses.TraineeReportReady:
        case Cerebrum.Shared.AppointmentTestStatuses.ReportCompleted:
            popoverTitle = "Menu";
            break;
        //default: popoverTitle = "Remove Test";
        default:
            popoverTitle = "Menu";
            break;
    };
}

@if (Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Test_Started) // test has has tarted
{
    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">
        <div><a class="anchor-white" target="_blank" href="@testUrl">@Model.Name @Html.Raw(appTestMenu)  </a></div>
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>

}
else if (Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.ReadyForDoctor ||
    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Test_Completed ||
    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Images_Transferred ||
    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.TraineeReportReady ||
    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.ReportCompleted)
{

    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">

        @if (Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Test_Completed ||
                            Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.ReadyForDoctor ||
                            Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.ReportCompleted)
        {
            var linkDesc = isVP ? "VP" : "Worksheet";
            var linkText = isVP ? "View Letter" : "View Report";
            <div class="test-popover">
                <div class="btn-popover-container">
                    <span class="popover-btn apptest-pointer">@Model.Name</span> @Html.Raw(appTestMenu)
                    <div class="btn-popover-title">
                        <span class="default-text-color">@popoverTitle</span>
                    </div>
                    <div class="btn-popover-content">
                        <ul class="ul-appstatus">
                            <li class="app-status-item">
                                <a target="_blank" href='@Url.Action("OpenRawDataClassicByPatient", "Measurement", new { area = "Measurements", appointmentId = Model.AppointmentId,    patientID = Model.PatientId })'>Legacy Data</a>
                            </li>
                            @if (!Model.IsImported)
                            {
                                <li class="app-status-item">
                                    <a target="_blank" href="@reportUrl">@linkText</a>
                                </li>
                            }
                            <li class="app-status-item">
                                <a href="@testUrl" target="_blank">View @linkDesc</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-left">
                <a class="anchor-white" target="_blank" href="@testUrl">@Model.Name @Html.Raw(appTestMenu)</a>
            </div>
        }

        @(await Html.PartialAsync("_appointmentItemTestDevice", (object)(object)Model))
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>

}
else if (isVP)
{
    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">
        <div><a class="anchor-white" target="_blank" href="@testUrl">@Model.Name @Html.Raw(appTestMenu)</a></div>
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>
}
else
{
    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">
        <div class="test-popover">
            <div class="btn-popover-container">
                <span class="popover-btn c-pointer">@Model.Name</span> @Html.Raw(appTestMenu) @*<span style="font-size:6px;"><span data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Change test status" class="glyphicon glyphicon-pencil btn-apptest-status c-pointer"></span></span>*@
                <div class="btn-popover-title">
                    <span class="default-text-color">@popoverTitle</span>
                </div>
                <div class="btn-popover-content">
                    @if (Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Arrived ||
                    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.ReadyToStart ||
                    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Not_Arrived ||
                    Model.TestStatus == Cerebrum.Shared.AppointmentTestStatuses.Appointed) // arrived or ready, now we can start the test
                    {
                        <form data-app-id="@Model.AppointmentId" data-worksheet="@testUrl" class="frm-test-start default-text-color" method="post" role="form" action="@Url.Action("starttest", "appointments", new { area = "schedule" })">
                            @Html.HiddenFor(model => (object)Model.AppointmentTestId)
                            @Html.HiddenFor(model => (object)model.AppointmentId)
                            @Html.HiddenFor(model => (object)model.TestId)
                            @Html.HiddenFor(model => (object)model.RequireDevice)
                            <div class="form-horizontal">
                                <div class="msg-div text-danger">

                                </div>
                                <div class="form-group form-group-sm">
                                    <label class="control-label col-md-3">Room</label>
                                    <div class="col-md-8">
                                        @Html.EditorFor(model => model.Room, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                                    </div>
                                </div>
                                @if (Model.RequireDevice)
                                {
                                    <div class="form-group form-group-sm">
                                        <label class="control-label col-md-3">Device</label>
                                        <div class="col-md-8">
                                            @Html.EditorFor(model => model.DeviceNumber, new { htmlAttributes = new { @class = "form-control custom-input-sm" } })
                                        </div>
                                    </div>
                                }

                                @for (int i = 0; i < Model.TestResourceIds.Count(); i++)
                                {
                                    var techCount = (i + 1);
                                    <div class="form-group form-group-sm">
                                        <label class="control-label col-md-3">Tech @techCount</label>
                                        <div class="col-md-8">
                                            @Html.DropDownListFor(model => Model.TestResourceIds[i], new SelectList(Model.TestResources, "UserId", "UserFullName", Model.TestResourceIds[i]), new { @class = "form-control" })
                                        </div>
                                    </div>
                                }
                                <div class="form-group form-group-sm">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary btn-xs">Start</button>
                                        <button type="button" class="btn btn-default btn-xs btn-popover-close btn-spacing">Close</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    }
                    @*else
                        {
                            <form data-app-id="@Model.AppointmentId" class="frm-test-delete default-text-color" method="post" role="form" action="@Url.Action("removetest", "appointments", new { area = "schedule" })">
                                @Html.HiddenFor(model => (object)model.AppointmentTestId)
                                @Html.HiddenFor(model => (object)model.AppointmentId)
                                <div class="msg-div text-danger hidden">

                                </div>
                                <div class="form-group form-group-sm">
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn-danger btn-xs btn-spacing">Remove</button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                    </div>
                                </div>

                            </form>

                        }*@
                </div>
            </div>
        </div>
        @(await Html.PartialAsync("_appointmentItemTestDevice", (object)(object)Model))
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>

}