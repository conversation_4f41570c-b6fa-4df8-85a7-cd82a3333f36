@model Cerebrum.ViewModels.Schedule.VMScheduleBase
@if (Model.Office != null)
{
    bool isDaySheet = Model.ScheduleType.Id == 3 ? true : false;
    var urlFormBase = isDaySheet ? Url.Action("day", "appointments", new { area = "schedule" }) : Url.Action("index", "appointments", new { area = "schedule" });
    var urlToday = isDaySheet ? 
        Url.Action("day", "appointments", new { area = "schedule", OfficeId = Model.Office.Id, Date = String.Format("{0:MM/dd/yyyy}", System.DateTime.Now), ScheduleTypeId = Model.ScheduleType.Id }) : 
        Url.Action("index", "appointments", new { area = "schedule", OfficeId = Model.Office.Id, Date = String.Format("{0:MM/dd/yyyy}", System.DateTime.Now), ScheduleTypeId = Model.ScheduleType.Id });
    var officeDesc = isDaySheet ? "Daysheet for " + Model.Office.Name + " Office" : "Schedule for " + Model.Office.Name + " Office";

@Html.Hidden("page-refresh", "no")
<div class="row row-top-padding">
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="1">
        <div class="form-inline">
            <form id="frm-office-date" action="@urlFormBase" method="get" class="__887034">
                @{
                    var officeId = Model.Office != null ? Model.Office.Id : 0;
                    <div class="form-group form-group-sm" @*style="width:155px"*@>
                        <label class="control-label" for="OfficeId">Office</label>
                        @Html.DropDownList("OfficeId", new SelectList(Model.Offices, "Id", "Name", officeId), new { @class = "form-control" })
                        @*@Html.DropDownList("OfficeId", new SelectList(CerebrumUser.UserOffices, "Id", "Name", officeId), new { @class = "form-control" })*@
                        @Html.ValidationMessage("OfficeId", new { @class = "text-danger" })
                    </div>
                    <div class="form-group form-group-sm">
                        @Html.Hidden("ScheduleTypeId", (object)(object)Model.ScheduleType.Id)
                        <label class="control-label" for="Date">Date</label>
                        @Html.TextBox("Date", (object)String.Format("{0:MM/dd/yyyy}", Model.SelectedDate), new { @class = "form-control date-picker input-sm" })
                    </div>
                    <div class="form-group form-group-sm">
                        <a href="@urlToday" class="btn btn-default btn-xs">Today</a>
                    </div>
                }
            </form>
        </div>
    </div>
    <script type="text/javascript">
        //$(document).ready(function(){
        //    $('#OfficeId').on('change', function(){
        //        $('#ajax-loader').show();
        //    })  
        //});
    </script>
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="2">
        <div class="btn-group btn-spacing pull-left" role="group">
            @foreach (var item in Model.ScheduleTypes)
            {
                var urlSchType = "";

                if (item.Id == 3)
                {
                    urlSchType = Url.Action("day", "appointments", new { area = "schedule", OfficeId = Model.Office.Id, ScheduleTypeId = item.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.SelectedDate) });
                }
                else
                {
                    urlSchType = Url.Action("index", "appointments", new { area = "schedule", OfficeId = Model.Office.Id, ScheduleTypeId = item.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.SelectedDate) });
                }

                if (Model.ScheduleType != null && Model.ScheduleType.Id == item.Id)
                {
                    <a data-form-url="@urlFormBase" class="btn btn-primary btn-sm office-schedule"
                       href="@urlSchType">
                        @item.Name
                    </a>
                }
                else
                {
                    <a data-form-url="@urlFormBase" class="btn btn-default btn-sm office-schedule"
                       href="@urlSchType">
                        @item.Name
                    </a>
                }
            }
        </div>

        @if (Model.Office.HasSchedule)
        {
            <script type="text/javascript">
                $(function () {
                    loadAppScheduleModal(@Model.Office.PracticeId,@Model.Office.Id);
                    @*var appScheduleModal = '<div tabindex="-1" class="modal" id="appScheduleModal" data-modal-url="@Url.Action("GetCreateView", "appointments", new { area = "schedule" })"' +
                    'data-practice-id="@Model.Office.PracticeId"' +
                    'data-office-id="@Model.Office.Id">' +
                    '<div class="modal-dialog modal-lg">' +
                    '<div class="modal-content">' +
                    '<div id="appScheduleModal-ct"></div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                    addModalHtml(appScheduleModal);
                    setTimeout(function () { loadScheduleCreateView(); }, 0);*@
                });
            </script>
            <div class="pull-left">
                <a data-modal-url="@Url.Action("create", "appointments", new { area = "schedule" })"
                   data-practice-id="@Model.Office.PracticeId"
                   data-office-id="@Model.Office.Id"
                   data-appointment-day="@Model.SelectedDate.ToShortDateString()"
                   data-schedule-type="@Model.ScheduleType.Id"
                   @*data-tests="4"
                   data-patient-record-id="1"
                   data-patient-name="Test name"
                   data-requisition-id="22"
                   data-resource-type="test"
                   data-resource-id="1" 
                   data-resource-name="Test Resource"*@
                   data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Normal"
                   class="btn-sch-new-appointment btn btn-sm btn-default btn-spacing" href="#">
                    <span class="glyphicon glyphicon-calendar text-primary"></span> New Appointment
                </a>
            </div>

            @*<div tabindex="-1" class="modal" id="appScheduleModalCreate" data-modal-url="@Url.Action("GetCreateView", "appointments", new { area = "schedule" })"
                 data-practice-id="@Model.Office.PracticeId"
                 data-office-id="@Model.Office.Id">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div id="appScheduleModalCreate-ct"></div>
                        </div>
                    </div>
                </div>*@
        }
        else
        {
            <div class="pull-left text-danger">
                Please contact Admin for Office Schedule
            </div>
        }      
    </div>


        <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="3">
            <div id="_placeHolder9"></div>
        </div>
</div>

if (isDaySheet)
{
    <div class="row">
        <div style="display:none; background-color:#ff8800;height: 20px;" class="col-xs-12 col-sm-12 col-md-12 col-lg-12" id="scheduleNotes" data-office-id="@Model.Office.Id" data-date="@String.Format("{0:MM/dd/yyyy}", Model.SelectedDate)" data-url="@Url.Action("GetScheduleNotes","appointments", new { area="schedule" })">&nbsp;</div>
    </div>
<script type="text/javascript">
    $(function () {
        setTimeout(function () { getScheduleNotes(); }, 0);           
    });
</script>
}

@*<hr style="margin-top:15px;margin-bottom:3px;" />*@
@*<div class="row nopadding">
    <div class="col-md-4"></div>
    <div class="col-md-8"><h3 class="nopadding">@officeDesc</h3></div>
</div>*@
}

