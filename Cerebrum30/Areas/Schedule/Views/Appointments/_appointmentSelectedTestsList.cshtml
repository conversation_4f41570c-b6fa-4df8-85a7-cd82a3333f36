@model IEnumerable<Cerebrum.ViewModels.Schedule.VMAppointmentTest>

@{
    var practiceTests = ViewBag.PracticeTests as List<Cerebrum.ViewModels.Schedule.VMAppointmentTest>;
    var totalSelectedTests = 0;
    var selectedTests = Model.Cast<dynamic>().Where((System.Func<dynamic, bool>)(w=>w.Selected==true)).ToList();
}

@{ await Html.RenderPartialAsync("_appointmentTestButtonList", (object)practiceTests);} 

<div class="panel panel-default content-height300">
    <div id="div-total-selected-test" data-total-selected-test="@totalSelectedTests" data-test-row-count="@totalSelectedTests" class="panel-heading">Total (@totalSelectedTests)</div>
    <table id="tbl-selected-app-tests" data-selected-app-tests="@Json.Serialize(selectedTests)" class="table table-condensed table-bordered">
        <thead>
            <tr>
                <th>
                    <label>Test</label>
                </th>
                <th>
                    <label>Resources</label>
                </th>
                <th style="width:10%"></th>
            </tr>
        </thead>
       
    </table>
</div>


<script type="text/javascript">
    $(function () {
        loadSelectedTestResources();
    });
</script>