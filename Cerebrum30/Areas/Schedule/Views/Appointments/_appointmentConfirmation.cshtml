@model Cerebrum.ViewModels.Schedule.VMAppointment

@{
    var selectedTests = Model.Tests.Cast<dynamic>().Where((System.Func<dynamic, bool>)(t => t.Selected)).ToList();
    var appointmentStatus = Model.IsCancel ? "Cancelled" : "Booked";
}

@Html.ModalHeader("Appointment Confirmation")

<div class="modal-body">
    <div id="app-confirmation-print-container">
        <!-- ********************* -->
        <style>
            #app-confirmation-print-container table {
                border: unset !important;
            }

                #app-confirmation-print-container table tr:nth-child(odd), #app-confirmation-print-container table tr:nth-child(even) {
                    background: unset !important;
                }

                #app-confirmation-print-container table > tr {
                    margin-bottom: 30px !important;
                }

            .tbl-print .col1 {
                width: 150px;
            }

            .tbl-print td {
                height: 30px;
            }

            .padding-30 {
                padding-bottom: 40px;
            }

            @@media print {
                #tbl1 {
                    font-family: 'arial';
                    font-size: 12px;
                }


                #tbl-print ul {
                    list-style-type: none;
                    margin: 0;
                    padding: 0;
                }
            }
        </style>
        <table style="width: 100%;" class="tbl-print" id="tbl1">
            @{
                var officeLogo = "";
                if (Model.OfficeItem.LogoImage != null)
                {
                    var base64Logo = Model.OfficeItem.LogoImage;
                    officeLogo = String.Format("data:image/gif;base64,{0}", base64Logo);
                }

                if (!String.IsNullOrWhiteSpace(officeLogo))
                {
                    <tr>
                        <td colspan="2"><img id="img-office-logo" src="@officeLogo" width="300" height="100" /></td>
                    </tr>

                    <tr>
                        <td colspan="2"><hr></td>
                    </tr>
                }
            }
            <tr>
                <td class="col1">@Html.LabelFor(model => model.AppointmentStatus)</td>
                <td class="col2">@Html.Label(appointmentStatus)</td>
            </tr>
            <tr>
                <td class="col1">@Html.LabelFor(model => model.Office)</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.Office)</td>
            </tr>
            <tr>
                <td class="col2">@Html.LabelFor(model => model.OfficeItem.FullAddress)</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.OfficeItem.FullAddress)</td>
            </tr>
            <tr>
                <td class="col1">@Html.LabelFor(model => model.OfficeItem.Phone)</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.OfficeItem.Phone)</td>
            </tr>
            <tr>
                <td class="col1">@Html.LabelFor(model => model.OfficeItem.Fax)</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.OfficeItem.Fax)</td>
            </tr>
            <tr>
                <td class="col1">@Html.Label("Patient")</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.PatientFullName)</td>
            </tr>
            <tr>
                <td class="col1">@Html.Label("HIN")</td>
                <td class="col2">@Html.DisplayFor(model => (object)model.HealthCard)</td>
            </tr>
            <tr>
                <td class="col1">@Html.LabelFor(model => model.AppointmentDate)</td>
                <td>@Html.Raw(Model.AppointmentDate.ToString("MMMM dd, yyyy"))</td>
            </tr>
            <tr>
                <td class="col1">@Html.LabelFor(model => model.AppointmentTime)</td>
                <td>@Html.DisplayFor(model => (object)model.AppointmentTime)</td>
            </tr>
            <tr>
                <td class="col1">@Html.Label("Appointment Type")</td>
                <td>@Html.DisplayFor(model => (object)model.AppointmentParentTypeName)</td>
            </tr>


            @if (selectedTests.Any())
            {
                <tr style="vertical-align: top;">
                    <td class="col1">@Html.LabelFor(model => model.Tests)</td>
                    <td class="padding-30">
                        @foreach (var test in selectedTests)
                        {
                            <div style="clear:both">
                                <div style="min-width:200px; float: left" class="pull-left">
                                    <strong>@Html.DisplayTextFor(model => test.TestFullName)</strong>
                                </div>
                                <div class="pull-left" style="float: left">
                                    &nbsp;@Html.DisplayTextFor(model => test.TestStartTime) - @Html.DisplayTextFor(model => test.TestEndTime)
                                </div>
                                <div style="float: left" class="pull-left italic">
                                    &nbsp;@Html.DisplayNameFor(model => test.TestDuration): @Html.DisplayTextFor(model => test.TestDuration)
                                </div>
                            </div>
                        }


                    </td>
                </tr>
            }
        </table>

    </div><!--End print container-->
</div>

<div class="modal-footer">

    @if (!Model.IsCancel)
    {
        var bcColor = (Model.BookingConfirmation && Model.AppointmentStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentStatus.Booked) ? "btn-secondary btn-lg active" : "btn-info";

        @Html.ActionLink("Send Booking Confirmation ", "BookingConfirmationPdf", "../Admin/BookingConfirmationMessage", new { appointmentId = Model.Id }, new { @class = "btn btn-sm " + @bcColor, target = "_blank" })

    }
    <button type="button" class="btn btn-default btn-sm btn-print-div" onclick="printAppointmentConfirmation('app-confirmation-print-container');"><span class="glyphicon glyphicon-print default-text-color"></span> Print </button>

    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>

</div>




<script type="text/javascript">

    function printAppointmentConfirmation(divId) {

        var prop1 = {};
        prop1['font-family'] = 'arial';
        prop1['font-size'] = '12px';

        var content = document.getElementById(divId).innerHTML;

        var htmlStr;
        htmlStr = '<html><head><title>Print</title></head><body >' + content + '</body></html>';

        var mywindow = window.open('', 'print-preview', 'height=600,width=800');
        mywindow.document.write(htmlStr);
        setTimeout(function () {
            mywindow.document.close();
            mywindow.focus()
            mywindow.print();
            mywindow.close();
        }, 200);
        return true;
    }

</script>


