@model Cerebrum.ViewModels.Schedule.VMLooseReportUpload


@using (Html.BeginForm("UploadLooseReport", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { enctype = "multipart/form-data", @id = "frm-loose-report-upload" }))
{
    var allowedTypes = string.Join(",",((List<string>)ViewBag.AllowedFileTypes));
    @Html.ModalHeader("Upload Loose Report for " + Model.PatientFullName)
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))       
        <div class="form-horizontal">
            @Html.AntiForgeryToken()            
            @Html.HiddenFor(model => (object)model.PatientId)
            @Html.HiddenFor(model => (object)model.PatientFullName)
            
            <div class="form-group form-group-sm">
                @Html.Label("Allowed types", "", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <p class="form-control-static">@allowedTypes</p>
                </div>
            </div> 
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FileDescription, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-4">
                   @Html.EditorFor(model => model.FileDescription, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FileDescription, "", new { @class = "text-danger" })
                </div>
            </div> 
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.File, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-4">
                    <input type="file" id="File" name="File" style="width:280px;" />
                    @Html.ValidationMessageFor(model => model.File, "", new { @class = "text-danger" })
                </div>                
            </div> 

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.Offices, "Id", "Name", Model.OfficeId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId),"Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.CategoryId, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.CategoryId, new SelectList(ViewBag.ReportCategories, "Value", "Text", Model.CategoryId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CategoryId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReportClassId, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.ReportClassId, new SelectList(ViewBag.ReportClasses, "Value", "Text", Model.ReportClassId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ReportClassId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReportSubClassId, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.ReportSubClassId, new SelectList(ViewBag.ReportSubClasses, "Value", "Text", Model.ReportSubClassId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ReportSubClassId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReportTestDate, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-2">
                    @Html.EditorFor(model => model.ReportTestDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.ReportTestDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DateReceived, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-2">
                    @Html.EditorFor(model => model.DateReceived, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.DateReceived, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DateCreated, htmlAttributes: new { @class = "control-label col-md-2 required-label" }, new {})
                <div class="col-md-2">
                    @Html.EditorFor(model => model.DateCreated, new { htmlAttributes = new { @class = "form-control date-picker" } })
                    @Html.ValidationMessageFor(model => model.DateCreated, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>
                <div class="col-md-2">
                    <div class="checkbox">
                        <label>
                            <input name="MarkAsSeen" id="MarkAsSeen" type="checkbox" @if (Model.MarkAsSeen) { @Html.Raw("checked")} /> <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsSeen)</span>
                            @*@Html.CheckBox("MarkAsSeen") <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsSeen)</span>*@
                            @*@Html.EditorFor(model => (object)model.MarkAsSeen) <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsSeen)</span>*@
                        </label>
                        @Html.ValidationMessageFor(model => model.MarkAsSeen, "", new { @class = "text-danger" })
                    </div>                                     

                </div>

                <div class="col-md-2">
                </div>
                <div class="col-md-2">
                    <div class="checkbox">
                        <label>
                            <input name="Abnormal" id="Abnormal" type="checkbox" @if (Model.Abnormal != null && (bool)Model.Abnormal) { @Html.Raw("checked") } /> <span class="checkbox-text">@Html.DisplayNameFor(model => model.Abnormal)</span>
                            @*@Html.CheckBox("MarkAsSeen") <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsSeen)</span>*@
                            @*@Html.EditorFor(model => (object)model.MarkAsSeen) <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsSeen)</span>*@
                        </label>
                        @Html.ValidationMessageFor(model => model.Abnormal, "", new { @class = "text-danger" })
                    </div>

                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.SourceAuthor, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-4">
                    @Html.EditorFor(model => model.SourceAuthor, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.SourceAuthor, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor(model => model.SourceFacility, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-4">
                    @Html.EditorFor(model => model.SourceFacility, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.SourceFacility, "", new { @class = "text-danger" })
                </div>
            </div>

            @*<a class="btn btn-primary btn-xs" role="button" data-toggle="collapse" href="#collapseExtra">
                View more fields
            </a>           
            <div class="collapse" id="collapseExtra">
                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.DateCreated, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.DateCreated, new { htmlAttributes = new { @class = "form-control date-picker" } })
                        @Html.ValidationMessageFor(model => model.DateCreated, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.SourceAuthor, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.SourceAuthor, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.SourceAuthor, "", new { @class = "text-danger" })
                    </div>

                    @Html.LabelFor(model => model.SourceFacility, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.SourceFacility, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.SourceFacility, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>*@

            @*<div class="form-group form-group-sm">
                @Html.Label("Selected Files", "", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="selected-files">
                        <ul style="list-style:none;padding:0px;margin:0px;font-size:11px;">
                            @foreach(var file in Model.FilesToUpload)
                            {
                                <li>@file.FileName <span> size: @file.ContentLength</span></li>
                            }
                        </ul>
                    </div>      
                </div>               
            </div>*@ 
                 
        </div>
    </div>
    @Html.ModalFooter("Upload File", "blue")
}




    




