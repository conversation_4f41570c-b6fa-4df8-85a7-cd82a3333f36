@model IEnumerable<Cerebrum.ViewModels.Schedule.VMAppointmentBookedInfo>



@{ 
    int totalAppointments = Model != null ? Model.Cast<dynamic>().FirstOrDefault().NumRecs : 0;
    int count = 1;
}
@Html.ModalHeader("Appointments")
<div class="modal-body content-height500">
    <div class="panel panel-info content-height300">
        <div class="panel-heading">
            <h3 class="panel-title">Please disable test from these appointment and try again <span class="badge cbadge">Top @Model.Count() of @totalAppointments</span></h3>
        </div>
        <table class="table">
            <tr>
                <th>#</th>
                <th>
                    @Html.DisplayNameFor(model => model.AppointmentDateTime)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.PatientFullName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.PracticeDoctorName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.OfficeName)
                </th>
            </tr>

            @foreach (var item in Model.Cast<dynamic>().OrderBy((System.Func<dynamic, object>)(o=>o.AppointmentDateTime))) {
                <tr>
                    <td>@(count++)</td>
                    <td>
                        @item.AppointmentDateTime
                    </td>
                    <td>
                        @Html.ActionLink((string)item.PatientFullName,"day",new {area="Schedule",controller="appointments",OfficeId=item.OfficeId, ScheduleTypeId=3 ,Date=item.AppointmentDateTime},new  {target="_blank" })
                        
                    </td>
                    <td>
                        @item.PracticeDoctorName
                    </td>
                    <td>
                        @item.OfficeName
                    </td>
                    
                </tr>
    }

        </table>
    </div>
</div>

@Html.ModalFooter(isInfoModal:true)