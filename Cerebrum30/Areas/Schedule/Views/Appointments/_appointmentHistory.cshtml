@model Cerebrum.ViewModels.Schedule.VMAppointmentHistory


@Html.ModalHeader("Appointment History for ", Model.PatientFullName)

<div class="modal-body">
    <div class="row">

        <div class="col-md-3">
            <span>Doctor: </span><span>@Model.PracticeDoctor</span>
        </div>
        <div class="col-md-3">
            <span>Appointment Date: </span><span>@Model.AppointmentTime</span>
        </div>
        <div class="col-md-3">
            <span>Appointment By: </span><span>@Model.CreatedBy</span>
        </div>
        <div class="col-md-3">
            <span>Created At: </span><span>@Model.createdDateTime</span>
        </div>
    </div>
    <div id="appointment-changes">
        @(await Html.PartialAsync("_appHistoryItemList", (object)(object)Model.AppointmentChanges))
    </div>
    <div id="appointment-changes">
        @(await Html.PartialAsync("_patientTriage", (object)(object)Model.WaitList))
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)
