@model Cerebrum.ViewModels.Schedule.VMDaySheet
@{    
    ViewBag.Title = "Daysheet";
    Layout = "~/Areas/Schedule/Views/Shared/_LayoutSchedule.cshtml";
}

@if (CerebrumUser.UserOffices != null && CerebrumUser.UserOffices.Any())
{
    await Html.RenderPartialAsync("_scheduleBar", (object)Model);
    @*<div class="__889874">
        &nbsp;
    </div>*@
    <div id="schedule-container" class="__000987" > @*style="height:129px"*@
        @if (Model.Office != null)
        {
            await Html.RenderPartialAsync("_daySheet", (object)Model);
        }
        else
        {
            await Html.RenderPartialAsync("_noOffice");
        }   

    </div> @*schedule container end*@
}
else
{
    await Html.RenderPartialAsync("_noUserOffice");
}
