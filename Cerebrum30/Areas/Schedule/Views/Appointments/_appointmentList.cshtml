@model IEnumerable<Cerebrum.ViewModels.Schedule.VMAppointmentItem>
@*<div><strong>Total Appointments (@Model.Count())</strong></div>*@

@{ 
    var isFamilyOffice = ViewBag.IsFamilyOffice != null ? (bool)ViewBag.IsFamilyOffice : false;
}

@{ await Html.RenderPartialAsync("_daysheetPaging"); }
<table id="table-daysheet" class="table">
    <thead>
        <tr id="table-daysheet-header">
            <th colspan="2">Appointment</th>            
            <th class="th-width-26">Patient</th>
            <th>Status</th>
            <th>VP</th>
            <th>Tests</th>  
            @if (!isFamilyOffice)
            {
                <th>Family Doctor</th>            
                <th>Referral Doctor</th>  
            }
            <th>Billing</th>
            <th>    </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model){
            await Html.RenderPartialAsync("_appointmentItem", (object)item);
        }
    </tbody>
</table>
@{ await Html.RenderPartialAsync("_daysheetPaging"); }
<div style="margin-bottom:106px;"></div>