@model Cerebrum.ViewModels.Schedule.VMAppointment

@{
    var hiddenClass = Model.IsCancel ? "" : "hidden";
    bool showAppointmentPriority = CerebrumUser.IsAppointmentPriorityEnabled && Convert.ToBoolean(ViewBag.IsPriorityExists) && CerebrumUser.IsPracticeAppointmentPriorityEnabled;
}
@using (Html.BeginForm("edit", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-appointment-edit", @class = "sch-app-form" }))
{
    var ul = "";
    var phoneNumbers = new List<string>();

    if (!String.IsNullOrWhiteSpace(Model.PhoneNumber))
    {
        phoneNumbers = Model.PhoneNumber.Split(',').ToList();

        string listItems = "";
        foreach (var item in phoneNumbers)
        {
            listItems += "<li>" + item + "</li>";
        }

        ul = "<ul>" + listItems + "</ul>";
    }

    <div class="modal-header">
        <div class="row">
            <div class="col-md-7"><h4 class="modal-title"><span>Edit Appointment for @Model.PatientFullName @Html.Raw(string.IsNullOrWhiteSpace(Model.PatientPreferredName) ? "" : "[" + Model.PatientPreferredName + "]")</span></h4></div>
            <div class="col-md-5">
                <div class="pull-left">

                    @if (!String.IsNullOrWhiteSpace(Model.DateOfBirth))
                    {
                        <span style="padding-right:5px;font-size:12px;"><strong>DOB:</strong> @Model.DateOfBirth</span>
                    }

                    @if (!String.IsNullOrWhiteSpace(Model.HealthCard))
                    {
                        <span style="padding-right: 5px; font-size: 12px;"><strong>HealthCard:</strong> @Model.HealthCard</span>
                    }

                    @if (!String.IsNullOrWhiteSpace(ul))
                    {
                        <span style="color: #428bca; font-size: 12px;" class="c-pointer" data-html="true" data-toggle="tooltip" data-placement="bottom" title="@ul">Phone numbers</span>
                    }
                </div>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
        </div>
    </div>

    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)
        @Html.HiddenFor(model => (object)model.FhirId)
        @Html.HiddenFor(model => (object)model.PracticeId)
        @Html.HiddenFor(model => (object)model.ResourceId)
        @Html.HiddenFor(model => (object)model.ResourceName)
        @Html.HiddenFor(model => (object)model.PatientId)
        @Html.HiddenFor(model => (object)model.PatientFhirId)
        @Html.HiddenFor(model => (object)model.PatientFullName)
        @Html.HiddenFor(model => (object)model.ReferralDoctorId)
        @Html.HiddenFor(model => (object)model.DisplayOptions)
        @Html.HiddenFor(model => (object)model.RequisitionId)
        @Html.HiddenFor(model => (object)model.AppointmentStatus)
        @Html.HiddenFor(model => (object)model.TriageToBook)
        @Html.HiddenFor(model => (object)model.IsImported)
        @Html.HiddenFor(model => (object)model.DateOfBirth)
        @Html.HiddenFor(model => (object)model.HealthCard)
        @Html.HiddenFor(model => (object)model.PhoneNumber)
        @Html.HiddenFor(model => (object)model.AppointmentParentTypeName)
        @Html.HiddenFor(model => (object)model.CancellationReasonDescription)
        @Html.HiddenFor(model => (object)model.RecordType)
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Office, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.PracticeOffices, "Id", "Name", Model.OfficeId), new { @class = "form-control form-office-drop-down" })
                    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentDate, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.AppointmentDate, new { htmlAttributes = new { @class = "form-control date-picker", @readonly = "readonly" } })
                    @Html.ValidationMessageFor(model => model.AppointmentDate, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.WeekDayOnly) <span class="checkbox-text">@Html.DisplayNameFor(model => model.WeekDayOnly)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.WeekDayOnly, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentTime, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.AppointmentTime, new { htmlAttributes = new { @class = "form-control time-autocomplete" } })
                    <p class="help-block">Select time from the list</p>
                    @Html.ValidationMessageFor(model => model.AppointmentTime, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.ForceBooking) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ForceBooking)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.ForceBooking, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ReferralDoctor, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.ReferralDoctor, new { htmlAttributes = new { @class = "form-control" } })
                    <p class="help-block">Select referral doctor from the list</p>
                    @Html.ValidationMessageFor(model => model.ReferralDoctor, "", new { @class = "text-danger" })
                </div>
            </div>
            @if (showAppointmentPriority)
            {
                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.AppointmentPriorityId, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.AppointmentPriorityId, new SelectList(ViewBag.AppointmentPriorityItems, "Id", "PriorityName", Model.AppointmentPriorityId), "", new { @class = "form-control" })
                    </div>
                </div>
            }
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentTypeId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.AppointmentTypeId, new SelectList(ViewBag.AppointmentTypeItems, "Id", "Name", "AppointmentType", Model.AppointmentTypeId), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AppointmentTypeId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm" config-cat="integrations_ocean-e-referral" style="display:none;">
                @Html.LabelFor(model => model.ServiceRequestId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <wc-service-request-select 
                    style-class="form-control c3-select"
                    id="wc-service-request-select"
                    input-id="ServiceRequestId" 
                    input-name="ServiceRequestId" 
                    patient-id="@Model.PatientFhirId"
                    appointment-id="@Model.FhirId"
                    service-request-id="@Model.ServiceRequestId"/>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Tests, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="selected-office-tests">
                        @{ await Html.RenderPartialAsync("_appointmentSelectedTestsList", (object)Model.Tests);}
                    </div>
                    <div>@Html.ValidationMessageFor(model => model.Tests, "", new { @class = "text-danger" })</div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PreConditions, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <div class="btn-group appointment-preconditions" data-toggle="buttons">
                        @Html.EditorFor(model => (object)model.PreConditions)
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>
                <div class="col-md-2">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.ActionOnAbnormal) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ActionOnAbnormal)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.ActionOnAbnormal, "", new { @class = "text-danger" })
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.CancellationList) <span class="checkbox-text">@Html.DisplayNameFor(model => model.CancellationList)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.CancellationList, "", new { @class = "text-danger" })
                    </div>

                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PaymentMethodId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PaymentMethodId, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PaymentMethodId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Purpose, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Purpose, new { @class = "form-control auto-expand", maxlength = 500 })
                    @Html.ValidationMessageFor(model => model.Purpose, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control auto-expand", maxlength = 1000 })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>
                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.IsCancel) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsCancel)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.IsCancel, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="col-md-6 app-cancel-reason">
                    @Html.LabelFor(model => model.CancelReasonId, htmlAttributes: new { @class = "control-label col-md-4 required-label dsp-cancel-reason " + hiddenClass })
                    <div class="col-md-8">
                        <select id="CancelReasonId" name="CancelReasonId" class="form-control dsp-cancel-reason @hiddenClass">
                            <option value="0"> Choose One</option>
                            @{
                                foreach (var item in Model.AppointmentCancelReasons)
                                {
                                    var selected = string.Empty;
                                    if (item.Value == Model.CancelReasonId.ToString())
                                    {
                                        selected = " selected";
                                    }
                                    <option value="@item.Value" selected="@(selected == " selected")">@item.Text</option>
                                }
                            }
                        </select>
                        @*@Html.DropDownListFor(model => model.CancelReasonId, new SelectList(Model.AppointmentCancelReasons, "Value", "Text", Model.CancelReasonId), "Choose One", new { @class = "form-control dsp-cancel-reason " + hiddenClass })*@
                        @Html.ValidationMessageFor(model => model.CancelReasonId, "", new { @class = "text-danger dsp-cancel-reason optional" })
                    </div>
                </div>
                <div class="col-md-6 col-md-offset-6 app-cancel-reason dsp-cancel-reason @hiddenClass" id="divCancellationReasonNotes">
                    @Html.LabelFor(model => model.CancellationReasonNotes, htmlAttributes: new { @class = "control-label col-md-4 required-label" })
                    <div class="col-md-8">
                        @Html.TextAreaFor(model => model.CancellationReasonNotes, new { @class = "form-control cancel-reason-notes", @rows = 3, @maxlength = 100 })
                        @Html.ValidationMessageFor(model => model.CancellationReasonNotes, "", new { @class = "text-danger cancel-reason-notes", @id = "CancellationReasonNotesError" })
                    </div>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.WaitingListId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.WaitingListId, new SelectList(ViewBag.WaitingListStatuses, "Id", "Description", Model.WaitingListId), "", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.WaitingListId, "", new { @class = "text-danger" })
                </div>
            </div>

        </div><!-- end form horizontal-->
    </div><!--Modal body end-->
    @Html.ModalFooter("Save", "blue")
}
