@model Cerebrum.ViewModels.Schedule.VMAppointmentItem
@{
    var isTestOnlyAppointment = Model.AppointmentType == "Test Only" ? true : false;
    var doubleBooked = Model.IsDoctorDoubleBook ? 1 : 0;
    var isFamilyOffice = ViewBag.IsFamilyOffice != null ? (bool)ViewBag.IsFamilyOffice : false;
    var patientAlertsDescription = Model.PatientAlerts.Cast<dynamic>().Any() ? Model.PatientAlerts.FirstOrDefault().Description : string.Empty;
    int appStatusId = (int)Model.AppointmentStatus;
    bool showAppointmentPriority = CerebrumUser.IsAppointmentPriorityEnabled && Convert.ToBoolean(ViewBag.IsPriorityExists) && CerebrumUser.IsPracticeAppointmentPriorityEnabled;
}
<td data-is-double-book="@doubleBooked" data-practice-doctor-id="@Model.AppProviderId" data-appointment-time="@Model.AppointmentTime" class="td-status @Model.AppointmentStatus.ToString()">
    <span>Appointment ID: @Model.AppointmentId </span>
    <div style="width:100%;height:100%;" data-toggle="tooltip" data-placement="right" title="@Model.AppointmentStatusDesc"></div>
    <br />
    <div class="btn-popover-container" style="clear:both; display:block">
        <span><strong>Room: </strong></span>@*<span>@Model.Room</span>*@&nbsp; <span class="glyphicon glyphicon-pencil popover-btn"></span>
        <div class="btn-popover-title">Room</div>
        <div class="btn-popover-content">
            <form data-app-id="@Model.AppointmentId" class="frm-room-edit" method="post" role="form" action="@Url.Action("editroom", "appointments", new { area = "schedule" })">
                <div class="form-horizontal">
                    <div class="form-group form-group-sm">
                        <div class="col-md-12">
                            @Html.HiddenFor(model  => (object)model.AppointmentId)
                            @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Room), new { htmlAttributes = new { @class = "form-control custom-input-sm", @autocomplete = "off", @maxlength = 10 } })
                            @Html.ValidationMessageFor(model => model.Room, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary btn-xs">Save</button>
                            <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                        </div>
                        @*<div class="col-md-6">
                                <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                            </div>*@
                    </div>
                </div>
            </form>
        </div>
        <div class="lbl_room"> @(String.IsNullOrEmpty(Model.Room) ? "__" : @Model.Room) </div>
    </div>
</td>
<td class="td-appointment">
    <div class="row ">
        <div class="col-md-12">
            <div style="display: block">
                <div class="app-time-color" style="display: inline-block; margin-right:3px"><span class="custom-bold"> @Model.AppointmentTime</span></div>
                @{string appTypeTextColor = isTestOnlyAppointment ? "text-test-only" : "text-primary"; }

                @if (isTestOnlyAppointment)
                {
                    <div class="" style="display: inline-block;"><span class="label label-test-only">@Model.AppointmentTypeItem</span></div>
                }
                else
                {
                    <div class="" style="display: inline-block;"><span class="label label-primary label-app-type">@Model.AppointmentTypeItem</span></div>
                }
            </div>

            <div style="display: block; clear: left">
                @if (String.IsNullOrWhiteSpace(Model.ArrivedTime))
                {
                    <div class="" style="display: inline-block;"><span class="custom-bold">Arrived: </span></div> <div style="display: inline-block;">@Model.ArrivedTime</div> <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Set Arrival Time" data-url="@Url.Action("settimearrived", "appointments", new { area = "schedule" })" data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-time btn-set-arrivaltime c-pointer"></span>
                }
                else
                {
                    @* Arrived Popover *@
                    <div class="btn-popover-container">
                        <div class="" style="display: inline-block;"><span class="custom-bold">Arrived: </span></div> <div class="" style="display: inline-block;">@Model.ArrivedTime</div> <span class="glyphicon glyphicon-pencil popover-btn c-pointer btn-edit-arrivaltime"></span>
                        <div class="btn-popover-title">Arrival Time</div>
                        <div class="btn-popover-content">
                            <form data-app-id="@Model.AppointmentId" class="frm-arrived-time" method="post" role="form" action="@Url.Action("settimearrived", "appointments", new { area = "schedule" })">
                                <div class="form-horizontal">
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12">
                                            <div class="msg-div text-danger"></div>
                                            @Html.HiddenFor(model  => (object)model.AppointmentId)
                                            <div><small>(Double click in text box for current time)</small></div>
                                            @Html.TextBox("ArrivedTime", Model.ArrivedTime, new { @class = "form-control custom-input-sm current-time", @autocomplete = "off" })
                                        </div>
                                    </div>
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-primary btn-xs">Save</button>
                                            <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                        </div>
                                        @*<div class="col-md-6">
                                                <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                            </div>*@
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                }
            </div>
            <div>
                @if (String.IsNullOrWhiteSpace(Model.LeftTime))
                {
                    <div class="" style="display: inline-block;"><span class="custom-bold">Left: </span></div> <div style="display: inline-block;">@Model.LeftTime</div> <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Set Left Time" data-url="@Url.Action("settimeleft", "appointments", new { area = "schedule" })" data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-time btn-set-lefttime c-pointer"></span>
                }
                else
                {
                    @* Time Left Popover *@
                    <div class="btn-popover-container">
                        <div class="" style="display: inline-block;"><span class="custom-bold">Left: </span></div> <div class="" style="display: inline-block;">@Model.LeftTime</div> <span class="glyphicon glyphicon-pencil popover-btn c-pointer btn-left1"></span>
                        <div class="btn-popover-title">Left Time</div>
                        <div class="btn-popover-content">
                            <form data-app-id="@Model.AppointmentId" class="frm-left-time" method="post" role="form" action="@Url.Action("settimeleft", "appointments", new { area = "schedule" })">
                                <div class="form-horizontal">
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12">
                                            <div class="msg-div text-danger"></div>
                                            @Html.HiddenFor(model  => (object)model.AppointmentId)
                                            <div><small>(Double click in text box for current time)</small></div>
                                            @Html.TextBox("LeftTime", Model.LeftTime, new { @class = "form-control custom-input-sm current-time", @autocomplete = "off" })

                                        </div>
                                    </div>
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-primary btn-xs">Save</button>
                                            <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="status-desc" style="display: block; clear: left">
        <div class="" style="display: inline-block;"><span class="custom-bold">Status: </span></div> <div class="app-status-desc" style="display: inline-block;">@Model.AppointmentStatusDesc</div> <div data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-pencil btn-app-status"></div>
    </div>
    @if (showAppointmentPriority)
    {
        <div class="div-inline">
            <span class="custom-bold">Priority: </span>
        </div>
        <div class="div-inline">
            @Model.PriorityName
        </div>
        <div data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-pencil btn-app-priority"></div>
    }
</td>

@*Patient column*@
<td data-app-id="@Model.AppointmentId" data-patient-record-id="@Model.PatientId" data-referral-doc-id="@Model.ReferralDoctorId" class="td-patient">
    <div>
        <div class="btn-popover-container">
            <div class="popover-btn apptest-pointer cb-text16 text-primary lbl-font-size" style="display: block">
                @Model.PatientFullName
                @Html.Raw(string.IsNullOrWhiteSpace(Model.PatientPreferrdName) ? "" : "[" + Model.PatientPreferrdName + "]")
            </div>
            <div class="btn-popover-title">
                <span class="default-text-color">e-Chart</span>
            </div>
            <div class="btn-popover-content">
                @*@{Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = Model.PatientId, AppointmentId = Model.AppointmentId }); }*@
                @{
                    var patientMenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();
                    patientMenu.PatientId = Model.PatientId;
                    patientMenu.AppointmentId = Model.AppointmentId;
                    patientMenu.AppointmentReferralDoctorId = Model.ReferralDoctorId;
                    patientMenu.Practiceid = Model.PracticeId;
                    patientMenu.PatientHasAlert = Model.PatientAlerts.Cast<dynamic>().Any();
                    patientMenu.OfficeId = Model.OfficeId;
                    patientMenu.PatientFhirId = Model.PatientFhirId;
                }
                @{ await Html.RenderPartialAsync("_PatientMenu", (object)patientMenu); }
            </div>
        </div>
    </div>
    <div id="<EMAIL>" class="div-pat-info-holder">
        @{ var commentsColor = Model.HasDoctorComments ? "text-danger" : "text-primary"; }
        <span data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("getcomments", "appointments", new { area = "schedule", patientId = Model.PatientId, appointmentId = Model.AppointmentId })"
              data-cb-tp="tooltip" data-cb-tp-placement="top" data-cb-tp-title="Comments"
              class="glyphicon glyphicon-comment btn-view-comments c-pointer @commentsColor"></span>&nbsp;
        <span data-modal-url="@Url.Action("getappointmenthistory", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })"
              data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Appointment History"
              class="glyphicon glyphicon-info-sign text-primary btn-app-history c-pointer"></span>&nbsp;

        @if (Model.IsVirtualVisitEnable)
        {
            if (Model.VirtualVisitRoomSettings == null)
            {
                string roomName = Model.AppointmentId + "_" + Model.AppProvider + "_" + Model.PatientFullName;
                <span class="popover-btn c-pointer glyphicon glyphicon-phone btn-create-virtual-visit"
                      data-app-id="@Model.AppointmentId"
                      data-app-start="@Model.AppointmentDate.ToString("yyyy-MM-dd hh:mm:ss tt")"
                      data-app-room-name="@roomName"
                      data-app-patient-name="@Model.PatientFullName"
                      data-app-patient-email="@Model.Email"
                      data-app-patient-phone="@Model.PatientCellNumbers"
                      data-app-consent-email="@Model.ConsentEmail">
                </span>@:&nbsp;&nbsp;

            }
            else
            {
                <span class="popover-btn c-pointer glyphicon glyphicon-phone text-danger btn-view-virtual-visit"
                      data-app-id="@Model.AppointmentId" data-app-consent-email="@Model.ConsentEmail" data-app-status-id="@appStatusId"></span>@:&nbsp;&nbsp;
            }
        }

        <div class="btn-popover-container">
            @{
                var appNotesColor = String.IsNullOrWhiteSpace(Model.Notes) ? "text-primary" : "text-danger";
            }
            <span class="popover-btn c-pointer glyphicon glyphicon-tag @appNotesColor"></span>
            <div class="btn-popover-title">
                <span class="default-text-color">Appointment Notes</span>
            </div>
            <div class="btn-popover-content">
                <form class="frm-appointment-notes-edit" method="post" role="form" action="@Url.Action("editnotes", "appointments" , new { area="schedule" })">
                    <div class="form-horizontal">
                        <div class="form-group form-group-sm">
                            <div class="col-md-12">
                                @Html.HiddenFor(model  => (object)model.AppointmentId)
                                @Html.TextAreaFor(model => model.Notes, new { @class = "_etb", @rows = 3, @cols = 6 })
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary btn-xs">Save</button>
                                <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                            </div>
                            @*<div class="col-md-6">
                                    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                </div>*@
                        </div>
                    </div>
                </form>
            </div>
            @if (Model.PatientAlerts.Cast<dynamic>().Any())
            {
                <div class="btn-popover-container" id="<EMAIL>">
                    &nbsp;&nbsp;<span class="popover-btn c-pointer glyphicon glyphicon-alert text-danger"></span>
                    <div class="btn-popover-title">
                        <span class="default-text-color">Alert Message</span>
                    </div>
                    <div class="btn-popover-content">
                        <div class="form-horizontal">
                            <div class="form-group form-group-sm">
                                <div class="col-md-12">
                                    @patientAlertsDescription
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div>Dr. @Model.AppProvider</div>
        @if (!String.IsNullOrWhiteSpace(Model.Purpose))
        {
            <div style="padding-top:2px;">
                <span><strong>Purpose: </strong></span><span>@Model.Purpose</span>
            </div>
        }
        @if (Model.PatientCohorts.Cast<dynamic>().Any())
        {
            <div><span class="custom-bold">Cohorts: </span><span>@String.Join(",", Model.PatientCohorts)</span></div>
        }
        @if (Model.PatientPhoneNumbers.Cast<dynamic>().Any())
        {
            var phoneNumbers = string.Join(", ", Model.PatientPhoneNumbers.Cast<dynamic>().Select((System.Func<dynamic, object>)(phone => phone.isPrimary ? $"<span class='dark-text'>{phone.PhoneNumber} {phone.Type}</span>" : $"{phone.PhoneNumber} {phone.Type}")));
            <div>
                <span class="custom-bold">Phone Numbers: </span>
                <span>@Html.Raw(phoneNumbers)</span>
            </div>
        }
    </div>
    @if (Model.PatientActionable.Cast<dynamic>().Any())
    {
        <div class="clearfix"></div>
        <div class="ds-bottom-msg-wrapper">
            <div class="bottom-msg">
                <div class="alert-danger">
                    @*<span class="glyphicon glyphicon-exclamation-sign text-danger"> </span>*@@String.Join(",", Model.PatientActionable)
                </div>
            </div>
        </div>
    }
</td>

@*appointment confirmations sttatus *@
<td class="td-app-confirm-status">
    <span class="app-status-desc">@Model.AppConfimationDesc</span> <span data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-pencil btn-app-confirmation"></span>
</td>

@*VP column*@
<td class="td-vp">
    @{ var vpTest = Model.Tests.FirstOrDefault((System.Func<dynamic, bool>)(t => t.Name.ToLower()) == "vp");}
    @if (vpTest != null)
    {
        await Html.RenderPartialAsync("_appointmentItemTest", (object)vpTest);
        if (Model.TotalVPBooked > 0)
        {
            <div class="text-danger"><span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="VP Overbooked">OB</span></div>
        }
    }
</td>
@*Tests column*@
<td class="td-tests">
    <div>
        @foreach (var test in Model.Tests.Cast<dynamic>().Where((System.Func<dynamic, bool>)(t => t.Name.ToLower()) != "vp")) // we do not need vp here
        {
            await Html.RenderPartialAsync("_appointmentItemTest", (object)test);
        }

    </div>
    <div class="clearfix"></div>
    <div>
        @foreach (var preCon in Model.PreConditions)
        {
            var preConColor = preCon.Selected ? "btn-dayprecon-active" : "";
            <div data-app-id="@Model.AppointmentId"
                 data-url="@Url.Action("ChangePrecondition", "appointments", new { area = "schedule" })"
                 data-precon-id="@preCon.AppointmentPreconId"
                 class="c-pointer pull-left btn-dayprecon @preConColor">
                <div>@preCon.Name</div>
            </div>
        }
    </div>
    <div class="clearfix"></div>
    @if (isTestOnlyAppointment) // added according to ticket 2591
    {
        <div>
            @{ var actionableColor = Model.ActionOnAbnormal ? "btn-actionable-active" : ""; }
            <div data-app-id="@Model.AppointmentId" data-url="@Url.Action("ChangeActionOnAbnormal", "appointments", new { area = "schedule" })" class="c-pointer btn btn-actionable @actionableColor">Action on Abnormal</div>
        </div>
        <div class="clearfix"></div>
    }
    <div class="ds-bottom-msg-wrapper">
        <div class="prereq-desc">
            @*Prerequisites*@
            @if (Model.Prerequisite != null && Model.Prerequisite.Orders.Any())
            {
                var preReqOrderCount = 1;
                //var unfinishedOrders = Model.Prerequisite.Orders.Where((System.Func<dynamic, bool>)(o => o.IsIncomplete)).ToList();
                var prerequisiteColor = Model.Prerequisite.Orders.Any() ? "text-danger" : "text-success";

                <div style="margin-right:5px;" class="pull-left">
                    <div class="btn-modal-container">
                        <span class="apptest-pointer @prerequisiteColor">Prerequisites (@Model.Prerequisite.Orders.Count())</span>

                        <div class="btn-modal-content">
                            @Html.ModalHeader("Prerequisites for " + Model.PatientFullName)
                            <div data-app-id="@Model.AppointmentId" class="modal-body prereq-modal">
                                <div style="margin-right:20px;" class="pull-left"><h4>Total: @Model.Prerequisite.Orders.Count()</h4></div>
                                @*<div class="pull-left"><h4>Incomplete: @Model.Prerequisite.Orders.Where((System.Func<dynamic, bool>)(o => o.IsIncomplete)).Count()</h4></div>*@
                                <div class="clearfix"></div>
                                <div data-app-id="@Model.AppointmentId" class="">
                                    <div class="panel panel-info ds-requisition-container">
                                        <div class="panel-heading">Prerquisites Orders</div>
                                        <table class="table table-condensed table-hover table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width:5%;">#</th>
                                                    <th>Order</th>
                                                    <th style="width:10%;">Date Ordered</th>
                                                    <th style="width:20%;">Status</th>
                                                    <th style="width:20%;"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var order in Model.Prerequisite.Orders)
                                                {
                                                    var isIncomplete = order.IsIncomplete;
                                                    var itemColor = isIncomplete ? "text-danger" : "text-primary";
                                                    var rowColor = isIncomplete ? "danger" : "";
                                                    var itemName = !String.IsNullOrWhiteSpace(order.TestName) ? order.TestName : order.Name;
                                                    var timeDescription = !String.IsNullOrWhiteSpace(order.RequisitionTime) ? " - " + order.RequisitionTime : "";

                                                    <tr class="@itemColor @rowColor">
                                                        <td>@preReqOrderCount</td>
                                                        <td>@itemName @timeDescription</td>
                                                        <td>@Model.Prerequisite.AppointmenDate</td>
                                                        <td>@order.StatusDescription</td>
                                                        <td>
                                                            <button class="btn btn-primary btn-xs btn-view-requisition" data-requisition-id="@order.RequisitionId" data-requisition-type="@order.Name">View</button>
                                                            @if (order.IsInternal)
                                                            {
                                                                var disableBook = !isIncomplete ? "disabled" : "btn-sch-new-appointment";
                                                                var requisitionDate = order.RequisitionDate != null ? order.RequisitionDate.Value.ToString("MM/dd/yyyy") : "";
                                                                <button data-practice-id="@Model.PracticeId"
                                                                        data-office-id="@Model.OfficeId"
                                                                        data-patient-record-id="@Model.PatientId"
                                                                        data-patient-name="@Model.PatientFullName"
                                                                        data-tests="@order.TestId"
                                                                        data-requisition-id="@order.RequisitionId"
                                                                        data-appointment-day="@requisitionDate"
                                                                        data-resource-type="practicedoctorid"
                                                                        data-resource-id="@order.PracticeDoctorId"
                                                                        class="btn btn-primary btn-xs @disableBook">
                                                                    Book
                                                                </button>
                                                            }
                                                            else
                                                            {
                                                                <button class="btn btn-primary btn-xs btn-change-requisition-status" data-requisition-type="@order.Name" data-requisition-id="@order.RequisitionId" data-requisition-status-id="@order.StatusId" data-requisition-test-date="@order.TestTime">Change Status</button>
                                                            }
                                                        </td>
                                                    </tr>
                                                    preReqOrderCount++;
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                                <div style="margin-right:20px;"><h4>Comment:</h4></div>
                                <div style="height: 160px; overflow: auto; white-space: pre;">@Model.Prerequisite.Comment</div>
                            </div>
                            <div class="modal-footer">
                                <a href="@Url.Action("index", "requisition", new { area = "requisition", patientRecordId = Model.PatientId, practiceDoctorId = Model.AppProviderId, appointmentId = Model.Prerequisite.AppointmentId, officeId = Model.OfficeId })" class="btn btn-default btn-sm">View Previous Flow</a>
                                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                if (CerebrumUser.DSFilterShowOrders)
                {
                    <div style="margin-right:5px;" class="text-success pull-left">Prerequisites(0)</div>
                }
            }
            @*Orders*@
            @if (Model.Order != null && Model.Order.Orders.Any())
            {
                var orderCount = 1;
                var orderColor = Model.Order.Orders.Any((System.Func<dynamic, bool>)(o => o.IsIncomplete)) ? "text-danger" : "text-success";
                <div style="margin-right:5px;" class="pull-left">
                    <div class="btn-modal-container">
                        <span class="apptest-pointer @orderColor">Orders (@Model.Order.Orders.Count())</span>
                        <div class="btn-modal-content">
                            @Html.ModalHeader("Orders for " + Model.PatientFullName)
                            <div data-app-id="@Model.AppointmentId" class="modal-body req-order-modal">
                                <div style="margin-right:20px;" class="pull-left"><h4>Total: @Model.Order.Orders.Count()</h4></div>
                                <div class="pull-left"><h4>Incomplete: @Model.Order.Orders.Where((System.Func<dynamic, bool>)(o => o.IsIncomplete)).Count()</h4></div>
                                <div class="clearfix"></div>
                                <div data-app-id="@Model.AppointmentId" class="">
                                    <div class="panel panel-info ds-requisition-container">
                                        <div class="panel-heading">Orders</div>
                                        <table class="table table-condensed table-hover table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width:5%;">#</th>
                                                    <th>Order</th>
                                                    <th style="width:10%;">Date Ordered</th>
                                                    <th style="width:20%;">Status</th>
                                                    <th style="width:20%;"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var order in Model.Order.Orders)
                                                {
                                                    var isIncomplete = order.IsIncomplete;
                                                    var itemColor = isIncomplete ? "text-danger" : "text-primary";
                                                    var rowColor = isIncomplete ? "danger" : "";
                                                    var itemName = !String.IsNullOrWhiteSpace(order.TestName) ? order.TestName : order.Name;
                                                    var timeDescription = !String.IsNullOrWhiteSpace(order.RequisitionTime) ? " - " + order.RequisitionTime : "";
                                                    <tr class="@itemColor @rowColor">
                                                        <td>@orderCount</td>
                                                        <td>@itemName @timeDescription</td>
                                                        <td>@Model.Order.AppointmenDate</td>
                                                        <td>@order.StatusDescription</td>
                                                        <td>
                                                            <button class="btn btn-primary btn-xs btn-view-requisition" data-requisition-id="@order.RequisitionId" data-requisition-type="@order.Name">View</button>
                                                            @if (order.IsInternal)
                                                            {
                                                                var disableBook = !isIncomplete ? "disabled" : "btn-sch-new-appointment";
                                                                var requisitionDate = order.RequisitionDate != null ? order.RequisitionDate.Value.ToString("MM/dd/yyyy") : "";
                                                                <button data-practice-id="@Model.PracticeId"
                                                                        data-office-id="@Model.OfficeId"
                                                                        data-patient-record-id="@Model.PatientId"
                                                                        data-patient-name="@Model.PatientFullName"
                                                                        data-tests="@order.TestId"
                                                                        data-requisition-id="@order.RequisitionId"
                                                                        data-appointment-day="@requisitionDate"
                                                                        data-resource-type="practicedoctorid"
                                                                        data-resource-id="@order.PracticeDoctorId"
                                                                        data-referral-doctor-id="@Model.ReferralDoctorId"
                                                                        data-referral-doctor="@Model.ReferralDoctor"
                                                                        class="btn btn-primary btn-xs @disableBook">
                                                                    Book
                                                                </button>
                                                            }
                                                            else
                                                            {
                                                                <button class="btn btn-primary btn-xs btn-change-requisition-status" data-requisition-type="@order.Name" data-requisition-id="@order.RequisitionId" data-requisition-status-id="@order.StatusId" data-requisition-test-date="@order.TestTime">Change Status</button>
                                                            }
                                                        </td>
                                                    </tr>
                                                    orderCount++;
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                                <div style="margin-right:20px;"><h4>Comment:</h4></div>
                                <div style="height: 160px; overflow: auto; white-space: pre;">@Model.Order.Comment</div>
                            </div>
                            <div class="modal-footer">
                                <a href="@Url.Action("index", "requisition", new { area = "requisition", patientRecordId = Model.PatientId, practiceDoctorId = Model.AppProviderId, appointmentId = Model.AppointmentId, officeId = Model.OfficeId })" class="btn btn-default btn-sm">View Flow</a>
                                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                if (CerebrumUser.DSFilterShowOrders)
                {
                    <div style="margin-right:5px;" class="text-success pull-left">Orders (0)</div>
                }
            }
            @*Orders end*@

            <div class="pull-left">
                <a href="@Url.Action("index", "requisition", new { area = "requisition", patientRecordId = Model.PatientId, practiceDoctorId = Model.AppProviderId, appointmentId = Model.AppointmentId, officeId = Model.OfficeId })" class="btn-flow">Flow</a>
            </div>
            <div id="requisitionFormDialog" name="requisitionFormDialog" style="display:none;"></div>
            <div class="clearfix"></div>
            @*@if(Model.AppointmentStatus >= Cerebrum.Shared.AppointmentStatus.Arrived)*@
            @*<span><a class="set-return-url" href="@Url.Action("index", "requisition", new { area = "requisition", patientRecordId = Model.PatientId, practiceDoctorId = Model.AppProviderId, appointmentId = Model.AppointmentId })">Flow</a></span>*@
            @*<span><a href="@Url.Action("index", "requisition", new { area = "requisition", patientRecordId = Model.PatientId, practiceDoctorId = Model.AppProviderId, appointmentId = Model.AppointmentId, officeId = Model.OfficeId })">Flow</a></span>*@
        </div>
    </div>

</td>

@if (!isFamilyOffice)
{
    @*Family Doctor column *@
    <td class="td-family-doc">
        @if (!String.IsNullOrWhiteSpace(Model.FamilyDoctor))
        {
            <div class="header">
                <span>@Model.FamilyDoctor</span>
                <span data-modal-url='@Url.Action("edit", "externaldoctors", new { area = "", extDoctorId = Model.FamilyDoctorId })'
                      data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Edit Family Doctor"
                      class="glyphicon glyphicon-pencil btn-edit-ext-doctor c-pointer">
                </span>
            </div>
            if (!String.IsNullOrWhiteSpace(Model.FamilyDoctorFax))
            {
                <div>Fax: @Model.FamilyDoctorFax</div>
            }
        }
    </td>

    @*Referring Doctor column *@
    <td class="td-referral-doc">
        <div>
            @if (!String.IsNullOrWhiteSpace(Model.ReferralDoctor))
            {
                <div class="header">
                    <span>@Model.ReferralDoctor</span>
                    <span data-external-doc-id="@Model.ReferralDoctorId"
                          data-patient-name="@Model.PatientFullName"
                          data-patient-record-id="@Model.PatientId"
                          data-appointment-id="@Model.AppointmentId"
                          data-doctor-type="@AwareMD.Cerebrum.Shared.Enums.DocType.Referral"
                          data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Edit referral doctor report contact information"
                          class="glyphicon glyphicon-home btn-get-exdoc-report-contact c-pointer">
                    </span>
                    <span data-modal-url='@Url.Action("edit", "externaldoctors", new { area = "", extDoctorId = Model.ReferralDoctorId })'
                          data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Edit Referral Doctor"
                          class="glyphicon glyphicon-pencil btn-edit-ext-doctor c-pointer">
                    </span>
                </div>
                if (!String.IsNullOrWhiteSpace(Model.ReferralDoctorFaxNumber))
                {
                    <div> Fax: @Model.ReferralDoctorFaxNumber</div>
                }
                if (!String.IsNullOrWhiteSpace(Model.RefDocOHIPPhysicianId))
                {
                    <div>Billing: @Model.RefDocOHIPPhysicianId</div>
                }
            }
        </div>
        @if (Model.DoctorActionable.Cast<dynamic>().Any())
        {
            <div class="clearfix"></div>
            <div class="bottom-msg">
                <div class="alert-danger DoctorActionable">
                    @*<span class="glyphicon glyphicon-exclamation-sign text-danger"> </span>*@@String.Join(",", Model.DoctorActionable)
                </div>
            </div>
        }
    </td>
}
@*Billing column*@
<td class="td-billing">
    <div>
        @*<span><strong>Payment: </strong></span>*@
        <span>@Model.PaymentMethod</span>
        <span data-app-id="@Model.AppointmentId" class="glyphicon glyphicon-pencil btn-pay-method"></span>
    </div>
    @*@Model.Billing <span class="glyphicon glyphicon-usd text-success"> </span> <span class="text-success"><strong>Claims</strong></span>*@
    @if (!String.IsNullOrWhiteSpace(Model.ConsultCodCode))
    {
        <span>@Model.ConsultCodCode</span>
        <br />
        <span>@Model.DiagnoseCodeDiagnosis</span>
    }
    else
    {
        if (Model.AppointmentTypeId == 1)
        {
            <div class="pull-left test-container Appointed"> </div>
        }
    }
    @{ string appointmentClaimDisplay = "none";
        //if (Model.BillStatusId > 1 && (Model.PaymentMethodId == 0 || Model.PaymentMethodId == 3)) // billed && payment is OHIP/RMB
        if (Model.BillStatusId > 1) // billed
        {
            appointmentClaimDisplay = "block";
        }
    }
    <div class="clearfix"></div>
    <div style="color:@Model.BillStatusColor;"><a class="appointment-claim" style="display: @appointmentClaimDisplay;color:@Model.BillStatusColor;" data-app-id="@Model.AppointmentId" data-admission-id="" data-admission-action-id="" data-modal-url="@Url.Action("appointmentclaim", "groupbill", new { area = "bill" })" href="#"><strong>Claims</strong></a></div>
    <div style="color:@Model.BillStatusColor;"><a class="appointment-claim-error text-danger" style="display: none;" data-app-id="@Model.AppointmentId" data-error-message="" href="#"><strong>Claims Error</strong></a></div>
</td>
<td class="td-edit-appointment">
    @*<span class="appointment-edit c-pointer glyphicon glyphicon-edit" data-modal-url="@Url.Action("edit", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })"> Edit</span>*@
    @*<a href="#" class="appointment-edit c-pointer" data-modal-url="@Url.Action("edit", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })">Edit</a>*@
    <div class="btn-popover-container">
        <button type="button" class="btn btn-default btn-xs popover-btn">
            <span class="glyphicon glyphicon-option-vertical text-primary"></span>
        </button>
        <div class="btn-popover-title">Appointment Menu</div>
        <div class="btn-popover-content">
            <ul style="width:175px;" class="ul-popover-list" id="<EMAIL>">
                <li><a href="#" class="appointment-edit" data-modal-url="@Url.Action("edit", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })">Edit Appointment</a></li>
                @{ var bcColor = Model.BookingConfirmation ? "default-text-color" : "text-primary"; }
                <li><a href="#" class="btn-view-app-confirmation" data-app-id="@Model.AppointmentId"><span class="@bcColor">Booking Confirmation</span> </a></li>
                <li><a href="#" class="booking-comments" data-modal-url="@Url.Action("EditBookingComments", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })">Booking Comments</a></li>
                <li>
                    @{
                        var ccDoctorLink = new Cerebrum.ViewModels.Common.VMCCDoctorLink();
                        ccDoctorLink.PatientId = Model.PatientId;
                        ccDoctorLink.AppointmentId = Model.AppointmentId;
                        ccDoctorLink.PatientFullName = Model.PatientFullName;
                    }
                    @{ await Html.RenderPartialAsync("_CCDoctorsLink", (object)ccDoctorLink); }
                </li>
                <li><a href="#" class="appointment-refresh" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("GetDaysheetItem", "daysheet", new { area = "schedule", appointmentId = Model.AppointmentId })">Refresh</a></li>
                <li><a href="#" class="btn-print-patient-label" data-patient-record-id="@Model.PatientId" data-url="@Url.Action("PrintPatientLabel", "appointments", new { area = "schedule" })">Print Patient Label</a></li>
                <li><a href="#" class="btn-print-patient-address-label" data-patient-record-id="@Model.PatientId" data-url="@Url.Action("PrintPatientAddressLabel", "appointments", new { area = "schedule" })">Print Address Label</a></li>
                <li><a href="#" class="btn-print-demo-label" data-patient-record-id="@Model.PatientId" data-url="@Url.Action("PrintDemoLabel", "appointments", new { area = "schedule" })">Print Demo Label</a></li>
                @{ var mwlColor = Model.MWLSentFlag ? "default-text-color" : "text-primary"; }
                <li><a href="#" class="btn-mwl" data-app-id="@Model.AppointmentId" data-url="@Url.Action("MWLData", "appointments", new { area = "schedule", appointmentId = Model.AppointmentId })" data-update-url="@Url.Action("UpdateMWLFlag", "appointments", new { area = "schedule" })"><span class="@mwlColor">MWL</span></a></li>

                @{
                    var refDocumentColor = Model.TotalReferralDocuments > 0 ? "text-danger bold-text" : "";
                    var refDocumentLink = "btn-view-ref-documents";
                    //var refDocTotal = Model.TotalReferralDocuments > 0 ? "(" + Model.TotalReferralDocuments + ")" : "";
                }
                <li><a class="@refDocumentLink @refDocumentColor" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("getreferraldocuments", "uploads", new { area = "documents", appointmentId = Model.AppointmentId })" href="#">RD</a></li>
                @if (!(Model.PaymentMethodId == 0 || Model.PaymentMethodId == 3 || Model.PaymentMethodId == 2)) // not OHIP/RMB/WCB
                {
                    <li><a class="appointment-invoice" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("invoice", "groupbill", new { area = "bill" })" href="#">Bill</a></li>
                }
                else
                {
                    <li><a class="appointment-bill" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("appointmentprebill", "groupbill", new { area = "bill" })" href="#">Bill</a></li>
                }
                <li><a class="appointment-unbill" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("appointmentunbill", "groupbill", new { area = "bill" })" href="#">Unbill</a></li>
                <li><a href='@Url.Action("FormBuilderAdmin", "formbuilder", new { area = "admin", appointmentId = @Model.AppointmentId })' target="_blank" class="btn-fill-pdf">Fill PDF</a></li>
                @if (Model.RecordType == 3)
                {
                    <li><a href="#" class="appointment-additionalInfo" data-modal-url="@Url.Action("RequestAdditionalInfo", "appointments", new { area = "schedule", appointmentId = @Model.AppointmentId})">Request Additional Info</a></li>
                }
                <li><wc-appointment-service-request-link appointment-id="@Model.FhirId" load-on-observe-id="<EMAIL>"></wc-appointment-service-request-link></li>

            </ul>
        </div>
    </div>

    @if (!String.IsNullOrWhiteSpace(Model.ResidualData))
    {
        <span class="btn-popover-container">
            <span class="text-danger popover-btn c-pointer">R</span>
            <span class="btn-popover-title">
                Residual Data
            </span>
            <span class="btn-popover-content">
                <span style="width:200px;max-height:200px;overflow-y:auto;">
                    @Model.ResidualData
                </span>
            </span>

        </span>
    }

</td>
<script>
    formatName();
</script>