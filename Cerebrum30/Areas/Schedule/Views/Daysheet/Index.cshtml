@model Cerebrum.ViewModels.Schedule.VMDaySheet
@using Cerebrum.ViewModels.User
@{
    ViewBag.Title = "Daysheet";
    Layout = "~/Areas/Schedule/Views/Shared/_LayoutDaysheet.cshtml";
}
@{ await Html.RenderPartialAsync("~/Areas/VirtualVisit/Views/Shared/_createVirtualVisitInclude.cshtml", (object)Model); }

@if (CerebrumUser.UserOffices != null && CerebrumUser.UserOffices.Any())
{
    await Html.RenderPartialAsync("_daysheetBar", (object)Model);

    <div id="daysheet-main-container" class="__000987">

        @if (Model.Office != null)
        {
            await Html.RenderPartialAsync("_daySheet", (object)Model);
        }
        else
        {
            await Html.RenderPartialAsync("_noOffice");
        }

    </div>
}
else
{
    await Html.RenderPartialAsync("_noUserOffice");
}
<br />
<br />
<br />
<br />
<br />
@*<script>
        $(document).ready(function () {
            //$('[data-toggle="popover"]').popover();
            $("#btn-popover-virtual-visit").popover({ trigger: "click" });
        });
    </script>*@