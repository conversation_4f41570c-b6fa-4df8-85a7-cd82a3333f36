@model Cerebrum.ViewModels.Schedule.VMDaySheet
@{
    string isPrescribeITTokenExpired = Convert.ToString(ViewBag.IsPrescribeITTokenExpired);
    bool isShowTTokenExpired = Convert.ToBoolean(ViewBag.IsPrescribeITTokenExpired);
    string otp = (string)ViewBag.OTP;
    var filterAppStatuses = Model.AppointmentStatuses.Cast<dynamic>().Where((System.Func<dynamic, bool>)(s => (s.Text.ToLower()) != "waitlist" && s.Text.ToLower() != "cancellationlist")).ToList();
    var cReturnUrl = Html.GetReturnUrl();
    var appPriorities = Model.AppointmentPriorities.OrderBy((System.Func<dynamic, object>)(p => p.Rank)).ThenBy((System.Func<dynamic, object>)(p => p.PriorityName));
}
@(await Html.PartialAsync("_AwareServicesUiLoader"))

<script src="~/Scripts/cerebrum3-healthcard.js"></script>
<link rel="stylesheet" type="text/css" href="/Content/tailwinds.css"></link>
<input type="hidden" id="hdIsPrescribeITTokenExpired" value="@isPrescribeITTokenExpired.ToLower()" />
<wc-appointment-event-handler></wc-appointment-event-handler>
@if (isShowTTokenExpired)
{
    <div id="prescribeITTokenExpired" title="PrescribeIT login" style="visibility: hidden">
        <div class="alert alert-danger">
            <p class="alert-link">PrescribeIT login is expired. Please enter the OTP to login</p>
        </div>
        <form autocomplete="off" class="form-inline" style="margin-top:15px">
            <lable class="control-label label-bold">OTP</lable>
            <input type="text" class="form-control" value="@otp" name="OTP" id="OTP" style="margin-left:10px; margin-right:5px;" />
            <button class="btn btn-info btn-sm" id="btnPrescribeITRequestToken">
                Login
                <span style="visibility:hidden;padding-left:2px;" class="span-loading-token">
                    <img src="~/Content/Images/loading.gif" style="width:20px;" />
                </span>
            </button>
            @*&nbsp;<a href="#" onclick="Javascript: return false" data-toggle="tooltip" title="To turn off this popup please go to PrescribeIT / Configuration Preference">Help</a>*@
        </form>
        <div id="div-prescribeIT-error-message" class="col-md-12 text-danger"></div>
    </div>
}
<div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <h3 class="lbl-datestamp">@String.Format("{0:dddd, MMMM d, yyyy}", Model.SelectedDate) <small><span class="lbl-appointments highlight">@*Total Appointments (@Model.Appointments.Count())*@</span></small></h3>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div id="ph_msg" style="display: none; text-align: center;"></div>
    </div>
</div>

<div class="ds-filter-container">
    @*Filters*@
<div class="row tailwinds">
    <div class="flex flex-row justify-between gap-4 px-12">
        <div class="inline-block lbl-filter __00956">Filters</div>
        <div class="flex form-inline">
            <div id="div-user-sch-filter-wrapper" class="form-group form-group-sm pull-left"
                 data-url="@Url.Action("GetUserScheduleViewButtons", "scheduleview", new { area = "schedule" })"
                 data-office-id="@Model.Office.Id"
                 data-selected-date="@Model.SelectedDate.ToShortDateString()">
                <script type="text/javascript">
                    $(function () {
                        loadUserSchFilterButtons();
                    });
                </script>
            </div>
            <div class="form-group form-group-sm pull-left">
                @{ await Html.RenderPartialAsync("_testGroupFilters", (object)Model.TestFilters); }
            </div>
            @using (Html.BeginForm("getdaysheetappointments", "daysheet", new { area = "schedule" }, FormMethod.Get, true, new { @id = "frm-day-filters" }))
            {
                <div class="form-group form-group-sm">
                    @Html.Hidden("OfficeId", (object)(object)Model.Request.OfficeId)

                    @Html.Hidden("Date", (object)Model.SelectedDate.ToShortDateString())
                    @Html.Hidden("FilterPatient", (object)(object)Model.Request.FilterPatient)
                    @Html.Hidden("PageSize", (object)Model.Request.PageSize)

                    <label class="control-label" for="AppointmentStatusId">Status</label>
                    @Html.DropDownList("AppointmentStatusId", new SelectList(filterAppStatuses, "Id", "Description", Model.Request.AppointmentStatusId), "All", new { @class = "form-control" })
                    <div class="checkbox">
                        <label>
                            @Html.CheckBox("Expected", (object)(object)(object)Model.Request.Expected) Show expected
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.CheckBox("ShowOrders", (object)(object)Model.Request.ShowOrders) Show orders
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.CheckBox("ExcludeTestOnly", (object)(object)(object)Model.Request.ExcludeTestOnly) Exclude test only
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.CheckBox("ExcludeCancelled", (object)(object)Model.Request.ExcludeCancelled) Exclude Cancelled
                        </label>
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.CheckBox("OnlyActionOnAbnormal", (object)(object)(object)Model.Request.OnlyActionOnAbnormal) Action On Abnormal
                        </label>
                    </div>
                </div>

            }
        </div>

        <div class="inline-block form-inline filter-by-patient ">
            <div class="form-group-sm pull-right">
                <!-- pull-right | pull-left -->
                <input type="text" class="form-control" id="ds-patient-filter" name="ds-patient-filter" value="@Model.Request.FilterPatient" placeholder="Filter by patient" />
            </div>
        </div>



    </div>
</div>
</div>

<div id="daysheet-wrapper" data-get-items-url="" data-selected-date="@Model.SelectedDate.ToShortDateString()">

    @{ await Html.RenderPartialAsync("_appointmentList", (object)Model.AppointmentsMain); }

</div>

<div id="app-confirmations" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.AppointmentConfirmations)
        {
            <li class="app-status-item" data-appconfirm-desc="@item.Description" data-appconfirm-color="@item.Color" data-appconfirm-id="@item.Id">
                <div>@item.Description</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
</div>

<div id="app-statuses" class="hidden">
    <form>
        @Html.AntiForgeryToken()
    </form>
    <ul class="ul-appstatus">
        @foreach (var item in filterAppStatuses)
        {
            <li class="app-status-item" data-appstatus-desc="@item.Description" data-appstatus-color="@item.Color" data-appstatus-id="@item.Id">
                <div class="pull-left legend-color-container @item.Color"></div>
                <div>@item.Description</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
</div>

<div id="app-priorities" class="hidden">
    <form>
        @Html.AntiForgeryToken()
    </form>
    <ul class="ul-app-priority">
        <li class="app-priority-item" data-priority-id="">
            <div class="pull-left legend-color-container"></div>
            <div>None</div>
        </li>
        @foreach (var item in appPriorities)
        {
            <li class="app-priority-item" data-priority-desc="@item.PriorityName" data-priority-id="@item.Id">
                <div class="pull-left legend-color-container"></div>
                <div>@item.PriorityName</div>
            </li>
        }
    </ul>
</div>

<div id="app-test-statuses" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.TestStatuses)
        {
            <li class="app-status-item" data-appstatus-desc="@item.Status" data-appstatus-color="@item.Color" data-appstatus-id="@item.Id">
                <div class="pull-left legend-color-container @item.Color"></div>
                <div>@item.Status</div>
            </li>
        }

    </ul>
    @if (CerebrumUser.HasPermission("RemoveTests"))
    {
        <button type="button" class="btn btn-danger btn-xs app-test-remove">Remove</button>
    }
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
</div>

<div id="practice-tests" class="hidden">
    <ul class="ul-appstatus ul-group-tests">
        @{
            var pracTests = Model.PracticeTests.Cast<dynamic>().Where((System.Func<dynamic, bool>)(t => t.Name.ToLower()) != "vp").ToList();
            var totalItems = pracTests.Count();
            var groups = pracTests.GroupBy(x => x.Category).ToList();
            var groupCount = pracTests.GroupBy(x => x.Category).Count();
        }

        @for (int i = 0; i < groupCount; i++)
        {
            var group = groups[i];

            if (groupCount > 0)
            {
                <li class="li-header">@group.Key</li>
            }

            foreach (var item in group)
            {
                <li class="app-status-item"
                    data-test-id="@item.TestId"
                    data-test-category="@item.Category"
                    data-test-category-id="@item.CategoryId"
                    data-test-modalities="@item.Modalities"
                    data-test-modalities-count="@item.ModalitiesCount"
                    data-test-doctors-count="@item.DoctorsCount">
                    <div>@item.Name</div>
                </li>
            }
            @*if (groupCount > 1 && i < (groupCount - 1))
                {
                    <li class="li-divider"></li>
                }*@
        }
    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close _66765">Close</button>
</div>

<div id="app-pay-methods" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.PaymentMethods)
        {
            <li class="app-status-item" data-pay-desc="@item.Name" data-pmethod-id="@item.Id">
                <div>@item.Name</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close _9980">Close</button>
</div>

<div id="office-techs" class="hidden">
    @Html.DropDownList("lk-office-tech", new SelectList(Model.OfficeTechs, "Value", "Text"), new { @class = "form-control" })
</div>

<div class="modal fade" id="billing-test-status-modal" role="dialog">
    <div class="modal-dialog" style="width: 450px;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Select Test Status</h4>
                <br /><br />Appointment tests with selected status (and beyond) will be billed.
            </div>
            <div class="modal-body">
                @foreach (var item in Model.BillingTestStatuses)
                {
                    if (!(item.Status == "Requested" || item.Status == "Appointed" || item.Status == "Not arrived" || item.Status == "Test ready to start" || item.Status == "Being Sent"))
                    {
                        string checkedFlag = string.Empty;
                        if (item.Status != null && item.Status.Contains("started"))
                        {
                            checkedFlag = "checked";
                        }
                        <div><input type="radio" id="billingTestStatus" name="billingTestStatus" value="@item.Id" @checkedFlag>&nbsp;&nbsp;&nbsp; @item.Status</div>
                    }
                }
            </div>
            <div class="modal-footer">
                <div class="col-md-6"></div>
                <div class="col-md-6">
                    <div>
                        <button class="btn btn-default btn-sm btn-prebill-day-sheet" data-dismiss="modal" style="width: 64px;" data-bill-date="0" data-appointment-id="0" data-bill-office-id="0">Bill</button>
                        <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="billing-message-modal" role="dialog">
    <div class="modal-dialog" style="width: 900px;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Message</h4>
            </div>
            <div class="modal-body">
                <div id="billing-message-modal-header"></div>
                <div class="modal-body" id="billing-message-modal-body" style="overflow-y: scroll; height:320px;"></div>
            </div>
            <div class="modal-footer">
                <div class="col-md-8" id="billing-message-modal-footer"></div>
                <div class="col-md-4">
                    <div id="billing-message-modal-continue-buttons">
                        <button class="btn btn-default btn-sm btn-view-bill-office" data-dismiss="modal" style="width: 64px;" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id" data-appointment-id="0" data-billing-test-status-id="0">Yes</button>
                        <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">No</button>
                    </div>
                    <div id="billing-message-modal-close-button">
                        <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<div id="requisitionStatusDialog" name="requisitionStatusDialog" style="display:none; margin-top: 8px; ">
    @foreach (var requisitionStatus in Model.requisitionStatus.OrderBy((System.Func<dynamic, object>)(status => status.SortOrder)).ToList())
    {
        <div class="row" style="padding-top: 8px;">
            <div class="col-sm-2 text-right"><input type="radio" id="requisitionStatus" name="requisitionStatus" value="@requisitionStatus.Id"></div>
            <div class="col-sm-6">@requisitionStatus.Name</div>
            <div class="col-sm-2 text-left" style="background-color: @requisitionStatus.Color; height: 16px; width: 8px;"></div>
        </div>
    }
    <div class="row" style="padding-top: 18px;">
        <div class="col-sm-2 text-right"></div>
        <div class="col-sm-4">Test Date</div>
        <div class="col-sm-6 text-left"><input type="text" id="requisitionTestDate" name="requisitionTestDate" value="" style="width: 128px;"></div>
    </div>
    <div class="row" style="padding: 24px 12px 0 12px;">
        <div class="col-sm-4">Comment</div>
    </div>
    <div class="row" style="padding: 4px 4px 0 12px;">
        <div class="col-sm-12">
            <textarea rows="2" style="width: 100%; resize: vertical;" maxlength="3000" id="requisitionComment" name="requisitionComment"></textarea>
        </div>
    </div>
</div>

