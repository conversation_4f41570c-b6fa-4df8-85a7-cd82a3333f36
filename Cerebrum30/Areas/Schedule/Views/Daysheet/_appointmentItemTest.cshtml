@model Cerebrum.ViewModels.Schedule.VMAppointmentItemTest

@{
    var popoverTitle = "";
    var isVP = Model.Name.ToLower() == "vp" ? true : false;
    var testUrl = isVP ? Url.Action("index","visit",new { area = "vp", appointmentTestId = Model.AppointmentTestId }) :
        Url.Action("Index", "Measurement", new { area = "Measurements", AppointmentID = Model.AppointmentId, TestID = Model.TestId, AppointmentTestID = Model.AppointmentTestId, officeId = Model.OfficeId, date = Model.SelectedDate });


    var reportUrl = isVP ? Url.Action("VPReportLetter", "Reports", new { area = "Documents", appointmentId = Model.AppointmentId, appointmentTestId = Model.AppointmentTestId }) :
        Url.Action("ViewReport", "Reports", new { area = "Documents", appointmentId = Model.AppointmentId, testId = Model.TestId, appointmentTestId = Model.AppointmentTestId, patientId=Model.PatientId });

    var appTestMenu = String.Format("<span style=\"display:inline-block; padding-right:1px width:10px; margin-right:1px\"><span data-app-id=\"{0}\" data-apptest-id=\"{1}\" data-cb-tp=\"tooltip\" data-cb-tp-placement=\"top\" data-cb-tp-title=\"Change test status\" class=\"glyphicon glyphicon-pencil btn-apptest-status c-pointer 56\"></span></span>", Model.AppointmentId, Model.AppointmentTestId);
    switch (Model.TestStatus)
    {
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Test_Started:
            popoverTitle = "Start Test";
            //textColor = "#808080";
            break;
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReadyToStart:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Arrived:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Not_Arrived:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Appointed:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.BeingSent:
            popoverTitle = "Start Test";
            break;
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Test_Completed:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReadyForDoctor:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Images_Transferred:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.TraineeReportReady:
        case AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReportCompleted:
            popoverTitle = "Menu";
            break;
        //default: popoverTitle = "Remove Test";
        default:
            popoverTitle = "Menu";
            break;
    };
}

@if (Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReadyForDoctor ||
    Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Test_Completed ||
    Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.Images_Transferred ||
    Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.TraineeReportReady ||
    Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReportCompleted)
{

    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">

        @if (Model.TestStatus == AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReportCompleted)
        {
            var linkDesc = isVP ? "VP" : "Worksheet";
            var linkText = isVP ? "View Letter" : "View Report";
            <div class="test-popover">
                <div class="btn-popover-container">
                    <span class="popover-btn apptest-pointer">@Model.Name</span> 
                    <div class="btn-popover-title">
                        <span class="default-text-color">@popoverTitle</span>
                    </div>
                    <div class="btn-popover-content">
                        <ul class="ul-appstatus">
                            <li class="app-status-item">
                                <a target="_blank" href='@Url.Action("OpenRawDataClassicByPatient", "Measurement", new { area = "Measurements", appointmentId = Model.AppointmentId,    patientID = Model.PatientId })'>Legacy Data</a>
                            </li>
                            @if (!Model.IsImported)
                            {
                                <li class="app-status-item">
                                    <a data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" class="test-report-url" target="_blank" href="@reportUrl">@linkText</a>
                                </li>
                            }                            
                            <li class="app-status-item">
                                <a data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" class="test-main-url" href="@testUrl" rel="opener" target="_blank">View @linkDesc</a>
                            </li>
                            <li class="app-status-item">
                                <a data-apptest-id="@Model.AppointmentTestId" class="btn-view-send-history" href="#">View Send History</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-left">
                <a data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" class="anchor-white test-main-url" rel="opener" target="_blank" href="@testUrl">@Model.Name @Html.Raw(appTestMenu)</a>
            </div>
        }

        @(await Html.PartialAsync("_appointmentItemTestDevice", (object)(object)Model))
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>

}
else
{

    <div data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="@Model.TestStatusDesc" class="pull-left test-container @Model.TestStatus.ToString()">
        <div>
            <a data-app-id="@Model.AppointmentId" data-apptest-id="@Model.AppointmentTestId" class="anchor-white test-main-url" rel="opener" target="_blank" href="@testUrl">
                @Model.Name 
                @if (Model.TestStatus != AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.BeingSent)
                {
                    @Html.Raw(appTestMenu)
                }
            </a>
        </div>        
         @(await Html.PartialAsync("_appointmentItemTestDevice", (object)(object)Model))        
        <div class="test-time">
            <small>@Model.TestStartTime</small>
        </div>
    </div>  

}