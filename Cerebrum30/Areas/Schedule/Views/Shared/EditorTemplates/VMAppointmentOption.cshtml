@model Cerebrum.ViewModels.Schedule.VMAppointmentOption

@if (Model.UseForm)
{
    using (Html.BeginForm("bookappointment", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class = "appointment-option-book", @id = "frm-appointmentbook-" + Model.OptionNumber }))
    {
        @(await Html.PartialAsync("_appointmentOption", (object)(object)Model))
    }
}
else
{
    @(await Html.PartialAsync("_appointmentOption", (object)(object)Model))
}

