@model Cerebrum.ViewModels.Schedule.VMOfficeSchedule

@Html.ValidationSummary(false, "", new { @class = "text-danger" })


@foreach (var item in Model.userSchedules)
{
    if (item.userScheduleDays.Count() > 0)
    {
        <div class="panel panel-warning">
            <div class="panel-heading">@item.UserType</div>
            <div class="panel-body">
                <table class="table table-striped">
                    <tr>
                        <td>
                            <div class="row">
                                @foreach (var f in item.userScheduleDays)
                                {

                                    @(await Html.PartialAsync("_userScheduleDay", (object)(object)f))

                                }
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

    }
}
