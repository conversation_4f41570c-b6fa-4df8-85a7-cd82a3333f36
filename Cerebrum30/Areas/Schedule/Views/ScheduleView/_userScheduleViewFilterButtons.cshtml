@model Cerebrum.ViewModels.Schedule.VMUserScheduleViewFilterButtons
@{
    var userId = Model.FilterMain.UserId;
    var officeId = Model.FilterMain.OfficeId;
    //var filterTypeId = Model.FilterMain.FilterTypeId;
    var totalSelectedDoctors = Model.PracticeDoctors.Cast<dynamic>().Where((System.Func<dynamic, bool>)(x => x.Selected)).Count();
    var totalSelectedTechs = Model.Technicians.Cast<dynamic>().Where((System.Func<dynamic, bool>)(x => x.Selected)).Count();
}

<div class="flex flex-wrap gap-2 div-user-sch-filter-buttons">

    <button type="button" data-filter-type-id="2" data-modal-id="div-sch-doctors-filter-modal" class="btn btn-white-box btn-xs btn-user-sch-filter-type">Doctors <span id="ds-filter-count-doctor">(@totalSelectedDoctors)</span></button>
    <button type="button" data-filter-type-id="3" data-modal-id="div-sch-techs-filter-modal" class="btn btn-white-box btn-xs btn-user-sch-filter-type">Techs <span id="ds-filter-count-techs">(@totalSelectedTechs)</span></button>
    <button type="button" data-url="@Url.Action("ResetUserScheduleViewFilters", "scheduleview", new { area = "schedule" })" data-office-id="@officeId" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="For Doctor and Technician Filters" class="btn btn-white-box btn-xs btn-user-sch-filter-reset">Clear filters</button>

    @* Doctors filter *@
    <div class="modal fade" id="div-sch-doctors-filter-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"><span>Doctors</span></h4>
                </div>

                <div class="modal-body">
                    <div id="div-sch-doctors-filter">
                        <div class="filter-sch-frm-container">
                            <form class="frm-buttons-user-sch-filters" method="post" role="form" action="@Url.Action("SaveUserScheduleViewFilters", "scheduleview", new { area = "schedule" })">
                                <div class="form-horizontal">
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12">
                                            @Html.AntiForgeryToken()
                                            @Html.Hidden("OfficeId", (object)(object)officeId)
                                            @Html.Hidden("UserId)", (object)userId)
                                            @Html.Hidden("FilterTypeId", (object)(object)2)
                                            <div class="content-height300">
                                                @{ await Html.RenderPartialAsync("_userScheduleViewFilterList", (object)Model.PracticeDoctors);}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="clearBoth"></div>
                                    <div class="form-group form-group-sm">
                                        <div class="col-md-12 text-right">
                                            <button type="submit" class="btn btn-primary btn-xs modal-submit-btn">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">                    
                    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                </div>
            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>    

    @* Techs filter *@
    <div class="modal fade" id="div-sch-techs-filter-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"><span>Techs</span></h4>
                </div>

                <div class="modal-body">
                    <div id="div-sch-techs-filter">
                        <form class="frm-buttons-user-sch-filters" method="post" role="form" action="@Url.Action("SaveUserScheduleViewFilters", "scheduleview", new { area = "schedule" })">
                            <div class="form-horizontal">
                                <div class="form-group form-group-sm">
                                    <div class="col-md-12">
                                        @Html.AntiForgeryToken()
                                        @Html.Hidden("OfficeId", (object)(object)officeId)
                                        @Html.Hidden("UserId)", (object)userId)
                                        @Html.Hidden("FilterTypeId", (object)(object)3)
                                        <div class="content-height300">
                                            @{ await Html.RenderPartialAsync("_userScheduleViewFilterList", (object)Model.Technicians);}
                                        </div>
                                    </div>
                                </div>
                                <div class="clearBoth"></div>
                                <div class="form-group form-group-sm">
                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary btn-xs modal-submit-btn">Save</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                </div>
            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>
    
</div>





