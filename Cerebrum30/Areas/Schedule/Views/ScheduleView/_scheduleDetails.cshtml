@model Cerebrum.ViewModels.Schedule.VMSchedule
@{
    string ptNewColor = "#DC143C"; //crimson
    string ptNewTriageColor = "#228B22"; //forestgreen
    string ptNew2PtColor = "#FF00FF"; //fuchsia
    string ptNewRegularColor = "#FFD700"; //Gold
    string ptRegularColor = "#1E90FF"; //dodgerblue
    string ptRegular2Color = "#0000FF"; //Blue
    string reservedSlotColor = "#581845";

    var newPatientAppTypes = new List<int>() { 12,13,14,15,16,17,18,19,20,21,22 };
    var appTypes = new List<Cerebrum.ViewModels.Schedule.VMAppointmentType>();
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 1, Name = "New Patient", Color = ptNewColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 2, Name = "New Triage Patient", Color = ptNewTriageColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 3, Name = "2 New Patients", Color = ptNew2PtColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 4, Name = "New + Regular", Color = ptNewRegularColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 5, Name = "Regular", Color = ptRegularColor });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 6, Name = "2 Regular", Color = ptRegular2Color });
    appTypes.Add(new Cerebrum.ViewModels.Schedule.VMAppointmentType() { Id = 7, Name = "Reserved", Color = reservedSlotColor });
}
<div class="row row-bottom-padding">
    <div class="col-md-12">
        <ul class="top-legend pull-right">
            <li>
                <div style="padding-left:10px;" class="pull-left legend-label">Test Colors:&nbsp;</div>
            </li>
            @foreach (var item in Model.TestTypes)
            {
                <li class="li-color-desc">
                    <div class="pull-left legend-name">@item.Name</div>
                    <div class="pull-left legend-color-container" style="background-color:@item.Color;"></div>
                </li>
            }
        </ul>
        <ul class="top-legend pull-left">
            <li>
                <div class="pull-left legend-label">Visit Colors:&nbsp;</div>
            </li>            
            @foreach (var item in appTypes)
            {
                <li class="li-color-desc">
                    <div class="pull-left legend-name">@item.Name</div>
                    <div class="pull-left legend-color-container" style="background-color:@item.Color;"></div>
                </li>
            }
        </ul>
    </div>      
</div>

@*for the top scrolling div above the schedule*@
<div style="position:relative">
    <div class="schedule-top-scroll">
        <div class="scroll-time-col">&nbsp;</div>
        <div class="scroll-schedule">
            <div class="scroll-schedule-content">
                &nbsp;
            </div>
        </div>
    </div>
</div>

<div class="schedule-wrapper"> 
    <div class="schedule-timeslot-wrapper">
        <div class="border-right timeslot-container">            
            <div class="schedule-day-header" style="background-color:#808080; color:#f2f2f2; padding-top:5px">Time 
                
            </div>  
            <div class="resources-info-wrapper">
                <div class="resource-header"></div>
            </div>           
            @foreach (var time in Model.TimeSlots)
            {
                <div class="cell-divider timeslot-cell"><span>@time.Time</span></div>
            }            
        </div>
    </div> 
    <div id="sch-wp-details" data-office-id="@Model.Office.Id" 
         data-practice-id="@Model.Office.PracticeId" 
         data-hour-earliest="@Model.TimeSlots.First().Hour" 
         data-hour-latest="@Model.TimeSlots.Last().Hour" 
         data-time-increment="@Model.TimeSlotIncrement"
         data-load-url="@Url.Action("GetScheduledDay","scheduleview",new { area= "Schedule" })"
         class="schedule-details-wrapper">     
    @{  var count = 0; }
    @foreach (var day in Model.ScheduleDays)
    {
        <div data-day-id="@day.DayId" data-scheduled-day="@day.Date.ToShortDateString()" class="scheduled-day day-divider" style="display:table-cell;height:inherit;position:relative;">
            @{ await Html.RenderPartialAsync("_scheduleDay", (object)day); }
        </div>
    } @*foreach schedule day loop end*@
                      
    <div style="height:16px"></div>@*17.5*@
        
    </div> <!-- end schedule details wraaper-->
            
    <div class="clearfix"></div>
</div>