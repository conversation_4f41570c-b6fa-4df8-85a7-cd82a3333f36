@model Cerebrum.ViewModels.Schedule.VMScheduleBase

@{
    ViewBag.Title = "Appointments";
    Layout = "~/Areas/Schedule/Views/Shared/_LayoutSchedule.cshtml";
}

@if (CerebrumUser.UserOffices.Any())
{
    await Html.RenderPartialAsync("_scheduleBar", (object)Model);

    if (Model.Office != null)
    {
        if (Model.Office.HasSchedule)
        {
            <div id="schedule-container"
                 data-office-id="@Model.Office.Id"
                 data-selected-date="@Model.SelectedDate.ToShortDateString()"
                 data-schedule-type-id="@Model.ScheduleType.Id">
                <div id="div-sch-details-loading"><img src="../../Content/fancybox_loading.gif" /></div>
            </div> @*schedule container end*@
            <script type="text/javascript">
                $(function () {
                    getScheduleData();
                });
            </script>
        }
        else
        {
            <div id="schedule-container">@(await Html.PartialAsync("_noSchedule"))</div> @*schedule container end*@
        }
    }
    else
    {
        <div id="schedule-container">@(await Html.PartialAsync("_noOffice"))</div> @*schedule container end*@
    }

}
else
{
    await Html.RenderPartialAsync("_noUserOffice");
}
