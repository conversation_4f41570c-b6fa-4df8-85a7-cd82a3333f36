@model Cerebrum.ViewModels.Schedule.VMScheduleBase
<style>
    .glyphicon {
        font-size: 10px !important;
    }
    .wrapper-height {
        height: 58px;
    }
    @@media screen and (max-width: 1300px) {
        .wrapper-height {
            height: 59px;
        }
    }
</style>
@if (Model.Office != null)
{
    var urlFormBase = Url.Action("index", "scheduleview", new { area = "schedule" });

    var urlToday = Url.Action("index", "scheduleview", new { area = "schedule", OfficeId = Model.Office.Id, Date = String.Format("{0:MM/dd/yyyy}", System.DateTime.Now), ScheduleTypeId = Model.ScheduleType.Id, SortByTypeId = Model.SortByTypeId });
    var officeDesc = "Schedule for " + Model.Office.Name + " Office";


@Html.Hidden("page-refresh", "no")
@Html.Hidden("hdSortByTypeId", (object)Model.SortByTypeId)
<div class="row row-top-padding">
    <div class="col-xs-12 col-sm-12 col-md-5 col-lg-5" data="1">
        <div class="form-inline">
            <form id="frm-office-date" action="@urlFormBase" method="get" class="__887034">
                @{
                    var officeId = Model.Office != null ? Model.Office.Id : 0;
                    <div class="form-group form-group-sm" @*style="width:155px"*@>
                        <label class="control-label" for="OfficeId">Office</label>
                        @Html.DropDownList("OfficeId", new SelectList(Model.Offices, "Id", "Name", officeId), new { @class = "form-control" })

                        @Html.ValidationMessage("OfficeId", new { @class = "text-danger" })
                    </div>
                    <div class="form-group form-group-sm">
                        @Html.Hidden("ScheduleTypeId", (object)(object)Model.ScheduleType.Id)
                        <label class="control-label" for="Date">Date</label>
                        @Html.TextBox("Date", (object)String.Format("{0:MM/dd/yyyy}", Model.SelectedDate), new { @class = "form-control date-picker input-sm" })
                    </div>
                    <div class="form-group form-group-sm">
                        <a href="@urlToday" class="btn btn-default btn-xs href-url">Today</a>
                    </div>
                }
            </form>
        </div>
    </div>

    <div class="col-xs-12 col-sm-12 col-md-5 col-lg-5" data="2">
        <div class="btn-group btn-spacing pull-left" role="group">
            @foreach (var item in Model.ScheduleTypes)
            {
                var urlSchType = "";
                urlSchType = Url.Action("index", "scheduleview", new { area = "schedule", OfficeId = Model.Office.Id, ScheduleTypeId = item.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.SelectedDate), SortByTypeId = Model.SortByTypeId });

                if (Model.ScheduleType != null && Model.ScheduleType.Id == item.Id)
                {
            <a data-form-url="@urlFormBase" class="btn btn-primary btn-sm office-schedule href-url"
               href="@urlSchType">
                @item.Name
            </a>
                }
                else
                {
            <a data-form-url="@urlFormBase" class="btn btn-default btn-sm office-schedule href-url"
               href="@urlSchType">
                @item.Name
            </a>
                }
            }

            <a data-form-url="@urlFormBase" class="btn btn-default btn-sm office-schedule href-url"
               href="@Url.Action("index", "scheduleview", new { area = "schedule", OfficeId = Model.Office.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.Prev3Date), ScheduleTypeId = Model.ScheduleType.Id, SortByTypeId = Model.SortByTypeId})">
                <span class="glyphicon glyphicon-chevron-left"></span> Previous
            </a>

            <a data-form-url="@urlFormBase" class="btn btn-default btn-sm office-schedule href-url"
               href="@Url.Action("index", "scheduleview", new { area = "schedule", OfficeId = Model.Office.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.Next3Date), ScheduleTypeId = Model.ScheduleType.Id, SortByTypeId = Model.SortByTypeId })">
                Next <span class="glyphicon glyphicon-chevron-right"></span>
            </a>
        </div>

        @if (Model.Office.HasSchedule)
        {
        <script type="text/javascript">
                $(function () {
                    loadAppScheduleModal(@Model.Office.PracticeId,@Model.Office.Id);
                    $('.office-schedule').prop('disabled', 'disabled');
                });
        </script>
        <div class="pull-left">
            <a data-modal-url="@Url.Action("create", "appointments", new { area = "schedule" })"
               data-practice-id="@Model.Office.PracticeId"
               data-office-id="@Model.Office.Id"
               data-appointment-day="@Model.SelectedDate.ToShortDateString()"
               data-schedule-type="@Model.ScheduleType.Id"
               data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Normal"
               class="btn-sch-new-appointment btn btn-sm btn-default btn-spacing" href="#">
                <span class="glyphicon glyphicon-calendar text-primary"></span> New Appointment
            </a>
        </div>
        <button type="button" id="btn-user-sch-filters" class="btn btn-default btn-sm" data-office-id="@Model.Office.Id" data-selected-date="@Model.SelectedDate.ToShortDateString()" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Filter schedule by staff" data-url="@Url.Action("GetUserScheduleViewMain", "scheduleview", new { area = "schedule" })"><span class="glyphicon glyphicon-filter default-text-color"> </span> Filters (@Model.FilterCount)</button>
        }
        else
        {
        <div class="pull-left text-danger">
            Please contact Admin for Office Schedule
        </div>
        }
    </div>


    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="3">

    </div>
</div>

<div id="sidebar-right-open-contents">


</div>
}

