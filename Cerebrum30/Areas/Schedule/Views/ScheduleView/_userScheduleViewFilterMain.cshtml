@model Cerebrum.ViewModels.Schedule.VMUserScheduleViewFilterMain

@using (Html.BeginForm("SaveUserScheduleViewFilters", "scheduleview", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-schedule-view-filter", @class = "" }))
{
    @Html.ModalHeader("Staff Filters")
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.UserId)
        @Html.HiddenFor(model => (object)model.FilterTypeId)
        
        <div class="form-horizontal">    

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-4">
            @{ var loadUrl = Url.Action("GetUserScheduleViewOptions", "scheduleview", new { area = "schedule" });}
            @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.Offices, "Id", "Name", Model.OfficeId), new { @class = "form-control", data_load_url=loadUrl })
            @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
        </div>
    </div>   

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.UserScheduleViewFilters, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
        <div class="col-md-10">
            <div id="user-schedule-view-container" class="content-height300">
                @{ await Html.RenderPartialAsync("_userScheduleViewFilterList", (object)Model.UserScheduleViewFilters);}
            </div>
            @Html.ValidationMessageFor(model => model.UserScheduleViewFilters, "", new { @class = "text-danger" })
        </div>
    </div>   

   
        </div><!-- end form horizontal-->
    </div><!--Modal body end-->
    @Html.ModalFooter("Save", "blue")
}




