@model Cerebrum.ViewModels.Documents.VMReferralDocumentMain

@Html.ModalHeader("Referral Documents for "+Model.PatientFullName)

<div class="modal-body">
    
    <div id="refDocsList-container">
        @(await Html.PartialAsync("_patReferralDocList", (object)(object)Model.ReferralDocuments))
    </div>
</div>

<div class="modal-footer">    
    <button data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("UploadReferralDocument", "uploads", new { area = "documents" })" type="button" class="btn-ref-documents-upload btn btn-default btn-sm"><span class="glyphicon glyphicon-folder-open default-text-color"> </span>&nbsp;Upload New</button>
    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
</div>

@*@Html.ModalFooter(isInfoModal: true)*@

<div style="display:none;" id="refdocview-container">
    @Html.ModalHeader("Documents for " + Model.PatientFullName)
    <div class="modal-body">        
        <div style="display:none;" id="refdoc-ajax-loader">
            <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
        </div>
        <iframe style="width:100%;height:600px;" class="if-referralDocuments"></iframe>        
    </div>
    @*@Html.ModalFooter(isInfoModal: true)*@
    <div class="modal-footer">        
        <span class="ref-doc-current"></span><span> of </span><span class="ref-doc-total"></span>        
        <button type="button" class="btn btn-default btn-sm btn-refdoc-prev"><span class="glyphicon glyphicon-chevron-left"></span></button>
        <button type="button" class="btn btn-default btn-sm btn-refdoc-next"><span class="glyphicon glyphicon-chevron-right"></span></button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
    </div>
</div>