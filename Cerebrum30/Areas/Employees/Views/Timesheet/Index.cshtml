
@{
    ViewBag.Title = "Timesheet";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
<script src="~/Scripts/moment.min.js"></script>
<script src="~/Areas/Employees/Scripts/c3-timesheet.js"></script>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4>Timesheet Search</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("TimesheetSearch", (object)new { area="Employees"}))
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div id="all-users-info-for-timesheet" style="height:650px;overflow-y: scroll;" class="col-sm-7"></div>
    <div id="edit-user-punch-in-outs" class="col-sm-4" ></div>
</div>
<br />
<hr />
