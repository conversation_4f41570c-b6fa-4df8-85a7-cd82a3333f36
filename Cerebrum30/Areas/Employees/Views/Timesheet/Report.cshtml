
@{
    ViewBag.Title = "Timesheet";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
<script src="~/Scripts/moment.min.js"></script>
<script src="~/Areas/Employees/Scripts/c3-timesheet.js"></script>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4>Timesheet Detail Search</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("WorkingHoursSearch", (object)new { area = "Employees" }))
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="panel panel-default ">
        <div class="panel-heading">Timesheet Report
        <span class="pull-right  btn-timesheet-print">Print</span> 
        </div>
        <div class="panel-body">
            <div id="all-users-worked-hours" class="col-md-8"></div>
        </div>
    </div>
    
</div>

