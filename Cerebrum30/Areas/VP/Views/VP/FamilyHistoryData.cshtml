@model Cerebrum.ViewModels.VP.VMFamilyHistoryControl
<script>
    $(document).ready(function () {
        initPopover();
    });

    function editFamilyHistory(element) {
        var _patientid = $(element).attr("patientid");
        var _rowid = $(element).attr("rowid");
        //var _cppType = $(element).data("cpptype");
        //var _cntrlName = $(element).data("name");

        var data = { patientID: _patientid, rowid: _rowid, };
        url = 'VP/VP/EditFamilyHistory';

        $("#" + modalTemplateContentId).width("1560px");
        //$("#" + modalTemplateBodyId).height(height);
        $("#" + modalTemplateTitleId).text("Edit");

        ajaxCall(url, data, false, function (data) {
            $("#" + modalTemplateBodyId).html(data);
            $("#" + modalTemplateId).on("shown.bs.modal", function () { CPPEditModalMode = true; });
            $("#" + modalTemplateId).on("hidden.bs.modal", function () { CPPEditModalMode = false; });
            $("#" + modalTemplateId).modal({ keyboard: false, backdrop: "static" }, "show");
        });

        return false;
    }
</script>
<div class="" id="divGrd" style="padding-top:30px;">
    <div class="row">
        <div class="col-lg-12">
            <div class="pre-scrollable">
                <table class="table table table-striped table-bordered table-condensed ">
                    <tr>
                        <td class="spanCell"><b>&nbsp; </b></td>
                        <td class="spanCell"><b>Show</b></td>
                        <td class="spanCell"><b>Diagnostic Code</b></td>
                        <td class="spanCell"><b>Diagnosis</b></td>
                        <td class="spanCell"><b>Problem Description</b></td>
                        <td class="spanCell"><b>Treatment</b></td>
                        <td class="spanCell"><b>Relation</b></td>
                        <td class="spanCell"><b>AgeOnset</b></td>
                        <td class="spanCell"><b>Life Stage </b></td>
                        <td class="spanCell"><b>Start Date </b></td>
                        <td class="spanCell"><b>Added </b></td>
                        <td class="spanCell"><b>Position </b></td>
                        @*<td class="spanCell"><b>Status </b></td>*@
                        <td class="spanCell"><b>Notes </b></td>
                    </tr>

                    @for (int i = 0; i < @Model.FamilyHistories.Count; i++)
                    {

                        @Html.HiddenFor(x => (object)x.FamilyHistories[i].Id)

                        <tr>

                            <td class="spanCell">
                               @Html.ActionLink("Edit", "EditFamilyHistory", new { EntryID = Model.FamilyHistories[i].Id, patientID = Model.PatientID },
                               new
                               {
                                   @patientid = @Model.PatientID,
                                   @rowid = @Model.FamilyHistories[i].Id,
                                   onclick = "return editFamilyHistory(this);"
                               })
                                <br /><br />
                                <a href="#" id="<EMAIL>[i].Id" onclick="return cppButtonDeleteClicked(this);" data-name="<EMAIL>[i].Id" data-patientid="@Model.PatientID" data-cpptype="1" data-rowid="@Model.FamilyHistories[i].Id">Delete</a>
                                <br /><br />
                                <div class="btn-popover-container">
                                    <button type="button" class="btn btn-default btn-xs popover-btn" data-original-title="" title="" data-toggle="popover">
                                        <span class="glyphicon glyphicon-th-list" style="color: #337ab7;"></span>
                                    </button>
                                    <div class="btn-popover-title">Residual Information</div>
                                    <div class="btn-popover-content">@Html.Raw(Model.FamilyHistories[i].ResidualInformation.Replace("\n", "<br />"))</div>
                                </div>
                            </td>

                            <td class="spanCell">
                                @Html.CheckBoxFor(model => model.FamilyHistories[i].Visible, new { @disabled = "disabled" })
                            </td>

                            <td class="spanCell" align="right">
                                <div>Code System: @Html.DisplayFor(model => (object)model.FamilyHistories[i].CodingSystem)</div>
                                <br />
                                <div style="margin-top: -12px;">Code: @Html.DisplayFor(model => (object)model.FamilyHistories[i].DiagnosticCode)</div>
                            </td>
                            <td class="spanCell">
                                @Html.TextAreaFor(model => model.FamilyHistories[i].DiagnosticDescription, new { @class = "", @cols = 25, @rows = 5 })
                            </td>
                            <td class="spanCell">
                                @Html.TextAreaFor(model => model.FamilyHistories[i].ProblemDescription, new { @class = "", @cols = 25, @rows = 5 })
                            </td>

                            <td class="spanCell">
                                @Html.TextAreaFor(model => model.FamilyHistories[i].Treatment, new { @class = "", @cols = 25, @rows = 5 })
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.FamilyHistories[i].RelationShip, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @(Model.FamilyHistories[i].AgeOnset == 0 ? string.Empty : Model.FamilyHistories[i].AgeOnset.ToString() + " " + (string.IsNullOrEmpty(Model.FamilyHistories[i].Unit) ? string.Empty : Model.FamilyHistories[i].Unit))
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.FamilyHistories[i].LifeStage, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @(((Model.FamilyHistories[i].StartDateMonth == 0 ? "" : @Model.FamilyHistories[i].StartDateMonth.ToString()) + (Model.FamilyHistories[i].StartDateDay == 0 ? "" : "/" + @Model.FamilyHistories[i].StartDateDay.ToString()) + (Model.FamilyHistories[i].StartDateYear == 0 ? "" : "/" + Model.FamilyHistories[i].StartDateYear.ToString())).Trim('/')))
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.FamilyHistories[i].AddDate )
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.FamilyHistories[i].Position, new { @class = "txtBox" })
                            </td>
                            @*<td class="spanCell">
                                @Html.CheckBox("ddStatus", (object)(object)(object)Model.FamilyHistories[i].Status == 1)
                            </td>*@
                            <td class="spanCell">
                                @Html.TextAreaFor(model => model.FamilyHistories[i].Notes, new { @class = "", @cols = 25, @rows = 5 })
                            </td>
                        </tr>

                    }

                </table>
            </div>
        </div>
    </div>
</div>