@model Cerebrum.ViewModels.VP.VP_VM
@{
    ViewBag.Title = "Visit Page";
    ViewBag.ModuleName = "Visit Page";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
@section customcss{
    @Styles.Render("~/Areas/VP/Content/css")
}

@section topscripts{
    @*<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.5/handlebars.js"></script>*@

}
@section scripts
{
    @Scripts.Render("~/bundles/vp")
    <script src="~/Areas/Measurements/Scripts/SendToContacts.js"></script>
    
}
@section patientinfo{
    @(await Html.PartialAsync("_PatientInfoMenuVP", (object)(object)Model))
}




<div id="div-diagnoseCodes" class="hidden" data-toggle="popover"></div>
<div id="div-diagnoseCodes2" class="hidden" data-toggle="popover"></div>
<div id="div-diagnoseCodes3" class="hidden" data-toggle="popover"></div>
<div id="div-consultCodes" class="hidden" data-toggle="popover"></div>
<div id="div-consultCodes2" class="hidden" data-toggle="popover"></div>
<div id="div-consultCodes3" class="hidden" data-toggle="popover"></div>
<div id="div-prev-tests-menu" class="hidden" data-toggle="popover"></div>

<div>
    <div id="vp-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="vp-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>




@using (Html.BeginForm("Index", "VP", FormMethod.Post, new { @autocomplete = "off", @id = "frm-vp", model = @Model }))
{

    @Html.HiddenFor(model => (object)model.ConsultCode.Id)
    @Html.HiddenFor(model => (object)model.ConsultCode2.Id)
    @Html.HiddenFor(model => (object)model.ConsultCode3.Id)

    @Html.HiddenFor(model => (object)model.DiagnoseCode.Id)
    @Html.HiddenFor(model => (object)model.DiagnoseCode2.Id)
    @Html.HiddenFor(model => (object)model.DiagnoseCode3.Id)

    @Html.HiddenFor(m => (object)m.AppointmentTestID)
    @Html.HiddenFor(m => (object)m.AppointmentID)
    @Html.HiddenFor(m => (object)m.IsWorkList)

    @Html.HiddenFor(m => (object)m.PatientID)
    @Html.HiddenFor(m => (object)m.OfficeID)
    @Html.HiddenFor(m => (object)m.UserID)
    @Html.HiddenFor(m => (object)m.SpecialityID)
    @Html.HiddenFor(m => (object)m.DateStr)
    @Html.HiddenFor(m => (object)m.TestID)

    @Html.HiddenFor(m => (object)m.DoctorID)
    @Html.HiddenFor(m => (object)m.PracticeDoctorID)
    @Html.HiddenFor(m => (object)m.AppointmentPracticeDoctorID)
    @Html.HiddenFor(m => (object)m.AppointmentBillStatus)
    @Html.HiddenFor(m => (object)m.AssociatedDoctorID)
    @Html.HiddenFor(x => (object)x.RootPhraseFormat)
    @Html.HiddenFor(x => x.ReportPhraseFormatTable, new { @id = "RootPhraseFormatTable" })
    @Html.HiddenFor(m => (object)m.IsClassicAppointment)
    @Html.HiddenFor(m => (object)m.ReportCompleted)
    @Html.HiddenFor(m => (object)m.Amended)

    <div>
        
        <div id="div-links" class="vp-links _34534 _toCloneIntoVP" style="display:none">
            @(await Html.PartialAsync("VP_Links", (object)(object)Model))
        </div>



        @*@(await Html.PartialAsync("_PatientInfoMenuVP", (object)(object)Model))*@
       

        <div id="div-cpp">
           @(await Html.PartialAsync("CPP", (object)(object)Model))
        </div>
        <div id="div-phrases">
            @(await Html.PartialAsync("ReportPhrases", (object)(object)Model))            
        </div>
               

        <div id="div-meas">@(await Html.PartialAsync("Measurements", (object)(object)Model))</div>
             

        <div class="row">&nbsp;</div>
       
        @{
            var ccDoctorLink = new Cerebrum.ViewModels.Common.VMCCDoctorLink();
            ccDoctorLink.PatientId = Model.PatientID;
            ccDoctorLink.AppointmentId = Model.AppointmentID;
            ccDoctorLink.PatientFullName = "";
            ccDoctorLink.IsModal = false;
        }
        @{ await Html.RenderPartialAsync("_CCDoctorsLink", (object)ccDoctorLink); }
        
        
        <div class="row">&nbsp;</div>
        <div class="row">
            <div class="col-sm-6">
                <div class="text-left nopadding form-group form-inline form-group-sm">
                    <span><b>Log</b></span>
                    @Html.DropDownListFor(m => m.AppointmentTestLogID,
                        new SelectList(Model.AppointmentTestLogs, "Value", "Text", @Model.AppointmentTestLogID),
                        new { @class = "dropdown form-control ", @id = "dd", @style = "min-width:100px" })
                </div>
            </div>

            <div class="col-sm-6" id="save-buttons">
                @if (!Model.IsClassicAppointment){
                    <div class="form-inline pull-right">
                        <div class="form-group form-group-sm _toCloneIntoVP2">
                            <a data-ds-url='@Url.Action("index", "daysheet",
                                new
                                {
                                    Area = "Schedule",
                                    OfficeId = Model.OfficeID,
                                    Date = Model.DateStr
                                })'
                               href="javascript:void(0)" 
                               @*id="btn-save-vp"*@ 
                               class="btn-save-vp btn btn-default btn-sm btn-vp-save-type" data-url='@Url.Action("CreateNewLog")'><i class="glyphicon glyphicon-floppy-save"></i>Save</a>

                            <a data-ds-url='@Url.Action("index", "daysheet",
                                    new
                                    {
                                        Area = "Schedule",
                                        OfficeId = Model.OfficeID,
                                        Date = Model.DateStr
                                    })'
                               href="javascript:void(0)" 
                               class="btn-save-draft-vp btn btn-default btn-sm btn-vp-save-type" data-url='@Url.Action("Save_Draft")' 
                                @*id="btn-save-draft-vp"*@ 
                                @*data-url='@Url.Action("Save_WS_Data")'*@>
                                <i class="glyphicon glyphicon-floppy-save"></i>Save & Change Status
                            </a>


                            <a data-ds-url='@Url.Action("index", "daysheet",
                               new
                               {
                                   Area = "Schedule",
                                   OfficeId = Model.OfficeID,
                                   Date = Model.DateStr
                               })'
                               target="_blank" 
                               class="btn-finalize-note btn btn-default btn-sm btn-vp-save-type" 
                               href="javascript:void(0)" 
                               data-cb-tp="tooltip"
                               data-cb-tp-title="Saves to patient's chart. This will NOT send the note externally."
                               data-url='@Url.Action("FinalizeChart")'
                               @*id="btn-finalize-note"*@
                               >
                                Save Chart Note
                            </a>

                            <a data-ds-url='@Url.Action("index", "daysheet",
                               new
                               {
                                   Area = "Schedule",
                                   OfficeId = Model.OfficeID,
                                   Date = Model.DateStr
                               })'
                               target="_blank" 
                               class="btn-send-letter btn btn-default btn-sm btn-vp-save-type" 
                                href="javascript:void(0)" 
                               data-url='@Url.Action("SendLetter", new { appointmentID = Model.AppointmentID, patientID = Model.PatientID  })'
                               @*id="btn-send-letter"*@
                               >
                                <i class="glyphicon glyphicon-share-alt"></i>
                                Send Letter
                            </a>

                        </div>
                    </div>
                }
            </div>
        </div>
        
    </div>
}


<br />
<br />


