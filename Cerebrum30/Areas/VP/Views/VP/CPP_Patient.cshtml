@model Cerebrum.ViewModels.VP.VP_VM
@{
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss{
    @Styles.Render("~/Areas/VP/Content/css")
}
@section topscripts{

}
@section scripts
{
    <script>

        var LoadMedications = function () {
            var url = "VP/VP/GetMedicationsByPatient?patientID=" + $("#PatientID").val() ;
            ajaxCall("VP/VP/GetMedicationsByPatient", {patientID: $("#PatientID").val()}, false, function (result) {
                $('#div-medications').html(result);
            });
            //$('#div-medications').load(url, function () {
            //});
        };

        var LoadCPP = function () {

            $("#div-cpp").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall('VP/VP/CPP', { AppointmentID: @Model.AppointmentID, PatientID :@Model.PatientID, PracticeDoctorID :@Model.PracticeDoctorID}, false, function (data) {
                $("#div-cpp").html(data);
            });
        };
        function OpenWindow(id, patientID) {
            $("#vp-modal-container").css('width', '95%');
            $("#vp-modal-container").css('padding', '0px');
            $("#vp-modal-container").css('margin', 'auto');
            //alert( id);
            var url = '';
            switch (id) {

                case "1":
                    url = "VP/VP/FamilyHistory?patientID=" + patientID + "&practiceDoctorId=" + @Model.DoctorID;
                    ajaxCall(url, "", false, function (result) {
                        $("#vp-modal-content").html(result);
                        $("#vp-modal-container").on("hidden.bs.modal", function () {
                            LoadFamilyHistory();
                            LoadCPP();
                        });

                        $("#vp-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');
                        $('#ajax-loader').hide();
                    });

                    break;

                case "2":
                    url = "VP/VP/ProblemList_CPP?patientID=" + patientID + "&appointmentId=" + @Model.AppointmentID + "&practiceDoctorId=" + @Model.DoctorID + "&isProblemList=false";
                    ajaxCall(url, "", false, function (data) {
                        $("#vp-modal-content").html(data);
                        $("#vp-modal-container").on("hidden.bs.modal", function () {
                            LoadCPP();
                        });

                        $("#vp-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');
                        $('#ajax-loader').hide();
                    });

                    break;

                case "3":
                    url = "VP/VP/ProblemList_CPP?patientID=" + patientID + "&appointmentId=" + @Model.AppointmentID + "&practiceDoctorId=" + @Model.DoctorID + "&isProblemList=true";
                    ajaxCall(url, "", false, function (data) {
                        $("#vp-modal-content").html(data);
                        $("#vp-modal-container").on("hidden.bs.modal", function () {
                            LoadCPP();
                        });

                        $("#vp-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');
                        $('#ajax-loader').hide();
                    });

                    break;

                case "4":
                    url = "VP/VP/RiskFactor_CPP?patientID=" + patientID + "&practiceDoctorId=" + @Model.DoctorID;
                    ajaxCall(url, "", false, function (data) {
                        $("#vp-modal-content").html(data);
                        $("#vp-modal-container").on("hidden.bs.modal", function () {
                            LoadHistory();
                            LoadCPP();
                        });

                        $("#vp-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');
                    });

                    break;

                case "5":

                    window.open("VP/VP/Immunization?patientID=" + patientID, "_blank");
                    break;

                case "6":
                    url = "VP/VP/Alert_CPP?patientID=" + patientID + "&practiceDoctorId=" + @Model.DoctorID;
                    ajaxCall(url, "", false, function (data) {
                        $("#vp-modal-content").html(data);
                        $("#vp-modal-container").on("hidden.bs.modal", function () {
                            LoadCPP();
                        });

                        $("#vp-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');
                        $('#ajax-loader').hide();
                    });

                    break;

                default:

                    alert("default!" + id);
                    //window.open("VP/VP/AddAlertCPP?patientID=" + patientID, "_blank");
                    break;
            }
        }

        $(document).on('click', '#btn-add-new-fh', function (e) {
            e.preventDefault();
            url = 'VP/VP/AddFamilyHistory';
            ajaxCall(url, $('#frm-familyhistory').serialize(), false, function (data) {
                if (data.Errored == "1") {

                    $("#span-result-add").html('');
                    $("#span-result-add").html(data.Message);
                    //showNotificationMessage('error', 'Changes not saved');
                }
                else {

                    //showNotificationMessage('success', 'Changes saved');
                    clear_form_elements('.container-familyhistory');
                    LoadFamilyHistory();
                    LoadCPP();
                }
            });
        });

        $(document).on('click', '.btn-cancel-model', function (e) {
            e.preventDefault();
           // $('#vp-modal-container').modal('hide');
        });

        $(document).ready(function () {

            $.ajaxSetup({ cache: false });
            LoadCPP();
        });
    </script>
}
@section patientinfo{
@(await Html.PartialAsync("_PatientInfoMenuVP", (object)(object)Model))   
}

@Html.HiddenFor(x => (object)x.AppointmentID)
@Html.HiddenFor(x => (object)x.PatientID)
@Html.HiddenFor(x => (object)x.UserID)


<div>
    <div id="vp-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="vp-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>

<div id="div-medications"></div>

<div>
    <div id="div-cpp">
        <img src="~/Content/fancybox_loading.gif" />
    </div>
</div>
