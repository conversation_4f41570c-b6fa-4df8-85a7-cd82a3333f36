@model Cerebrum.ViewModels.VP.VMCPPProblem

@Html.HiddenFor(x => (object)x.PatientID)
@Html.HiddenFor(x => (object)x.IsProblemList)

<style>
    .input-read-only {
        background-color: lightgray;
    }
    .input-edit {
        background-color: white;
    }
</style>

<script>
    var problemListSelectedIndex = -1;
    var problemListEditData;

    $(document).ready(function () {
        $(document).on("ICD10CodeSelected", function (event, ICD10Selected) { ICD10CodeSelectedProblemListEdit(ICD10Selected); });
        $(document).on("ReasonForDeletionCompleted", function (event) { reasonForDeletionCompletedProblemList(); });

        initPopover();
        changeEditMode(-1);
    });

    function problemListEditButtonClicked(index) {
        problemListEditData = GetProblemListData(index);
        changeEditMode(index);
        return false;
    }

    function problemListCancelButtonClicked(index) {
        SetProblemListData(index);
        changeEditMode(-1);
        return false;
    }

    function problemListSaveButtonClicked(index) {
        url = 'VP/VP/Save_ProblemList';
        ajaxCall(url, GetProblemListData(index), false, function (data) {
            if (data.Errored == "1") {
                alert(data.Message);
                //showMessageModal("error", data.Message, false);
            }
            else {
                //LoadCPP();
                LoadHistory();
            }
        });

        return false;
    }

    function OnsetAgeChangedProblemList(index) {
        $.ajax({
            type: "POST",
            url: 'VP/VP/GetLifeStage',
            data: { days: $("#problemListLifeStageYear_" + index).val(), option: $("#problemListLifeStageUnit_" + index).val() },
            success: function (data) {
                if (data.Result == "0") {
                    $("#problemListLifeStageText_" + index).val(data.Message);
                }
            },
            error: function (xhr, thrownError) {
                alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
            }
        });
    }

    function problemListICD10Clicked(index, element) {
        problemListSelectedIndex = index;
        return ICD10Clicked(element);
    }

    function ICD10CodeSelectedProblemListEdit(ICD10Selected) {
        $("#problemListCodingSystem_" + problemListSelectedIndex).val("ICD-10");
        $("#problemListDiagnosticCode_" + problemListSelectedIndex).val(ICD10Selected.code);
        $("#problemListDiagnosticDescription_" + problemListSelectedIndex).val(ICD10Selected.name);
    }

    function reasonForDeletionCompletedProblemList() {
        LoadHistory();
    }

    function GetProblemListData(index) {
        var data = {
            id: $("#problemListDataId_" + index).val(),
            isProblemList: @(Model.IsProblemList ? "true" : "false"),
            visible: $("#problemListShow_" + index).is(":checked"),
            patientRecordID: @Model.PatientID,
            practiceDoctorID: @Model.PracticeDoctorID,
            appointmentID: @Model.AppointmentID,
            position: $("#problemListPosition_" + index).val(),
            codingSystem: $("#problemListCodingSystem_" + index).val(),
            diagnosticCode: $("#problemListDiagnosticCode_" + index).val(),
            diagnosticDescription: $("#problemListDiagnosticDescription_" + index).val(),
            procedure: $("#problemListProcedure_" + index).val(),
            description: $("#problemListDescription_" + index).val(),
            status: $("#problemListStatus_" + index).val(),
            dateOnsetDay: $("#problemListDateOnsetDay_" + index).val(),
            dateOnsetMonth: $("#problemListDateOnsetMonth_" + index).val(),
            dateOnsetYear: $("#problemListDateOnsetYear_" + index).val(),
            resolutionDateDay: $("#problemListResolutionDateDay_" + index).val(),
            resolutionDateMonth: $("#problemListResolutionDateMonth_" + index).val(),
            resolutionDateYear: $("#problemListResolutionDateYear_" + index).val(),
            procedureDateDay: $("#problemListProcedureDateDay_" + index).val(),
            procedureDateMonth: $("#problemListProcedureDateMonth_" + index).val(),
            procedureDateYear: $("#problemListProcedureDateYear_" + index).val(),
            lifeStageYear: $("#problemListLifeStageYear_" + index).val(),
            lifeStageUnit: $("#problemListLifeStageUnit_" + index).val(),
            lifeStageText: $("#problemListLifeStageText_" + index).val(),
            note: $("#problemListLifeNote_" + index).val(),
            col1Visible: $("#problemListCol1Visible").is(":checked"),
            col2Visible: $("#problemListCol2Visible").is(":checked"),
            col3Visible: $("#problemListCol3Visible").is(":checked"),
            col4Visible: $("#problemListCol4Visible").is(":checked"),
            col5Visible: $("#problemListCol5Visible").is(":checked"),
            col6Visible: $("#problemListCol6Visible").is(":checked"),
            col7Visible: $("#problemListCol7Visible").is(":checked"),
            col8Visible: $("#problemListCol8Visible").is(":checked"),
            col9Visible: $("#problemListCol9Visible").is(":checked"),
            col10Visible: $("#problemListCol10Visible").is(":checked")
    };

        return data;
    }

    function SetProblemListData(index) {
        $("#problemListShow_" + index).prop("checked", problemListEditData.visible);
        $("#problemListPosition_" + index).val(problemListEditData.position);
        $("#problemListCodingSystem_" + index).val(problemListEditData.CodingSystem);
        $("#problemListDiagnosticCode_" + index).val(problemListEditData.DiagnosticCode);
        $("#problemListDiagnosticDescription_" + index).val(problemListEditData.DiagnosticDescription);        
        $("#problemListProcedure_" + index).val(problemListEditData.procedure);
        $("#problemListDescription_" + index).val(problemListEditData.description);
        $("#problemListStatus_" + index).val(problemListEditData.status);
        $("#problemListDateOnsetDay_" + index).val(problemListEditData.dateOnsetDay);
        $("#problemListDateOnsetMonth_" + index).val(problemListEditData.dateOnsetMonth);
        $("#problemListDateOnsetYear_" + index).val(problemListEditData.dateOnsetYear);
        $("#problemListResolutionDateDay_" + index).val(problemListEditData.resolutionDateDay);
        $("#problemListResolutionDateMonth_" + index).val(problemListEditData.resolutionDateMonth);
        $("#problemListResolutionDateYear_" + index).val(problemListEditData.resolutionDateYear);
        $("#problemListProcedureDateDay_" + index).val(problemListEditData.procedureDateDay);
        $("#problemListProcedureDateMonth_" + index).val(problemListEditData.procedureDateMonth);
        $("#problemListProcedureDateYear_" + index).val(problemListEditData.procedureDateYear);
        $("#problemListLifeStageYear_" + index).val(problemListEditData.lifeStageYear);
        $("#problemListLifeStageUnit_" + index).val(problemListEditData.lifeStageUnit);
        $("#problemListLifeStageText_" + index).val(problemListEditData.lifeStageText);
        $("#problemListLifeNote_" + index).val(problemListEditData.note); 
    }

    function changeClass(element, readOnly) {
        if (readOnly) {
            $(element).removeClass("input-edit");
            $(element).addClass("input-read-only");
        } else {
            $(element).removeClass("input-read-only");
            $(element).addClass("input-edit");
        }
    }

    function changeEditMode(index) {
        $("#div-problem-list").find(':input').each(function () {
            var elementId = $(this).attr("id");
            if (!(elementId == null || elementId == "")) {
                var readOnly = true;
                var searchString = "_" + index;
                if (elementId.slice(-1 * searchString.length) === searchString) {
                    readOnly = false;
                }

                if (elementId.slice(-4) === "_Add") {
                    if (index >= 0) {
                        readOnly = true;
                    } else {
                        readOnly = false;
                    }
                }

                switch (this.type) {
                    case "text":
                    case "textarea":
                        if (elementId.indexOf("problemListLifeStageText_") >= 0) {
                            $(this).attr("readonly", true);
                        } else {
                            $(this).attr("readonly", readOnly);
                        }
                        changeClass(this, readOnly);
                        break;
                    case "checkbox":                        
                        if (elementId.indexOf("problemListCol") >= 0) {
                            $(this).attr("disabled", false);
                        } else {
                            $(this).attr("disabled", readOnly);
                        }
                        changeClass(this, readOnly);
                        break;
                    case "select-one":
                        $(this).attr("disabled", readOnly);
                        changeClass(this, readOnly);
                        break;
                    case "password":
                    case "file":
                    case "select-multiple":
                    case "date":
                    case "number":
                    case "tel":
                    case "email":
                        break;
                }

            }
        });

        var CPPProblemListVMCount = $("#CPPProblemListVMCount").val();
        for (i = 0; i < CPPProblemListVMCount; i++) {
            var editSpan = $("#problemListEditSpan_" + i);
            var textSpan = $("#problemListTextSpan_" + i);
            var saveSpan = $("#problemListSaveSpan_" + i);
            var icd10LinkSpan = $("#problemListICD10LinkSpan_" + i);
            var icd10TextSpan = $("#problemListICD10TextSpan_" + i);

            if (index >= 0) {
                if (i == index) {
                    editSpan.hide();
                    textSpan.hide();
                    saveSpan.show();
                    icd10LinkSpan.show();
                    icd10TextSpan.hide();
                } else {
                    editSpan.hide();
                    textSpan.show();
                    saveSpan.hide();
                    icd10LinkSpan.hide();
                    icd10TextSpan.show();
                }
            } else {
                editSpan.show();
                textSpan.hide();
                saveSpan.hide();
                icd10LinkSpan.hide();
                icd10TextSpan.show();
            }
        }

        if (index >= 0) {
            $("#problemListEditSpan_Add").hide();
            $("#problemListTextSpan_Add").show();
            $("#problemListICD10LinkSpan_Add").hide();
            $("#problemListICD10TextSpan_Add").show();
        } else {
            $("#problemListEditSpan_Add").show();
            $("#problemListTextSpan_Add").hide();
            $("#problemListICD10LinkSpan_Add").show();
            $("#problemListICD10TextSpan_Add").hide();
        }

        return false;
    }
</script>

<div class="row" id="div-problem-list">
    <div class="col-md-12 pre-scrollable" style="padding: 0;">
        <input type="hidden" id="CPPProblemListVMCount" value="@Model.VP_CPP_Problem_List_VM.Count" />
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 24px;">Show @*in Summary*@</th>
                    <th style="width: 32px;">Position</th>
                    <th>Diagnostic Code <input type="checkbox" id="problemListCol10Visible" @(Model.CPPVisibleField.Col10Visible ? "checked" : string.Empty) /></th>
                    <th>Diagnosis <input type="checkbox" id="problemListCol1Visible" @(Model.CPPVisibleField.Col1Visible ? "checked" : string.Empty) /></th>
                    <th>Procedure <input type="checkbox" id="problemListCol2Visible" @(Model.CPPVisibleField.Col2Visible ? "checked" : string.Empty) /></th>
                    <th>Problem Description <input type="checkbox" id="problemListCol3Visible" @(Model.CPPVisibleField.Col3Visible ? "checked" : string.Empty) /></th>
                    <th style="width: 116px;">Problem Status <input type="checkbox" id="problemListCol4Visible" @(Model.CPPVisibleField.Col4Visible ? "checked" : string.Empty) /></th>
                    <th style="width: 412px;">
                        <span style="margin-right: 12px;">Date of Onset <input type="checkbox" id="problemListCol5Visible" @(Model.CPPVisibleField.Col5Visible ? "checked" : string.Empty) /></span>
                        <span style="margin-right: 12px;">Resolution Date <input type="checkbox" id="problemListCol6Visible" @(Model.CPPVisibleField.Col6Visible ? "checked" : string.Empty) /></span>
                        <span style="margin-right: 12px;">Procedure Date <input type="checkbox" id="problemListCol7Visible" @(Model.CPPVisibleField.Col7Visible ? "checked" : string.Empty) /></span>
                        <span>Life Stage <input type="checkbox" id="problemListCol8Visible" @(Model.CPPVisibleField.Col8Visible ? "checked" : string.Empty) /></span>
                    </th>
                    <th>Notes <input type="checkbox" id="problemListCol9Visible" @(Model.CPPVisibleField.Col9Visible ? "checked" : string.Empty) /></th>
                    <th style="width: 32px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @for (int i = 0; i < @Model.VP_CPP_Problem_List_VM.Count; i++)
                {
                    string problemListLifeStageYear = Model.VP_CPP_Problem_List_VM[i].Years == 0 ? string.Empty : Model.VP_CPP_Problem_List_VM[i].Years.ToString();
                    <tr class="tr-row">
                        <td>
                            <input type="hidden" id="problemListDataId_@(i)" value="@Model.VP_CPP_Problem_List_VM[i].Id" />
                            <input type="checkbox" id="problemListShow_@(i)" @(Model.VP_CPP_Problem_List_VM[i].visible ? "checked" : string.Empty)/> 
                        </td>
                        <td align="left"><input type="text" id="problemListPosition_@(i)" class="input-read-only" style="width: 32px;" value="@Model.VP_CPP_Problem_List_VM[i].Position" /></td>
                        <td align="right">
                            <div>Code System: <input type="text" id="problemListCodingSystem_@(i)" class="input-read-only" style="width: 56px;" value="@Model.VP_CPP_Problem_List_VM[i].CodingSystem" /></div>
                            <br />
                            <div style="margin-top: -12px;">Code: <input type="text" id="problemListDiagnosticCode_@(i)" class="input-read-only" style="width: 56px;" value="@Model.VP_CPP_Problem_List_VM[i].DiagnosticCode" /></div>
                            <br />
                            <div>
                                <span id="problemListICD10LinkSpan_@i" style="display: none;">
                                    <a data-url="VP/VP/Search_ICD10?CntrlCode=&CntrlName=" href="#" onclick="return problemListICD10Clicked(@i, this);" class="btn btn-default btn-xs">ICD10</a>
                                </span>
                                <span id="problemListICD10TextSpan_@i">ICD10</span>
                            </div>
                        </td>
                        <td align="left">
                            <textarea id="problemListDiagnosticDescription_@(i)" rows="4" class="input-read-only">@Model.VP_CPP_Problem_List_VM[i].DiagnosticDescription</textarea> 
                        </td>
                        <td align="left"><textarea id="problemListProcedure_@(i)" rows="4" class="input-read-only">@Model.VP_CPP_Problem_List_VM[i].Proc_Interv</textarea></td>
                        <td align="left"><textarea id="problemListDescription_@(i)" rows="4" class="input-read-only">@Model.VP_CPP_Problem_List_VM[i].Problem_Description</textarea></td>
                        <td align="left">
                            @Html.DropDownList("problemListStatus_" + i.ToString(), new SelectList(Model.ActiveList, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].Problem_Status), "Select", new { @class = "input-read-only", @style = "width: 72px;" })
                        </td>
                        <td align="left">
                            <div class="row">
                                <div class="col-sm-3">Date of Onset</div>
                                <div class="col-sm-9">
                                    @Html.DropDownList("problemListDateOnsetDay_" + i.ToString(), new SelectList(Model.Days, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Day), "Day", new {@class = "input-read-only", @style = "width: 56px;" })
                                    @Html.DropDownList("problemListDateOnsetMonth_" + i.ToString(), new SelectList(Model.Months, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Month), "Month", new { @class = "input-read-only", @style = "width: 64px;" })
                                    @Html.DropDownList("problemListDateOnsetYear_" + i.ToString(), new SelectList(Model.Years, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Year), "Year", new { @class = "input-read-only", @style = "width: 64px;" })
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-3">Resolution Date</div>
                                <div class="col-sm-9">
                                    @Html.DropDownList("problemListResolutionDateDay_" + i.ToString(), new SelectList(Model.Days, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ResolutionDate_Day), "Day", new { @class = "input-read-only", @style = "width: 56px;" })
                                    @Html.DropDownList("problemListResolutionDateMonth_" + i.ToString(), new SelectList(Model.Months, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ResolutionDate_Month), "Month", new { @class = "input-read-only", @style = "width: 64px;" })
                                    @Html.DropDownList("problemListResolutionDateYear_" + i.ToString(), new SelectList(Model.Years, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ResolutionDate_Year), "Year", new { @class = "input-read-only", @style = "width: 64px;" })
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-3">Procedure Date</div>
                                <div class="col-sm-9">
                                    @Html.DropDownList("problemListProcedureDateDay_" + i.ToString(), new SelectList(Model.Days, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ProcDate_Day), "Day", new { @class = "input-read-only", @style = "width: 56px;" })
                                    @Html.DropDownList("problemListProcedureDateMonth_" + i.ToString(), new SelectList(Model.Months, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ProcDate_Month), "Month", new { @class = "input-read-only", @style = "width: 64px;" })
                                    @Html.DropDownList("problemListProcedureDateYear_" + i.ToString(), new SelectList(Model.Years, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].ProcDate_Year), "Year", new { @class = "input-read-only", @style = "width: 64px;" })
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-3"> Life Stage</div>
                                <div class="col-sm-9">
                                    <input id="problemListLifeStageYear_@(i)" type="text" class="input-read-only" style="width: 56px;" value="@problemListLifeStageYear" onblur = "OnsetAgeChangedProblemList('@i')" />
                                    @Html.DropDownList("problemListLifeStageUnit_" + i.ToString(), new SelectList(Model.Units, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].Units), "Select", new { @class = "input-read-only", @style = "width: 64px;", onchange = "OnsetAgeChangedProblemList('" + i.ToString() + "')" })
                                    @Html.DropDownList("problemListLifeStageText_" + i.ToString(), new SelectList(Model.LifeStages, "Value", "Text", Model.VP_CPP_Problem_List_VM[i].Life_Stage), "Select", new { @class = "input-read-only", @style = "width: 64px;" })
                                </div>
                            </div>
                        </td>
                        <td align="left"><textarea id="problemListLifeNote_@(i)" rows="4" class="input-read-only">@Model.VP_CPP_Problem_List_VM[i].Notes</textarea></td>
                        <td align="left">
                            <span id="problemListEditSpan_@i">
                                <a href="" onclick="return problemListEditButtonClicked(@i);">Edit</a>
                                <br />
                                <a href="" onclick="return cppButtonDeleteClicked(this);" data-name="@Model.VP_CPP_Problem_List_VM[i].Id" data-patientid="@Model.PatientID" data-cpptype="@(Model.VP_CPP_Problem_List_VM[i].isProblemList ? (int)AwareMD.Cerebrum.Shared.Enums.CPP_Categories.PROBLEMLIST : (int)AwareMD.Cerebrum.Shared.Enums.CPP_Categories.PASTHEALTH)" data-rowid="@Model.VP_CPP_Problem_List_VM[i].Id">Delete</a>
                            </span>
                            <span id="problemListTextSpan_@i" style="display: none;">
                                Edit
                                <br />
                                Delete
                            </span>
                            <span id="problemListSaveSpan_@i" style="display: none;">
                                <a href="" onclick="return problemListSaveButtonClicked('@i');">Save</a>
                                <br />
                                <a href="" onclick="return problemListCancelButtonClicked('@i');">Cancel</a>
                            </span>
                            <br />
                            <div class="CCP">
                                <div class="btn-popover-container">
                                    <button type="button" class="btn btn-default btn-xs popover-btn" data-original-title="" title="" data-toggle="popover">
                                        <span class="glyphicon glyphicon-option-vertical" style="color: #337ab7;"></span>
                                    </button>
                                    <div class="btn-popover-title">Information</div>
                                    <div class="btn-popover-content">
                                        <ul class="ul-popover-list">
                                            @*<li>Added: @(Model.VP_CPP_Problem_List_VM[i].AddDate == null ? string.Empty : Model.VP_CPP_Problem_List_VM[i].AddDate.Value.ToString("MM/dd/yyyy"))</li>*@
                                            <li>Last Updated: @(Model.VP_CPP_Problem_List_VM[i].AddedByName.Trim().Trim(',')) @(Model.VP_CPP_Problem_List_VM[i].AddDate == null ? string.Empty : Model.VP_CPP_Problem_List_VM[i].AddDate.Value.ToString("MM/dd/yyyy"))</li>
                                            <li>Appointment Date: @(Model.VP_CPP_Problem_List_VM[i].AppointmentDate == null ? string.Empty : Model.VP_CPP_Problem_List_VM[i].AppointmentDate.Value.ToString("MM/dd/yyyy"))</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="btn-popover-container">
                                    <button type="button" class="btn btn-default btn-xs popover-btn" data-original-title="" title="" data-toggle="popover">
                                        <span class="glyphicon glyphicon-th-list" style="color: #337ab7;"></span>
                                    </button>
                                    <div class="btn-popover-title">Residual Information</div>
                                    <div class="btn-popover-content">@Html.Raw(Model.VP_CPP_Problem_List_VM[i].ResidualInformation.Replace("\n", "<br />"))</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
    <div class="col-md-12" style="margin-top: 16px; padding: 0;">
        <table class="table">
            <tr class="tr-row">
                <td style="width: 24px;">
                    <input type="hidden" id="problemListDataId_Add" value="0" />
                    <input type="checkbox" id="problemListShow_Add" checked /> 
                </td>
                <td align="left" style="width: 32px;"><input type="text" id="problemListPosition_Add" style="width: 32px;" /></td>
                <td align="right">
                    <div>Code System: <input type="text" id="problemListCodingSystem_Add" class="input-read-only" style="width: 56px;" value="" /></div>
                    <br />
                    <div style="margin-top: -12px;">Code: <input type="text" id="problemListDiagnosticCode_Add" class="input-read-only" style="width: 56px;" value="" /></div>
                    <br />
                    <div>
                        <span id="problemListICD10LinkSpan_Add">
                            <a data-url="VP/VP/Search_ICD10?CntrlCode=&CntrlName=" href="#" onclick="return problemListICD10Clicked('Add', this);" class="btn btn-default btn-xs">ICD10</a>
                        </span>
                        <span id="problemListICD10TextSpan_Add" style="display: none;">ICD10</span>
                    </div>
                </td>
                <td align="left">
                    <textarea id="problemListDiagnosticDescription_Add" rows="4"></textarea> 
                </td>
                <td align="left"><textarea id="problemListProcedure_Add" rows="4"></textarea></td>
                <td align="left"><textarea id="problemListDescription_Add" rows="4"></textarea></td>
                <td align="left" style="width: 116px;">
                    @Html.DropDownList("problemListStatus_Add", new SelectList(Model.ActiveList, "Value", "Text"), "Select", new { @style = "width: 72px;" })
                </td>
                <td align="left" style="width: 412px;">
                    <div class="row">
                        <div class="col-sm-3">Date of Onset</div>
                        <div class="col-sm-9">
                            @Html.DropDownList("problemListDateOnsetDay_Add", new SelectList(Model.Days, "Value", "Text"), "Day", new { @style = "width: 56px;" })
                            @Html.DropDownList("problemListDateOnsetMonth_Add", new SelectList(Model.Months, "Value", "Text"), "Month", new { @style = "width: 64px;" })
                            @Html.DropDownList("problemListDateOnsetYear_Add", new SelectList(Model.Years, "Value", "Text"), "Year", new { @style = "width: 64px;" })
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">Resolution Date</div>
                        <div class="col-sm-9">
                            @Html.DropDownList("problemListResolutionDateDay_Add", new SelectList(Model.Days, "Value", "Text"), "Day", new { @style = "width: 56px;" })
                            @Html.DropDownList("problemListResolutionDateMonth_Add", new SelectList(Model.Months, "Value", "Text"), "Month", new { @style = "width: 64px;" })
                            @Html.DropDownList("problemListResolutionDateYear_Add", new SelectList(Model.Years, "Value", "Text"), "Year", new { @style = "width: 64px;" })
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">Procedure Date</div>
                        <div class="col-sm-9">
                            @Html.DropDownList("problemListProcedureDateDay_Add", new SelectList(Model.Days, "Value", "Text"), "Day", new { @style = "width: 56px;" })
                            @Html.DropDownList("problemListProcedureDateMonth_Add", new SelectList(Model.Months, "Value", "Text"), "Month", new { @style = "width: 64px;" })
                            @Html.DropDownList("problemListProcedureDateYear_Add", new SelectList(Model.Years, "Value", "Text"), "Year", new { @style = "width: 64px;" })
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3"> Life Stage</div>
                        <div class="col-sm-9">
                            <input id="problemListLifeStageYear_Add" type="text" style="width: 56px;" value="" onblur = "OnsetAgeChangedProblemList('Add')" />
                            @Html.DropDownList("problemListLifeStageUnit_Add", new SelectList(Model.Units, "Value", "Text"), "Select", new { @style = "width: 64px;", onchange = "OnsetAgeChangedProblemList('Add')" })
                            @Html.DropDownList("problemListLifeStageText_Add", new SelectList(Model.LifeStages, "Value", "Text"), "Select", new { @class = "input-read-only", @style = "width: 64px;" })
                        </div>
                    </div>
                </td>
                <td align="left"><textarea id="problemListLifeNote_Add" rows="4"></textarea></td>
                <td align="left" style="width: 32px;">
                    <span id="problemListEditSpan_Add">
                        <a href="" onclick="return problemListSaveButtonClicked('Add');">Add New</a>
                    </span>
                    <span id="problemListTextSpan_Add" style="display: none;">
                        Add New
                    </span>
                </td>
            </tr>
        </table>
    </div>
</div>
