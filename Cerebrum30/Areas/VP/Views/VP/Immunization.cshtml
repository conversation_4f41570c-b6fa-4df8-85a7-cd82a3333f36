@model Cerebrum.ViewModels.VP.VMImmunization
@{
    ViewBag.ModuleName = "CPP Immunization";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.5/handlebars.js"></script>
<style>
    textarea {
        white-space: normal;
        text-align: justify;
        -moz-text-align-last: center;
        text-align-last: center;
    }

    .divInfo {
        background-color: red;
        width: 215px;
        height: 96px;
    }

    .PowderBlue {
        background-color: powderblue;
    }

    .Pink {
        background-color: pink;
    }

    .LawnGreen {
        background-color: lawngreen;
    }

    .Yellow {
        background-color: yellow;
    }

    .Red {
        background-color: red;
    }

    .DarkGray {
        background-color: darkgray;
    }

    .deepskyblue {
        background-color: deepskyblue;
    }

    .tablepadded td {
        padding: 2px 12px;
        font-weight: bold;
        height: 30px;

    }

    .btn-popover-container .btn-popover-title,
    .btn-popover-container .btn-popover-content,
    .btn-modal-container .btn-modal-content {
        display: none;
    }

    .btn-popover-container {
    }

    .popover-content {
        /*height: 400px;*/
    }

    ol li {
        list-style: none;
    }

    .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #e8f2f8;
    }

    .sm-txt-box{
        width:45px;
    }

    .xs-txt-box{
        width:35px;
    }

    .ul_immunization li {
        list-style: none;     
        margin-right: 15px;  
    }

    .tbl-immun-col1 {
        width:130px;
    }
    .td-mid {
        min-width:90px;
    }
    .btn-info2 {
        cursor: pointer
    }
    input[type="text"] {
        padding-left: 3px !important;
    }
    
</style>

@section scripts {

    <script>

        $(document).ready(function () {

           

            $(document).on('change', '#Agecategory', function (e) {

                e.preventDefault();
                var drp = $(this);
                
                if(drp.val() == 'Routine Infants &amp; Children')
                {
                    $('.age-dsc').text("Month(s)")
                }
                else
                {
                    $('.age-dsc').text("Year(s)")
                }

            });


            $(document).on('click', '#btn-save-imm-billing', function (e) {

                e.preventDefault();

                $.ajax({

                    //type: "POST",
                    data: { Code: $("#txt-immun-code").val(), PatientID : $("#PatientID").val() },
                    url: "VP/VP/SaveImmunBilling",
                    success: function (data) {

                        $("#span-immbill-result").html(data.Message);
                        $("#txt-immun-code").val();
                    }
                });

            });

            $(document).on('click', '.tr-immun-code', function (e) {

                e.preventDefault();
                var self = $(this);
                var code = self.data("code");
                //var id = self.data("id");
                $("#txt-immun-code").val(code);

            });

            var LoadExclusionCodes = function () {
                $("#div-immun-exclusion").html('<img src="../../Content/fancybox_loading.gif" />');
                $.ajax({

                    //type: "POST",
                    //data: { patientID:@Model.PatientID },
                    url: "VP/VP/ImmunExclusionCodes",
                    success: function (data) {

                        $("#div-immun-exclusion").html(data);
                        // $("#dynamic-content-button-exc").show();
                    }
                });
            };

            var LoadBiilingCodes = function () {

                $("#div-immun-billng").html('<img src="../../Content/fancybox_loading.gif" />');
                $.ajax({

                    //type: "POST",
                    //data: { patientID:@Model.PatientID },
                    url: "VP/VP/ImmunBillingCode",
                    success: function (data) {

                        $("#div-immun-billng").html(data);
                        $("#dynamic-content-button-bill").show();
                    }
                });
            };

            var LoadImmunData = function () {
                $("#div-immun-data").html('<img src="../../Content/fancybox_loading.gif" />');
                $.ajax({

                    //type: "POST",
                    data: { patientID:@Model.PatientID },
                    url: "VP/VP/Immunization_Data",
                    success: function (data) {

                        $("#div-immun-data").html(data);
                    }
                });
            };

            var LoadPatientName = function () {
                $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
                ajaxCall("../../Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
                    // $("#div-pat-name").html(data);
                    $.when( $('#div-pat-name').html(data)).then(function( data, textStatus, jqXHR) {
                        var clone = $('._toClone17a').clone();
                        clone.show().appendTo($('#_placeHolder17'));
                    });
                });
            };

            var LoadData = function () {

                var typeid = $('.proc-type').val();
                if(!typeid ) //nothign selected
                {
                    $(".target-div").html('');
                    return ;
                }
                $.ajax({

                    method: 'POST',
                    url: "VP/VP/GetImmunDetails",
                    data: { recID: typeid },

                    success: function (data) {

                        if (data.Errored == "1") {

                            //showNotificationMessage('error', 'Could not get Immun. Details');
                        }

                        else {

                            $(".target-div").html('<img src="../../Content/fancybox_loading.gif" />');
                            $(".target-div").html( GetHTML( data.Message ));

                        }
                    },

                    error: function (xhr, thrownError) {

                        //alert("Error while tryng to call  'VP/GetImmunDetails'  " + xhr.status + " " + thrownError);
                    },

                    complete : function () {
                        $("#ajax-loader").hide();
                    }
                });


            };

            var GetHTML = function(data){
                var isImmun = "No";
                if(data.IsImmunization != null && data.IsImmunization == true)
                {
                    isImmun = "Yes";
                }
                var context =
                        {
                            GenderStr: data.GenderStr ,
                            //Age : data.Age,
                            //OperatorStr: data.OperatorStr,
                            AgeFrom : data.AgeFrom,
                            AgeTo : data.AgeTo,
                            AgeCategory: data.Agecategory,
                            DateFrom :  data.DateFromStr,
                            DateTo  : data.DateToStr,
                            Period : data.Period,
                            IsImmunization: isImmun
                        };
                var source = $("#resultTemplate").html();
                var template = Handlebars.compile(source);
                var html = template(context);
                return html;
            };

            //LoadBiilingCodes();
            // LoadExclusionCodes();
            LoadData();
            LoadImmunData();


            $(document).on('click', '#hlAdminister', function (e) {

                //$("#ajax-loader").show();
                e.preventDefault();

                $.ajax({

                    method: 'POST',
                    url: "VP/VP/ImmunizationAdministerJSON",
                    data: {PatientId : @Model.PatientID, VP_CPP_ImmunizationTypeId : $("#SelectedType").val()  },
                    success: function (data) {

                        if (data.Errored == "1") {

                            var cList = $('#mylist');
                            $.each(data.Errors, function (i) {
                                var li = $('<li/>')
                                    .addClass('ui-menu-item')
                                    .attr('role', 'menuitem')
                                    .text(data.Errors[i])
                                    .appendTo(cList);

                            });
                        }
                        else {

                            if (opener != null && opener.LoadCPP) {
                                opener.LoadCPP();
                            }
                            LoadImmunData();
                            //window.location.href = window.location.href;
                        }

                        // $('#btn-save').removeAttr("disabled");
                    },

                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $(document).on('click', '#btn-administer-procedure', function (e) {
                               
                e.preventDefault();

                if($("#SelectedType").val() > 0)
                {
                    var modalId = "#administer-modal-container";
                    var modalContentId = "#administer-modal-container-ct";
                    $("#ajax-loader").show();
                    $.ajax({

                        method: 'GET',
                        url: "VP/VP/AdministerImmunization",
                        data: {patientId : @Model.PatientID, procedureTypeId : $("#SelectedType").val()  },
                        complete: function () {
                            $("#ajax-loader").hide();
                        },
                        success: function (data) {
                            console.log('administer data: '+ data);

                            loadCBModal(modalId, data, "modal-md");
                            //try{
                            //    $("#administer-modal-content").html(data);
                            //    showModal('#administer-modal-container');
                            //}catch(ex){
                            //}
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            checkAjaxError(jqXHR);                       
                        }
                    });
                }
                else{

                    showMessageModal("error", "Please select a procedure type.");
                }
            });

            $(document).on('submit', '#frm-administer-procedure', function (e) {
                              
                e.preventDefault();
                var modalId = "#administer-modal-container";
                var modalContentId = "#administer-modal-container-ct";
                $('#ajax-loader').show();
                $.ajax({
                    url: this.action,
                    type: this.method,
                    data: $(this).serialize(),
                    complete: function () {
                        $("#ajax-loader").hide();
                    },
                    success: function (result) {
                        if (result.success) {      
                            hideModal(modalId);
                            showNotificationMessage("success", result.message);
                            try{
                                if (opener != null && opener.LoadCPP) {
                                    opener.LoadCPP();
                                }
                            }
                            catch(err)
                            {

                            }
                            LoadImmunData();
                        } // end if success
                        else {
                            //$("#administer-modal-content").html(result);
                            $(modalContentId).html(result);
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        checkAjaxError(jqXHR);
                    }
                });               
            });

            $(document).on('click', '#btn-edit-procedure', function (e) {

                e.preventDefault();

                var typeID = $(this).data("immunizationTypeID");
                var entryid = $(this).data("id");

                var url = "VP/VP/ImmunizationAdminister?patientID=@Model.PatientID&immunizationTypeID=" + typeID + "&id=" + entryid;
                //console.log('here');
                //$("#immun-modal-content").html('');
                $("#immun-modal-content").load(url, function () {

                    $("#immun-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
                    }, 'show');
                    // $("#ajax-loader").hide();
                });
            });

            $(document).on('click', '.btn-close-status-change', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

            $(document).on('click', '.btn-popover-close', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });


            $(document).on('click', '#hlAddNew', function (e) {                
                e.preventDefault();
                var url = "VP/VP/ImmunizationAdd?patientID=@Model.PatientID";
               
                $("#immun-modal-content").load(url, function (d) {

                    console.log(d);
                    $("#immun-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static',                     
                    }, 'show');
                });              


                $('#immun-modal-container').on('show.bs.modal', function (e) {  
                   // $('#immun-modal-container .modal-title').html('Add New');
                })

                $('#immun-modal-container').on('hide.bs.modal', function (e) {                   
                  //  $('#immun-modal-container .modal-title').html('');
                })



            });





            $(document).on('click', '#hlRecall', function (e) {
                e.preventDefault();
                $.ajax({
                    method: 'GET',
                    url: "VP/VP/GenerateRecall",
                    data: {},
                    success: function (data) {
                        if (data.Errored == "1") {
                            showNotificationMessage('error', 'Error generating Recall List');
                        }
                        else {
                            window.location.href = window.location.href;
                            showNotificationMessage('success', 'Generated Recall List');
                        }
                    },

                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/GenerateRecall'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('[data-toggle="tooltip"]').tooltip({ animation: false });

            LoadPatientName();

            $(document).on('click', '#btn-add', function (e) {
                e.preventDefault();
                $.ajax({
                    method: 'POST',
                    url: "VP/VP/AddImmunization",
                    data: {
                        Name        : $('#Name').val(),
                        Agecategory : $('#Agecategory').val(),
                        Period      : $('#Period').val(),
                        Gender      : $('#Gender').val(),
                        Age         : $('#Age').val(),
                        Operator    : $('#Operator').val(),
                        DateFrom    : $('#DateFrom').val(),
                        DateTo      : $('#DateTo').val(),
                        AgeFrom     : $('#AgeFrom').val(),
                        AgeTo       : $('#AgeTo').val(),
                        Code        : $('#Code').val()

                    },

                    success: function (data) {
                        if (data.Errored == "1") {
                            var cList = $('#mylist');
                            $.each(data.Errors, function (i) {
                                var li = $('<li/>')
                                    .addClass('ui-menu-item')
                                    .attr('role', 'menuitem')
                                    .text(data.Errors[i])
                                    .appendTo(cList);
                            });
                        }
                        else {
                            window.location.href = window.location.href;
                        }
                    },

                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $(document).on('click', '#btn-history', function (e) {
                e.preventDefault();
                var rowid = $(this).data("rowid");
                $("#ajax-loader").show();

                $("#immun-modal-content").load("VP/VP/ImmunizationHistory?PatientID=@Model.PatientID&RowID="+ rowid, function () {
                    $("#immun-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
                    }, 'show');
                    $("#ajax-loader").hide();
                });
            });

            $(document).on('click', '#btn-cancel', function (e) {
                e.preventDefault();
                $("#immun-modal-container").modal("hide");
            });

            $(document).on('click', '.btn-cancel-model', function (e) {
                e.preventDefault();
                $('#immun-modal-container').modal('hide');
            });


            $(document).on('click','#btn-update-recall', function(e){
                e.preventDefault();
                var url = $(this).data("url");

                if($("#ddRecallDoctor").val() > 0)
                {
                    $.ajax({
                        method: 'POST',
                        url: url ,
                        data: {ImmunType : $("#ddRecallList").val(), doctorID:$("#ddRecallDoctor").val()},
                        success: function (data) {
                            if (data.Errored == "1") {
                                showNotificationMessage('error', 'Changes not saved');
                            }
                            else {
                                //console.log("Recall List updated");
                                $("#span-recall-error").text(data.Message + " Patients Added");

                                try{
                                    if(data.Message > 0)
                                    {
                                        LoadImmunData();
                                    }
                                }
                                catch(ex)
                                {

                                }
                            }
                        },
                        error: function (xhr, thrownError) {
                            alert("Error while tryng to call  'VP/Update Recall'  " + xhr.status);
                            //alert("Error while tryng to call  'VP/Update Recall'  " + xhr.status + " " + thrownError);
                        },
                        complete : function () {
                            $("#ajax-loader").hide();
                        }
                    });
                }
                
                else{
                    showMessageModal("error", "Please select a doctor to generate the recall list.");
                }
                

                

            });

            $(document).on('click', '#hlupdatestatus', function (e) {
                e.preventDefault();
                $("#ajax-loader").show();
                var rowid = $(this).data("rowid");
                $.ajax({
                    method: 'POST',
                    url: "VP/VP/UpdateImmunizationStatus",
                    data: { recID: rowid, statusID: 1  },
                    success: function (data) {
                        if (data.Errored == "1") {
                            showNotificationMessage('error', 'Changes not saved');
                        }
                        else {
                            window.location.href = window.location.href;
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
                    },
                    complete : function () {
                        $("#ajax-loader").hide();
                    }
                });
            });

            $(document).on('click', '#tbl_immun .btn-info2', function (e) {
                e.preventDefault();
                $("#ajax-loader").show();
                var id = $(this).data("id");              
                var url = "VP/VP/ImmunizationAdminister?patientID=@Model.PatientID&immunizationTypeID=0&id=" + id;
                $("#immun-modal-content").load(url, function () {
                    $("#immun-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
                    }, 'show');
                    $("#ajax-loader").hide();
                });
            });

            //$(document).on('change', '.sel-status', function (e) {

            //    //console.log('here');

            //    e.preventDefault();

            //    $("#ajax-loader").show();

            //    var rowid = $(this).data("rowid");
            //    var recallid = $(this).data("recallid");

            //    $.ajax({

            //        method: 'POST',
            //        url: "VP/VP/UpdateImmunizationStatus",
            //        data: { recID: rowid,RecallID:recallid, statusID: $(this).val() , DateServiced:$("#txtDateServiced_"+recallid).val() },

            //        success: function (data) {

            //            if (data.Errored == "1") {

            //                showNotificationMessage('error', 'Changes not saved');
            //            }

            //            else {

            //                // console.log(data);
            //                if(opener!=null && opener.UpdateServiceDate ){
            //                    opener.UpdateServiceDate(data);
            //                }
            //                window.location.href = window.location.href;
            //            }
            //        },

            //        error: function (xhr, thrownError) {

            //            alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
            //        },

            //        complete : function () {
            //            $("#ajax-loader").hide();
            //        }
            //    });

            //});


            $(document).on('click', '#btn-edit-proc-save', function (e){
                e.preventDefault();
                $("#ajax-loader").show();
                var url = "VP/VP/SaveProcedure";
                $.ajax({
                    method: 'POST',
                    url: url,
                    data:  $("#frm-edit-proc").serialize(),
                    success: function (data) {
                        if (data.Errored == "1") {
                            showNotificationMessage('error', 'Changes not saved');
                        }
                        else {
                            //refresh the list
                            $("#SelectedType").get(0).selectedIndex= 0;
                            $(".target-div").html('');
                            $('#immun-modal-container').modal('hide');
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/SaveProcedure'  " + xhr.status + " " + thrownError);
                    },
                    complete : function () {
                        $("#ajax-loader").hide();
                    }
                });

            });

            $(document).on('click', '#btn-edit-proc-type', function (e){
                e.preventDefault();

                if($(".proc-type").val() > 0)
                {
                    $("#ajax-loader").show();
                    var id = $(this).data("id");
                    var url = "VP/VP/EditProcedure?ProcID=" + $(".proc-type").val();

                    $("#immun-modal-content").load(url, function () {
                        $("#immun-modal-container").modal({
                            keyboard: false,
                            backdrop: 'static'
                        }, 'show');

                        $("#ajax-loader").hide();
                    });
                }
                else{
                    showMessageModal("error", "Please select procedure type to edit.");
                }
            });

            $(document).on('click', '.btn-add-immun-log', function (e) {

                //console.log('here');

                e.preventDefault();
                $(".immunization-edit-status-msg").html('');

                $("#ajax-loader").show();

                var rowid = $(this).data("rowid");
                var recallid = $(this).data("recallid");
                var msgDiv = $("#edit-error-msg-"+recallid);

                $.ajax({
                    method: 'POST',
                    url: "VP/VP/UpdateImmunizationStatus",
                    data: { recID: rowid,RecallID:recallid,
                        statusID: $("#sel-status_"+ recallid).val() ,
                        DateServiced:$("#txtDateServiced_"+recallid).val(),
                        DateServicedDay:$("#txtDateServicedDay_"+recallid).val(),
                        DateServicedMonth:$("#txtDateServicedMonth_"+recallid).val(),
                        DateServicedYear:$("#txtDateServicedYear_"+recallid).val(),
                    },
                    success: function (data) {
                        if (data.Errored == "1") {
                            //showNotificationMessage('error', 'Changes not saved');
                            msgDiv.html(data.ErrorMessage);
                        }

                        else {
                            //console.log(data);
                            //if( opener != null &&   opener.LoadCPP ){
                            //    opener.LoadCPP();
                            //}
                            //if(opener!=null &&  opener.UpdateRecallRow ){
                            //    opener.UpdateRecallRow(data);
                            //}

                            try{
                                if( opener != null &&   opener.LoadCPP ){
                                    opener.LoadCPP();
                                }
                                if(opener!=null &&  opener.UpdateRecallRow ){
                                    opener.UpdateRecallRow(data);
                                }
                            }
                            catch(err)
                            {

                            }

                            LoadImmunData();
                            //window.location.href = window.location.href;
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
                    },
                    complete : function () {
                        $("#ajax-loader").hide();
                    }
                });

            });

            $(document).on('change', '.proc-type', function (e) {
                //e.preventDefault();
                var typeid = $('.proc-type').val();
                if(!typeid ) //nothign selected
                {
                    $(".target-div").html('');
                    return ;
                }
                $.ajax({
                    method: 'POST',
                    url: "VP/VP/GetImmunDetails",
                    data: { recID: typeid },
                    success: function (data) {
                        if (data.Errored == "1") {
                            showNotificationMessage('error', 'Could not get Immun. Details');
                        }
                        else {
                            $(".target-div").html('<img src="../../Content/fancybox_loading.gif" />');
                            $(".target-div").html( GetHTML( data.Message ));
                            // console.log(data);
                            //window.location.href = window.location.href;
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/GetImmunDetails'  " + xhr.status + " " + thrownError);
                    },
                    complete : function () {
                        $("#ajax-loader").hide();
                    }
                });

            });

            $(document).on('click', '#btn-save-proc-edit', function (e) {
                //$('#btn-save-proc-edit').click(function (e) {

                e.preventDefault();
                //$(this).attr("disabled", true);
                $.ajax({
                    method: 'POST',
                    url: "VP/VP/ImmunizationAdministerJSON",
                    data: $("#frm-immunization").serialize(),
                    success: function (data) {
                        if (data.Errored == "1") {
                            var cList = $('#mylist');
                            $.each(data.Errors, function (i) {
                                var li = $('<li/>')
                                    .addClass('ui-menu-item')
                                    .attr('role', 'menuitem')
                                    .text(data.Errors[i])
                                    .appendTo(cList);

                            });
                        }
                        else {

                            if (opener != null && opener.LoadCPP) {
                                opener.LoadCPP();
                            }
                            LoadImmunData();
                            $("#immun-modal-container").modal("hide");
                        }
                        // $('#btn-save').removeAttr("disabled");
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/AddImmunization'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            @*$('.divInfo').click(function () {

                //e.preventDefault();

                var id = $(this).data("id");

                var url = "VP/VP/ImmunizationAdminister?patientID=@Model.PatientID&immunizationTypeID=0&id=" + id;

                $("#immun-modal-content").load(url, function () {

                    $("#immun-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
                    }, 'show');

                });
            });*@
        });
    </script>
}
 
<link rel="stylesheet" href="~/Areas/Schedule/Content/shared-styles.css" />

@*<div id="mmm">
    <a href="javascript:void(0)" class="closebtn" onclick="closeSidebar()">&times;</a>
    @{Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = Model.PatientID }); }
</div>*@




@Html.HiddenFor(x => (object)x.PatientID)
<script id="resultTemplate" type="text/x-handlebars-template">
        <div class="col-md-4">
            <div class="panel panel-info ">
                <div class="panel-heading">
                    <div class="pull-left">
                        <label class="panel-title custom-label">Procedure Details</label>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="panel-body">
                    <div class="form-group-sm">
                        <label><b>Gender:</b></label>
                        <span>{{GenderStr}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Age:</b></label>
                        <span>{{AgeFrom}}--{{AgeTo}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Age Category:</b></label>
                        <span>{{AgeCategory}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Age as Of:</b></label>
                        <span>{{DateFrom}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Frequency:</b></label>
                        <span>{{Period}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Service Cut Off Date:</b></label>
                        <span>{{DateTo}}</span>
                    </div>
                    <div class="form-group-sm">
                        <label><b>Immunization:</b></label>
                        <span>{{IsImmunization}}</span>
                    </div>
                </div>
            </div>

        @*</div>*@
        </div>
</script>

<div id="immun-modal-container" class="modal fade" tabindex="-1" role="dialog" >
    <div class="modal-dialog modal-md" role="document" >@*style="width:auto"*@
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"></h4>
            </div>

            <div class="modal-body ">
                @*<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>*@
                <div id="immun-modal-content" @*style="height:147px"*@></div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->


@*<div id="administer-modal-container" class="modal fade in" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md" role="document">       
    <div class="modal-content">        
            <div id="administer-modal-content"></div>        
    </div><!-- /.modal-content -->
</div><!-- /.modal-dialog -->
</div><!-- /.modal -->*@

<div id="div-pat-name"></div>



<div class="immunization">&nbsp;</div>
@*<div class="row">&nbsp;</div>*@
@* <div class="text-center"> *@
     @*<div class="margin-left-15 float_r _toClone17a" style="white-space: nowrap; display:none">
        <div id="sidebar-right-open"><div class="c-pointer" onclick="openSidebar()"><span class="glyphicon glyphicon-list f25"></span> <span>e-Chart</span></div></div>
    </div>*@
     @*<div class="text-left">

        <div class="margin-left-15 float_r" style="white-space: nowrap">
            <div id="sidebar-right-open"><div class="c-pointer" onclick="openSidebar()"><span class="glyphicon glyphicon-list f25"></span> <span>e-Chart</span></div></div>
        </div>

    </div>*@
     @*<div class="row">
        <div class="pull-left">
            <h3><b>2.2.1.2.3</b></h3>
        </div>
        <div class="">
            <h3><b>IMMUNIZATION AND PREVENTATIVE CARE</b></h3>
        </div>
    </div>*@
    <div class="row">
        <div class="col-md-12">
            <div class="float_l">
                <div class="float_l" style="padding: 6px 5px 0px 0px">Procedure Type</div>
                <div class="float_l">
                    @Html.DropDownListFor(model => model.SelectedType, new SelectList(Model.Types, "Id", "Name"), "Choose One", new { @class = "ddl-matching-btn-sm proc-type" })
                </div>
                <div class="float_l" style="margin-left: 3px;">
                    <a class="btn btn-default btn-sm" id="btn-edit-proc-type" href="#">Edit</a>
                </div>
                <div class="float_l" style="margin-left: 3px;">
                    <a href="@Url.Action("GetImmunizationTypesPDF")" target="_blank">
                        Lookup Codes
                    </a>
                </div>
            </div>
        </div>

        <div class="target-div"></div>

    </div>


    <div class="row __00934535" style="margin-top:3px">
        @*<div class="row">&nbsp;</div>*@
        @*<div class="row">*@

        <div class="col-md-8">
            <div class="form-inline">
                <div class="popover-btn form-group">
                    <a id="hlAddNew" href="#" class="btn btn-default btn-sm">Add New</a>
                    @*<a id="hlAdminister" href="#" class="btn btn-default">Administer</a>*@
                    <a id="btn-administer-procedure" href="#" class="btn btn-default btn-sm">Administer</a>
                </div>
                <div class="popover-btn form-group">
                    <div class="btn-popover-container">
                        <span @*data-toggle="tooltip"
                              title="Update Recall List"
                              data-placement="right"*@
                              class="text-danger popover-btn c-pointer btn btn-default btn-sm">Generate Recall List</span>
                        <div class="btn-popover-title"></div>
                        <div class="btn-popover-content">
                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="control-label">Procedure</label>
                                    <div @*class="pull-right"*@>
                                        @Html.DropDownListFor(model => model.SelectedType, new SelectList(Model.Types, "Id", "Name"), new { @id = "ddRecallList", @class = "" }) @*ddl-matching-btn-sm*@
                                    </div>
                                </div>
                            </div>

                            <div class="form-inline">
                                <div class="form-group">
                                    <label class="control-label">Doctor</label>

                                    <div @*class="pull-right"*@>
                                        <div @*class="pull-right"*@>
                                            @Html.DropDownListFor(model => model.DoctorID, new SelectList(Model.Doctors, "Value", "Text"), "Choose One", new { @id = "ddRecallDoctor", @class = "88" }) @*ddl-matching-btn-sm*@
                                        </div>
                                    </div>

                                    <div class="form-inline">
                                        <div class="row @*text-center*@">
                                            <label id="span-recall-error" class="text-danger small"></label>
                                        </div>
                                        <div class="@*row pull-right*@">
                                            <a id="btn-update-recall" class="btn btn-primary btn-sm"
                                               data-url='@Url.Action("UpdateRecallList")'
                                               href="#">Generate</a>
                                            <a href="#" class="btn btn-default btn-sm btn-close-status-change">@*<i style="color:red" class="glyphicon glyphicon-remove"></i>*@Close</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="popover-btn form-group" style="margin-left: -6px">
                    <div class="btn-popover-container">
                        <span id="dynamic-content-button-bill"
                              @*data-toggle="tooltip"
                              title="Billing Codes"
                              data-placement="right"*@
                              class="text-danger popover-btn c-pointer btn btn-default btn-sm">Billing Codes</span>
                        <div class="btn-popover-title">Billing Codes </div>
                        <div class="btn-popover-content">
                            @(await Html.PartialAsync("ImmunBillingCode", (object)(object)Model.BillingCats))
                            @*<div id="div-immun-billng">
                            </div>*@
                        </div>
                    </div>
                </div>

                <div class="popover-btn form-group" style="margin-left: -6px">
                    <div class="btn-popover-container">
                        <span
                              @*data-toggle="tooltip"
                              title="Exclusion Codes "
                              data-placement="right"*@
                              class="text-danger popover-btn c-pointer btn btn-default btn-sm">Exclusion Codes</span>
                        <div class="btn-popover-title ">Exclusion Codes </div>
                        <div class="btn-popover-content">
                            <div>
                                @(await Html.PartialAsync("ImmunExclusionCodes"))
                            </div>
                            @*<div id="div-immun-exclusion">
                            </div>*@
                        </div>
                    </div>
                </div>

                <div class="btn-group" style="margin-left: -6px">
                    <button type="button" class="btn btn-default dropdown-toggle btn-sm"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Reference Material <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a href="https://www.ontario.ca/page/vaccines" target="_blank">Immunization Information</a></li>
                        <li><a href="http://health.gov.on.ca/en/public/programs/cancer/screening/guidelines.aspx" target="_blank">Screening</a></li>
                        <li><a href="http://www.health.gov.on.ca/en/pro/programs/ohip/bulletins/11000/bul11179.pdf" target="_blank">Bulletins</a></li>
                        @*<li role="separator" class="divider"></li>*@
                    </ul>
                </div>

            </div>
        </div>


        <div class="col-md-4 pull-right">
            <table class="tablepadded pull-right">
                <tr>
                    <td style="background-color: powderblue;">
                        To be done
                    </td>
                    <td style="background-color: pink;">
                        Refused
                    </td>
                    <td style="background-color: lawngreen;">
                        Completed
                    </td>
                    @*<td style="background-color: yellow;">
                        Ineligible
                    </td>*@
                    <td style="background-color: red;">
                        Overdue
                    </td>
                    @*<td style="background-color: darkgray;">
                    Excluded
                </td>*@
                    <td style="background-color: deepskyblue;">
                        Tracked
                    </td>
                </tr>
            </table>
        </div> 
 </div>

<div class="row">&nbsp;</div>
<div class="row" id="div-immun-data"></div>

<br />
<br />

