@model  Cerebrum.ViewModels.VP.VMRiskFactor
<script>
    $(document).ready(function () {
        initPopover();
    });

    $('.hl-delete')
      .popover({
          trigger: 'manual',
          html: true,
          placement: 'auto right'
      })

  .click(function (e) {

      $('#ajax-loader').show();

      e.preventDefault();

      var box = $(this);
      var title = "";

      var _patientid = $(this).data("patientid");
      var _cppType = $(this).data("cpptype");
      var _rowid = $(this).data("rowid");
      var _cntrlName = $(this).data("name");

      var data = { patientID: _patientid, cppType: _cppType, rowid: _rowid, cntrlName: _cntrlName };
      url = 'VP/VP/ReasonForDeletion';
      ajaxCall(url, data, false, function (data) {
          box.attr('data-content', data).popover('show');
      });
  });

    function editRiskFactor(element) {
        var _patientid = $(element).data("patientid");
        var _rowid = $(element).data("entryid");
        var data = { patientID: _patientid, entryID: _rowid, };
        url = 'VP/VP/RiskFactor_CPP_Edit';

        //$("#" + modalTemplateContentId).width(width);
        //$("#" + modalTemplateBodyId).height(height);
        $("#" + modalTemplateTitleId).text("Edit");

        ajaxCall(url, data, false, function (data) {
            $("#" + modalTemplateBodyId).html(data);
            $("#" + modalTemplateId).on("shown.bs.modal", function () { });
            $("#" + modalTemplateId).on("hidden.bs.modal", function () { });
            $("#" + modalTemplateId).modal({ keyboard: false, backdrop: "static" }, "show");
        });

        return false;
    }
</script>
<div class="topPadding" id="divGrd">
    <div class="row">
        <div class="col-md-12">
            <div class="pre-scrollable">
                <table class="table table table-striped table-bordered table-condensed ">

                    @for (int i = 0; i < @Model.VP_RiskFactors.Count; i++)
                    {
                        string startDate = ((Model.VP_RiskFactors[i].StartDateMonth == 0 ? string.Empty : Model.VP_RiskFactors[i].StartDateMonth.ToString()) + (Model.VP_RiskFactors[i].StartDateDay == 0 ? string.Empty : "/" + Model.VP_RiskFactors[i].StartDateDay.ToString()) + (Model.VP_RiskFactors[i].StartDateYear == 0 ? string.Empty : "/" + Model.VP_RiskFactors[i].StartDateYear.ToString())).Trim('/');
                        string endDate = ((Model.VP_RiskFactors[i].EndDateMonth == 0 ? string.Empty : Model.VP_RiskFactors[i].EndDateMonth.ToString()) + (Model.VP_RiskFactors[i].EndDateDay == 0 ? string.Empty : "/" + Model.VP_RiskFactors[i].EndDateDay.ToString()) + (Model.VP_RiskFactors[i].EndDateYear == 0 ? string.Empty : "/" + Model.VP_RiskFactors[i].EndDateYear.ToString())).Trim('/');

                        if (i == 0)
                        {
                            <tr>
                                <td><span class="span"><b>Show</b></span></td>
                                <td><span class="span"><b>ExposureDetails</b></span></td>
                                <td><span class="span"><b>RiskFactor</b></span></td>
                                <td><span class="span"><b>Notes</b></span></td>
                                <td class="spanCell"><span class="span"><b>On Set Age</b></span></td>
                                <td class="spanCell"><span class="span"><b>Start Date</b></span></td>
                                <td class="spanCell"><span class="span"><b>End Date</b></span></td>
                                <td class="spanCell"><span class="span"><b>Life Stage</b></span></td>

                                <td class="spanCell"><span class="span"><b>Position</b></span></td>
                                <td class="spanCell"><span class="span"><b>Status</b></span></td>
                                <td class="spanCell"><span class="span"><b>SubmitDate</b></span></td>
                                <td class="spanCell"> </td>
                            </tr>
                        }

                        <tr>                            
                            <td class="spanCell">
                                @Html.CheckBoxFor(model => model.VP_RiskFactors[i].Visible, new { @disabled = "disabled" })
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].ExposureDetails, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].RiskFactor, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].Notes, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @*@Html.DisplayFor(model => (object)model.VP_RiskFactors[i].OnsetAge, new { @class = "txtBox" })
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].Unit, new { @class = "txtBox" })*@
                                @(Model.VP_RiskFactors[i].OnsetAge == 0 ? string.Empty : Model.VP_RiskFactors[i].OnsetAge.ToString() + " " + (string.IsNullOrEmpty(Model.VP_RiskFactors[i].Unit) ? string.Empty : Model.VP_RiskFactors[i].Unit))
                            </td>

                            <td class="spanCell">
                                @startDate
                            </td>
                            <td>
                                @endDate
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].LifeStage, new { @class = "txtBox" })
                            </td>

                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].Position, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                @*@Html.TextBoxFor(model => model.VP_RiskFactors[i].Status, new { @class = "txtBox" })*@
                                @Html.CheckBox("chkStatus", (object)(object)(object)Model.VP_RiskFactors[i].Status == 1)
                            </td>
                            <td class="spanCell">
                                @Html.DisplayFor(model => (object)model.VP_RiskFactors[i].SubmitDate, new { @class = "txtBox" })
                            </td>
                            <td class="spanCell">
                                <a href="#" onclick="return editRiskFactor(this);"
                                   data-patientid="@Model.PatientId"
                                   data-entryid="@Model.VP_RiskFactors[i].Id">Edit</a>
                                &nbsp;&nbsp;&nbsp;
                                <a href="#" id="hl-del-@Model.VP_RiskFactors[i].Id" onclick="return cppButtonDeleteClicked(this);" data-name="hl-del-@Model.VP_RiskFactors[i].Id" data-patientid="@Model.PatientId" data-cpptype="4" data-rowid="@Model.VP_RiskFactors[i].Id">Delete</a>
                                &nbsp;&nbsp;&nbsp;
                                <div class="btn-popover-container">
                                    <button type="button" class="btn btn-default btn-xs popover-btn" data-original-title="" title="" data-toggle="popover">
                                        <span class="glyphicon glyphicon-th-list" style="color: #337ab7;"></span>
                                    </button>
                                    <div class="btn-popover-title">Residual Information</div>
                                    <div class="btn-popover-content">@Html.Raw(Model.VP_RiskFactors[i].ResidualInformation.Replace("\n", "<br />"))</div>
                                </div>
                            </td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </div>
</div>