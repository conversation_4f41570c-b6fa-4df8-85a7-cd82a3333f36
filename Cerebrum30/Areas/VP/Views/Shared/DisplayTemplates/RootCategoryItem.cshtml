@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustom

@{
    var rootCategory = Model;
    var rootCategoryId = Model.RootCategoryId;
    var groupId = Model.GroupId;
    var externalDoctorId = Model.ExternalDoctorId;   
    var categoryName = Model.Name;
    var practiceTemplateId = Model.PracRootCategoryTempId;       
}


<ul class="nav drp-container">
    <li data-load-always="true" data-load-url="@Url.Action("GetRootCategoryCustomPhrasesList","visit",
                  new { area="vp",
                      practiceTemplateId = practiceTemplateId,
                      rootCategoryId = rootCategoryId,
                      groupId = groupId,
                      externalDoctorId = externalDoctorId
                  })" data-after-load-function="setSelectedPhrases" data-after-load-function-args="@rootCategoryId" class="dropdown">       
    <a class="dropdown-toggle" href="#" data-toggle="dropdown">
        <span class="rep-phrase-header drp-header">@categoryName</span>
    </a>           

    @if (rootCategory.PhrasesLoaded)
    {
       @(await Html.PartialAsync("~/Areas/VP/Views/Shared/DisplayTemplates/RootCategoryItemList.cshtml", (object)(object)rootCategory))
    }

    </li>
</ul>


    


