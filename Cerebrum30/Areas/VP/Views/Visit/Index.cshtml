@model Cerebrum.ViewModels.VP.VMVPMain
@{
    ViewBag.Title = "Visit Page";
    ViewBag.ModuleName = "Visit Page";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";

    var isValidVP = Model.IsInValidVP == false ? true : false;
    var appDateStr = isValidVP ? Model.AppointmentTime.ToShortDateString() : "";
    var isBeingSent = Model.TestStatusId == (int)AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.BeingSent;
}
@section customcss{
    @Styles.Render("~/Areas/VP/Content/visit/css")
}

@section topscripts{

}
@section scripts
{
    @if (isValidVP)
    {
        @Scripts.Render("~/bundles/visit")
    }
    <script src="~/Scripts/autosize.min.js"></script>
}
@section patientinfo{
    @if (isValidVP)
    {
        @Html.GetPatientInfo((object)Model.PatientId, (object)(object)(object)Model.AppointmentId, (object)(object)Model.AppointmentTestId, (object)Model.TestId)
    }
}

@section topLinks{
    @if (isValidVP)
    {
        <div class="container-fluid">
            <div id="toplink-container">
                <div id="toplink-content">
                    <div id="div-links" class="vp-links spacer-top-10">
                        @{ await Html.RenderPartialAsync("_vplinks", (object)Model.LinksMain); }
                    </div>
                </div>
            </div>
        </div>
    }
}

<div id="div-vp-main-messages" class="spacer-top-10">
    @if (!isValidVP)
    {
        <script type="text/javascript">
            $(function () {
                var divMessages = $('#div-vp-main-messages');
                var messageHTML = getULMessageList(''+'@Model.Message');
                divMessages.html(messageHTML);
            });
        </script>
    }
</div>

@if (isValidVP)
{
    using (Html.BeginForm("SaveVisitData", "Visit", new { area = "VP" }, FormMethod.Post, true, new { @autocomplete = "off", @id = "frm-vp" }))
    {
        <div id="test-base-wrapper">
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => (object)m.AppointmentTestLogId)
            @Html.HiddenFor(m => (object)m.PrevAppointmentTestLogId)
            @Html.HiddenFor(m => (object)m.PrevAppTestLogIdDoctor)
            @Html.HiddenFor(m => (object)m.AppointmentTestLogDate)
            @Html.HiddenFor(m => (object)m.AppointmentTestId)
            @Html.HiddenFor(m => (object)m.AppointmentId)
            @Html.HiddenFor(m => (object)m.AppointmentTime)
            @Html.HiddenFor(m => (object)m.AppointmentStatus)
            @Html.HiddenFor(m => (object)m.PatientId)
            @Html.HiddenFor(m => (object)m.PracticeId)
            @Html.HiddenFor(m => (object)m.OfficeId)
            @Html.HiddenFor(m => (object)m.PracticeTemplateId)
            @Html.HiddenFor(m => (object)m.PracticeDoctor)
            @Html.HiddenFor(m => (object)m.PracticeDoctorId)
            @Html.HiddenFor(m => (object)m.PracticeDoctorUserId)
            @Html.HiddenFor(m => (object)m.PracticeDoctorSpecialtyId)
            @Html.HiddenFor(m => (object)m.ExternalDoctorId)
            @Html.HiddenFor(m => (object)m.PatientSalutation)
            @Html.HiddenFor(m => (object)m.PatientFirstName)
            @Html.HiddenFor(m => (object)m.PatientLastName)
            @Html.HiddenFor(m => (object)m.PatientAge)
            @Html.HiddenFor(m => (object)m.PatientAgeStr)
            @Html.HiddenFor(m => (object)m.PatientGender)
            @Html.HiddenFor(m => (object)m.PatientDOB)
            @Html.HiddenFor(m => (object)m.TestId)
            @Html.HiddenFor(m => (object)m.TestGroupId)
            @Html.HiddenFor(m => (object)m.TestStatusId)
            @Html.HiddenFor(m => (object)m.TestStatusIdSave)
            @Html.HiddenFor(m => (object)m.TestTime)
            @Html.HiddenFor(m => (object)m.TestStatus)
            @Html.HiddenFor(m => (object)m.TestStatusCSS)
            @Html.HiddenFor(m => (object)m.IsAmended)
            @Html.Hidden("IsTrainee", (object)CerebrumUser.IsTrainee)
            @Html.Hidden("SaveType", 1)@* default is regular save**@
            @Html.HiddenFor(m => (object)m.RootPhraseFormat)
            @Html.HiddenFor(m => m.ReportPhraseFormatTable, new { @id = "RootPhraseFormatTable" })
        </div>
        <div id="vp-main-wrapper">

            <div id="div-billing" class="spacer-top-10">
                @{ await Html.RenderPartialAsync("_billingCodes", (object)Model.BillingMain); }
            </div>

            <div id="div-cpp-meds">
                @{ await Html.RenderPartialAsync("_cppMedsMain", (object)Model.CPPMedsMain); }
            </div>

            <div id="div-phrases" class="spacer-top-10">
            </div>

            @*<div id="div-meas">
                    @{ await Html.RenderPartialAsync("_measurements", (object)Model.MeasurementsMain); }
                </div>*@
            <div id="div-meas">
                @{ await Html.RenderPartialAsync("_measurements_", (object)Model.MeasurementsMain); }
            </div>

            <div class="row">&nbsp;</div>

            <div id="vp-cc-doctor-holder"></div>

            <div class="row">&nbsp;</div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="text-left nopadding form-group form-inline form-group-sm">
                        <span><b>Log</b></span>
                        <select style="min-width:100px" class="dropdown form-control" name="drp-vp-app-logs" id="drp-vp-app-logs">

                            @foreach (var dbLog in Model.AppointmentTestLogsLKP)
                            {
                                if (dbLog.Id == Model.AppointmentTestLogId)
                                {
                                    <option data-practice-template-id="@dbLog.PracticeTemplateId" data-template-name="@dbLog.TemplateName" data-app-log-date="@dbLog.LogDateStr" value="@dbLog.Id" selected>@dbLog.Text</option>
                                }
                                else
                                {
                                    <option data-practice-template-id="@dbLog.PracticeTemplateId" data-template-name="@dbLog.TemplateName" data-app-log-date="@dbLog.LogDateStr" value="@dbLog.Id">@dbLog.Text</option>
                                }
                            }

                        </select>
                    </div>
                </div>

                <div class="col-sm-6" id="save-buttons">
                    <div class="form-inline pull-right">
                        <div class="form-group form-group-sm _toCloneIntoVP2">
                            <div class="save-btn-wrapper">

                                @if (CerebrumUser.HasPermission("Save VP"))
                                {


                                    <div style="margin-right:15px;padding:5px;" class="div-test-status pull-left @Model.TestStatusCSS">
                                        <span class="status-desc-holder">Status: @Model.TestStatus</span>
                                        @if (Model.TestStatusId != (int)AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReportCompleted
                                            && Model.TestStatusId != (int)AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.BeingSent)
                                        {
                                            <div class="btn-popover-container">
                                                <button type="button" class="btn btn-default btn-xs popover-btn">
                                                    <span class="glyphicon glyphicon-pencil text-primary"></span>
                                                </button>
                                                <div class="btn-popover-title">Test Status</div>
                                                <div class="btn-popover-content">
                                                    <ul class="ul-appstatus"></ul>
                                                </div>
                                            </div>
                                        }
                                    </div>

                                    <button type="button" data-ds-url="@Url.Action("index", "daysheet", new { Area = "Schedule", OfficeId = Model.OfficeId, Date = appDateStr })"
                                            data-save-type="@AwareMD.Cerebrum.Shared.Enums.VPWSSaveType.Save"
                                            class="btn-save-vp btn btn-default btn-sm btn-vp-save-type">
                                        <i class="glyphicon glyphicon-floppy-save"></i>
                                        Save
                                    </button>

                                }

                                @if (CerebrumUser.HasPermission("SendLetter"))
                                {
                                    <a data-cb-tp="tooltip"
                                       data-cb-tp-title="Saves to patient's chart. This will NOT send the note externally."
                                       data-ds-url="@Url.Action("index", "daysheet", new { Area = "Schedule", OfficeId = Model.OfficeId, Date = appDateStr })"
                                       data-save-type="@AwareMD.Cerebrum.Shared.Enums.VPWSSaveType.FinalizeNote"
                                       class="btn-finalize-note btn btn-default btn-sm btn-vp-save-type">
                                        Save Chart Note
                                    </a>
                                    if (!isBeingSent)
                                    {
                                        <a data-ds-url="@Url.Action("index", "daysheet", new { Area = "Schedule", OfficeId = Model.OfficeId, Date = appDateStr })"
                                           data-save-type="@AwareMD.Cerebrum.Shared.Enums.VPWSSaveType.SendLetter"
                                           class="btn-send-letter btn btn-default btn-sm btn-vp-save-type">
                                            <i class="glyphicon glyphicon-share-alt"></i>
                                            Send Letter
                                        </a>
                                    }
                                }
                            </div>
                        </div>
                    </div>

                </div>

            </div>

        </div>
    }// end using

}