@model Cerebrum.ViewModels.TestBase.VMSendHistoryMain


@using (Html.BeginForm("", "visit", new { area = "VP" }, FormMethod.Post, true, new { @id = "frm-save-custom-measurements" }))
{
    @Html.HiddenFor(x => (object)x.AppointmentId)
    @Html.HiddenFor(x => (object)x.TestId)
    @Html.HiddenFor(x => (object)x.AppointmentTestId)
    @Html.HiddenFor(x => (object)x.PatientId)

    <div class="form-horizontal">        
        @{ await Html.RenderPartialAsync("_sendHistoryList", (object)Model.SendHistoryItems);}       
    </div>
        
    <div style="margin-top:15px;" class="text-right">
        <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
    </div>             
      
}


