
 @{      
     var practiceCategoriesLK = ViewBag.PracticeCagoriesLK != null ? (List<Cerebrum.ViewModels.Common.VMLookupItem>)ViewBag.PracticeCagoriesLK : new List<Cerebrum.ViewModels.Common.VMLookupItem>();
     var multiSelectList = new MultiSelectList(practiceCategoriesLK, "Value", "Text");
 }


@Html.DropDownList("PracticeCategoriesLK", new SelectList(practiceCategoriesLK, "Value", "Text"), new { @class = "form-control" })
                


