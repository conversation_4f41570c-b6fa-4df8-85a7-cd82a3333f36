@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustom

@{
    var rootCategory = Model;
    var rootCategoryId = Model.RootCategoryId;
    var groupId = Model.GroupId;
    var externalDoctorId = Model.ExternalDoctorId;   
    var categoryName = Model.Name;
    var practiceTemplateId = Model.PracRootCategoryTempId;       
}


@*<ul class="nav drp-container">*@
<ul class="nav drp-container">
    <li class="dropdown">       
    <a class="dropdown-toggle rep-phrase-header-holder" href="#" data-toggle="dropdown">
        <span class="rep-phrase-header drp-header">@categoryName</span>
    </a>           

        @if (rootCategory.PhrasesLoaded && !String.IsNullOrWhiteSpace(rootCategory.PhrasesHTML))
        {
            @Html.Raw(rootCategory.PhrasesHTML)
        }
        else if (rootCategory.PhrasesLoaded)
        {
            @(await Html.PartialAsync("_rootCategoryItemList", (object)(object)rootCategory))
        }

    </li>
</ul>


    


