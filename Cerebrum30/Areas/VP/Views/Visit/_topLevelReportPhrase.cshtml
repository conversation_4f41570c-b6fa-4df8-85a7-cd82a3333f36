@model Cerebrum.ViewModels.VP.VMTopLevelRptPhrase

@{
    var topPhrase = Model;
    var phraseId = Model.Id;
    var topLevelId = Model.Id;
    var phraseName = Model.Name;
    var topLeveltxtBox = Model.ControlName;
    var isPracticeSetting = Model.IsPracticeSetting;
    var isOpeningStatement = Model.IsOpeningStatement;   

}

  <li>
      <div class="rpt-phrases-config">
          <div class="pull-left">
              <a data-phrase-id="@phraseId"
                 data-phrase-name="@phraseName"
                 data-topphrase-id="@topLevelId"
                 data-parent-id="-1"
                 data-cntrlname="@topLeveltxtBox"
                 data-is-practice-setting="@isPracticeSetting"
                 data-is-opening-statement="@isOpeningStatement"
                 data-url="#"
                 class="btn-add-subitem" href="#">Add Subcategory/Item</a>
          </div>
          <div class="pull-right">
              <a data-phrase-id="@phraseId"
                 data-phrase-name="@phraseName"
                 data-topphrase-id="@topLevelId"
                 data-parent-id="-1"
                 @*data-cntrlname="@topLeveltxtBox"*@
                 data-is-practice-setting="@isPracticeSetting"
                 data-is-opening-statement="@isOpeningStatement"
                 data-url="#" class="btn-edit-subcategories" href="#">View Subcategories</a>
          </div>
          <div class="clearfix"></div>
      </div>
    </li>
    <li class="divider"></li>

    @foreach (var item in topPhrase.Phrases)
    {        
        await Html.RenderPartialAsync("_reportPhrase", (object)item);
    }
    


