@model Cerebrum.ViewModels.User.VMUserPatientAccessMain

@{
    ViewBag.Title = "User Patient Access List";
    ViewBag.ModuleName = "Admin - User Patient Access";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
    var added = TempData["UserPatientAdded"] != null ? true: false;
}

@section scripts{
    <script type="text/javascript">
        var wasAdded = @added.ToString().ToLower();
        $(function () {
            loadPracticePatientAutoComplete("#frm-add-user-patient #PatientFullName", "#frm-add-user-patient #PatientId");
            loadPracticeUsersAutoComplete("#frm-add-user-patient #UserFullName", "#frm-add-user-patient #UserId");

            if(wasAdded)
            {
                showNotificationMessage("success", "User Patient Access Added");
            }          

            $(document).on('click', '.btn-user-pat-delete', function (e) {

                e.preventDefault();
                var modalId = "#confirmModal";
                var confirmMessage = $(this).data("confirm-msg");                
                var element = $(this);

                prepareConfirmModal(modalId, "delete", confirmMessage, false);

                $(modalId).on('shown.bs.modal', function (e) {

                    var btnConfirm = $(this).find(".btn-confirm-ok").first();
                    $(btnConfirm).click(function (event) {
                        
                        hideModal(modalId);
                        var url = '/admin/users/deleteuserpatient/';
                        var userPatId = element.data("userpat-id");
                        ajaxCall(url, {id: userPatId}, false, function (result) {
                            if (result.success) {      
                                sessionStorage.ResultMessage = result.message;                                    
                                window.location = '/admin/users/userpatientaccess/'
                            }
                            else{
                                showNotificationMessage("error", result.message);
                            }
                        });                       
                    });
                    
                });

                showModal(modalId);
            });
        });
    </script>
}

<h3>User Patient Access</h3>

<div style="padding-top:3px;" id="user-patient-add-container">
    @(await Html.PartialAsync("_userPatientAdd", (object)(object)Model.UserPatientAccessAdd))
</div>

<hr />

<div id="userPatientList-container">
    @(await Html.PartialAsync("_userPatientAccessList", (object)(object)Model.UserPatientAccesses))
</div>

