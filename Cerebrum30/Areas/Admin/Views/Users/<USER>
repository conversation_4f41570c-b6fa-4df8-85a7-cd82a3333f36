@model Cerebrum.ViewModels.User.VMLoginHistoryMain

@{
    ViewBag.Title = "Login History";
    ViewBag.ModuleName = "Admin - Login History";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
}
<script src="~/Areas/Admin/scripts/auditJS.js"></script>
<h3>Login History</h3>

<div style="padding-top:3px;" id="loginHistory-search">
    @(await Html.PartialAsync("_loginSearch", (object)(object)Model.LoginSearch))
</div>

<hr />

<div id="loginHistoryList-container">
    @(await Html.PartialAsync("_pageOpenList", (object)(object)Model.LoginHistories))
</div>

