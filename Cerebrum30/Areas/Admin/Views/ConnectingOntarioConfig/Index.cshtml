
@{
    ViewBag.Title = "Connecting Ontario Config";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
<script src="~/Areas/Admin/scripts/connecting-ontario-config.js"></script>

<h2>Connecting Ontario Config</h2>
@if (CerebrumUser.HasRole($"Practice Admin"))
{
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4>Practice Config</h4></div>
            <div id="co-practice-config" class="panel-body">
                @(await Html.PartialAsync("Practice", (object)new { area = "Admin" }))
            </div>
        </div>
    </div>
</div>
}
<div class="row">
    <div class="col-lg-6" style="height:650px;overflow-y: scroll;" >
        <div class="panel panel-default ">
            <div class="panel-heading"><h4>User(s) Config</h4></div>
            <div id="co-practice-config" class="panel-body">
                @if (CerebrumUser.HasPermission("UserAdmin"))
                {
                    @(await Html.PartialAsync("COUsers", (object)new { area = "Admin" }))
                }
                else
                {
                    @(await Html.PartialAsync("COUser", (object)new { area = "Admin" }))
                }

            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div id="co-user-panel"></div>
    </div>
</div>
<br />
<br />
