@model IEnumerable<Cerebrum30.Areas.Admin.Models.ViewModels.PracticeTestVM>

@{
    ViewBag.Title = "Practice Tests";
    ViewBag.ModuleName = "Practice Tests";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<script src="~/Scripts/cerebrum3-practice-test.js"></script>


<h2>
    "@ViewBag.PracticeName"
</h2>
<h4>Total  @(Model != null ? Model.Count() : 0)  tests found.</h4>
<div id="dialog-confirm" title="Select Practice">
    <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>You are about to redirect to home page. Are you sure?</p>
</div>
<div id="dialog-testInstruction" title="Test Instruction">
    <p><span class="ui-icon ui-icon-alert"></span>You are about to redirect to home page. Are you sure?</p>
</div>
<p>
    <a href="/admin/practicetests/AddNewTests" id="addNewTest" data-value="@ViewBag.PracticeId">Add New Test</a>
</p>

<p>

    @*@Html.DropDownList("practiceId", new SelectList(ViewBag.Practices, "Id", "name", ViewBag.PracticeId), "Select a practice....")*@
</p>
@if (Model != null)
{
    <table class="table" id="table-adminPractice">
        <thead>
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.TestInstruction)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.testDuration)
                </th>
                <th>
                    Test
                </th>
                <th>
                    Test Short Name
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.isActive)
                </th>
                <th>Required Resources</th>
                <th>
                    @Html.DisplayNameFor(model => model.createdDate)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.updatedDate)
                </th>
                @*<th>
                        Attach Raw Data
                    </th>*@
            </tr>
        </thead>
        @foreach (var item in Model)
        {
            <tr>
                <td id="<EMAIL>">
                    <p>
                        @item.TestInstruction

                        <a href="#" data-toggle="modal" data-target="#practice-test-modal" data-url="/admin/practicetests/AddUpdatePracticeTestInstruction/@item.Id" data-test="@item.Test.testFullName" data-value="@item.Id">@(item.TestInstruction!=null?"Edit Instruction":"Add Instruction")</a>
                    </p>
                </td>
                <td>
                    @Html.TextBoxFor((System.Func<dynamic, object>)(modelItem => (object)item.testDuration), new { @data_value = item.Id, @type = "number", min = "0", @step = "5", @class = "test-duration" })
                </td>

                <td>
                    @item.Test.testFullName
                </td>
                <td>
                    @item.Test.testShortName
                </td>
                <td>
                    <input type="checkbox" class="test-active" value="@item.isActive" @(item.isActive ? "checked" : "") data-value="@item.Id" data-testId="@item.TestId" />
                </td>
                <td>
                    @{
                        var testresources = item.Test.TestResources.Where((System.Func<dynamic, bool>)(w => w.IsActive == true)).Select((System.Func<dynamic, object>)(s => s.testResourceType.TestResourceName));
                    }
                    @string.Join(",", testresources)
                </td>
                <td>
                    @item.createdDate
                </td>
                <td>
                    @item.updatedDate
                </td>
                @*<td>
                        <input type="checkbox" class="raw-data-attach" value="@item.attachRawData" @(item.attachRawData ? "checked" : "") data-value="@item.Id" data-testId="@item.TestId" />
                    </td>*@
            </tr>
        }

    </table>
}
@Html.ActionLink("Back to Tests", "Index", "Tests", new { area = "Admin" }, null)
<br />
<br />
<br />
<br />

<!-- Modal -->
<div class="modal fade" id="practice-test-modal" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Instruction</h4>
            </div>
            <div class="modal-body">
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success modal-submit-btn">Save</button>
            </div>
        </div>

    </div>
</div>