@model IEnumerable<Cerebrum.ViewModels.User.VMPageLog>
<link href="~/Areas/Admin/Content/PageLog.css" rel="stylesheet" />
<div class="panel panel-info panel-table">
    <div class="panel-heading">Results (@Model.Count())</div>
    <table class="table table-bordered">
        <tr>
            <th></th>
            <th>
                @Html.DisplayNameFor(model => model.UserName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IPAddress)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateCreated)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Port)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Page)
            </th>
            
        </tr>

        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @if (item.Message != null )
                    {
                        <button class="btn btn-info" data-toggle="collapse" data-target="#@item.Id">Show Info</button>

                    }
                </td>

                <td>
                    @item.UserName
                </td>
                <td>
                    @item.IPAddress
                </td>

                <td>
                    @item.DateCreated
                </td>
                <td>
                    @item.Port
                </td>
                <td>
                    @item.Page
                </td>
              
            </tr>
            
                    <tr id="@item.Id" class="collapse">

                        <td colspan="6">
                            @if (item.Message != null)
                            {
                                @(await Html.PartialAsync("_pageOpen", (object)item.Message))
                            }else if(item.LegacyMessage!=null)
                            {
                                <script>
                                    var obj = jQuery.parseJSON(@item.LegacyMessage);
                                    $("#_"+@item.Id).html(obj.action);
                                </script>
                                <div id="<EMAIL>"></div>
                            }

                        </td>

                    </tr>
                
        }

    </table>
</div>