@model Cerebrum.ViewModels.User.VMPageOpenMain

@{
    ViewBag.Title = "Login History";
    ViewBag.ModuleName = "Admin - Login History";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
    bool eformsEnabled = Convert.ToBoolean(ViewBag.EformsEnabled);
}

<h3>Login History</h3>

<div style="padding-top:3px;" id="loginHistory-search">
    @(await Html.PartialAsync("_pageOpenSearch", (object)(object)Model.PageOpenSearch))
</div>

<hr />

<div style="padding-top:3px;" id="loginHistory-pagination">
    @(await Html.PartialAsync("_pageOpenPaging", (object)(object)Model.PageOpenSearch))
</div>

<div id="loginHistoryList-container">
    @(await Html.PartialAsync("_pageOpenList", (object)(object)Model.LoginHistories, new ViewDataDictionary { { "EformsEnabled", eformsEnabled } }))
</div>


