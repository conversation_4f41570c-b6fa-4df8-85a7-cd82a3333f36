@model Cerebrum.ViewModels.HL7.VMHL7LostReport
<tbody id="<EMAIL>">
    <tr>
        <td>
            @Html.DisplayFor(model => (object)model.Count)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.Lab)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.AccessionNumber)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.PatientOHIP)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.PatientLastName)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.PatientFirstName)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.ReportSaved)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.CreateDateTime)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.LastModifiedDateTime)
        </td>
        <td>
            @Html.DisplayFor(model => (object)model.LastModifiedByUserId)
        </td>
        <td>
            @Html.AntiForgeryToken()
            @if (Model.Doctors.Cast<dynamic>().All((System.Func<dynamic, bool>)(a => a.PracticeId > 0 && a.ExternalDoctorId > 0)))
            {
                @Html.ActionLink("Add Patient", "CreateNewPatient", "Patients", new { @class = "save-report-patient", data_ohip = Model.PatientOHIP, data_LastName = Model.PatientLastName, data_FirstName = Model.PatientFirstName })

                @Html.Raw(" | ")

                @Html.ActionLink("Save Report", "SaveHL7LostReport", new { id = Model.Id }, new { @class = "save-lost-report", data_id = Model.Id })
                
                @Html.Raw(" | ")
            <span class="text-danger">
                @Html.ActionLink("Reject Report", "RejectHL7LostReport", new { id = Model.Id }, new { @class = "reject-lost-report", data_id = Model.Id })
            </span>
            }
        </td>
    </tr>
    <tr>
        <td colspan="11">
            @(await Html.PartialAsync("ReportDoctors", (object)(object)Model.Doctors))
        </td>
    </tr>
</tbody>

