@model IEnumerable<Cerebrum.ViewModels.HL7.VMHL7LostReportDoctor>
@{ 
    var reportid = Model != null && Model.Any() ? Model.FirstOrDefault().HL7LostReportId : 0;
}

@if (Model != null && Model.Any())
{
<table class="table" id="report-@reportid">
    <tr>
        <th>
            OHIP / Group
        </th>
        <th>
            Last Name
        </th>
        <th>
            First Name
        </th>
        <th>
            Address
        </th>
        <th>MapTo=></th>
        <th>Practice</th>
        <th>Doctor</th>
        <th></th>
        <th></th>
        <th></th>
    </tr>
    @foreach (var d in Model)
    {

        <tr>
            <td>
                @Html.Raw(d.OHIPOrGroup)
            </td>
            <td>
                @Html.Raw(d.LastName)
            </td>
            <td>
                @Html.Raw(d.FirstName)
            </td>
            <td>
                @Html.Raw(d.Address)
            </td>
            <th>MapTo=></th>


            @if (d.PracticeId == null && d.ExternalDoctorId == null)
            {
                <td colspan="4">
                    <div class="row form-horizontal">
                        @using (Html.BeginForm("UpdateHL7LostReportDoctor", "HL7LostReports", new { area = "Admin" }, FormMethod.Post, true, new { @id = "frm-HL7LostReports-" + @d.Id }))
                        {
                            @Html.AntiForgeryToken()
                            <div class="col-sm-4">
                                @Html.DropDownList("PracticeId", new SelectList(ViewBag.Practices, "Id", "PracticeName"), "Choose One", new { @class = "form-control" })
                                @Html.Hidden("HL7LostDoctorId", (object)(object)d.Id)
                                @Html.Hidden("HL7ReportId", (object)d.HL7LostReportId)
                            </div>
                            <div class="col-sm-4">
                                @Html.TextBox("ExternalDoctor", (object)null, new { @placeholder = "Doctor Name", @class = "form-control doctor-text" })
                                @Html.Hidden("ExternalDoctorId")
                            </div>
                            <div class="col-sm-3">
                                <button type="button" data-practiceTest="@d.HL7LostReportId" class="btn btn-xs btn-save-doctor btn-info">
                                    <small> <span class="glyphicon glyphicon-floppy-disk"></span></small>
                                </button>
                            </div>
                        }
                    </div>
                </td>
            }
            else
            {
                <td>@d.PracticeName</td>
                <td>@d.DoctorName</td>
                <td></td>
                <td></td>
            }


            <td></td>

        </tr>
    }
</table>
}
