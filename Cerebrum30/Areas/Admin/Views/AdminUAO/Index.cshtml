@model List<Cerebrum.ViewModels.AdminUAO.VMAdminUao>
@{
    ViewBag.Title = "UAO Settings";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section scripts{
    <script src="~/Areas/Admin/scripts/admin-uao.js"></script>
}

<h3 class="text-center">List of UAO</h3>
<input type="button" class="btn btn-primary btn-sm pull-right" id="btn-add-new-uao" value="Add new UAO" data-id="0" />
@if (Model.Count > 0)
{
    <div class="col-md-12">
        <br />
        <div id="uao-container">
            @(await Html.PartialAsync("_uaoList", (object)(object)Model))
        </div>
    </div>
}




