@model Cerebrum.ViewModels.Audit.VMAuditSearch

@{
    ViewBag.Title = "Audit";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}


<script src="~/Areas/Admin/scripts/auditJS.js"></script>
<script type="text/javascript">

    $(document).on('click', "a.audit-page", function () {
        var liId = $(this).parent("li").data("id");
        $(this).parent("li").addClass("active");
        $("#frmAudit input[name=p_page]").val(liId)
        $("#frmAudit").submit();
    });

    $(function () {
        $("#btnPrint").click(function (e) {
            e.preventDefault()
            window.print();
        });
        $(".btn-reset").click(function (e) {
            $('#frmAudit')[0].reset();
            $("#frmAudit input[type=hidden]").each(function () {
                $(this).val('');
            });
            clearForm("#frmAudit");
            window.location.href = '/Admin/Audit/index';
        });

    });
    function clearForm(form) {
        // iterate over all of the inputs for the form
        // element that was passed in
        $(':input', form).each(function () {
            var type = this.type;
            var tag = this.tagName.toLowerCase(); // normalize case
            // it's ok to reset the value attr of text inputs,
            // password inputs, and textareas
            if (type == 'text' || type == 'password' || tag == 'textarea')
                this.value = "";
                // checkboxes and radios need to have their checked state cleared
                // but should *not* have their 'value' changed
            else if (type == 'checkbox' || type == 'radio')
                this.checked = false;
                // select elements need to have their 'selectedIndex' property set to -1
                // (this works for both single and multiple select elements)
            else if (tag == 'select')
                this.selectedIndex = -1;
        });
    };
</script>
<style type="text/css">
    @@media print {
        body {
            margin-top: 5mm;
            margin-bottom: 5mm;
            margin-left: 0mm;
            margin-right: 0mm;
        }

        #div-print {
            margin: 3px !important;
            padding: 3px !important;
            transition: none;
        }

        .audit-changes {
            display: inline;
            margin: 13px !important;
        }
    }

    @@page {
        size: auto; /* auto is the initial value */
        margin: 5mm; /* this affects the margin in the printer settings */
    }

    .audit-margin-lr {
        margin-left: 5px;
    }

    .label-text {
        padding-top: 3px;
    }
</style>
<div class="audit-margin-lr">
    <h2>Audit</h2>

    <div class="panel panel-info">
        <div class="panel-body">
            @using (Html.BeginForm("Index", "Audit", FormMethod.Post, new { id = "frmAudit" }))
            {
                @Html.AntiForgeryToken()
                <table>
                    <tr>
                        <td class="label-text">
                            @Html.LabelFor(model => model.startDate, htmlAttributes: new { @class = "control-label col-md-2" })
                        </td>
                        <td>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.startDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                            </div>
                        </td>
                        <td class="label-text">
                            @Html.LabelFor(model => model.endDate, htmlAttributes: new { @class = "control-label col-md-2" })
                        </td>
                        <td>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.endDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                            </div>
                        </td>
                        <td class="label-text text-nowrap">
                            @Html.LabelFor(model => model.IPAddress, htmlAttributes: new { @class = "control-label col-md-2 " })
                        </td>
                        <td>
                            <div class="col-md-10 text-nowrap">
                                @Html.EditorFor(model => model.IPAddress, new { htmlAttributes = new { @class = "form-control" } })
                            </div>
                        </td>
                        <td class="label-text">
                            @Html.LabelFor(model => model.UserName, htmlAttributes: new { @class = "control-label col-md-2" })
                        </td>
                        <td>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.UserName, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.HiddenFor(model => (object)model.UserId)
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label-text">
                            @Html.LabelFor(model => model.patientName, htmlAttributes: new { @class = "control-label col-md-2" })
                        </td>
                        <td>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.patientName, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.HiddenFor(model => (object)model.PatientRecordId)
                            </div>
                        </td>
                        <td class="label-text">
                            @Html.LabelFor(model => model.Content, htmlAttributes: new { @class = "control-label col-md-2" })
                        </td>
                        <td>
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Content, new { htmlAttributes = new { @class = "form-control" } })
                            </div>
                        </td>

                        <td colspan="2">
                            @Html.HiddenFor(model => (object)model.p_page)
                            @Html.HiddenFor(model => (object)model.p_pagesize)
                            @Html.HiddenFor(model => (object)model.TotalPages)
                            @Html.HiddenFor(model => (object)model.PracticeId)
                            <input type="submit" value="Search" class="btn btn-default" />
                            <button type="reset" class="btn btn-default btn-reset" value="Reset">Reset</button>
                        </td>
                        <td valign="top" colspan="2"></td>
                    </tr>
                </table>
                <div class="form-inline">
                    @Html.ValidationSummary(false, "", new { @class = "text-danger" })
                </div>
            }
        </div>
    </div>
    <hr />
    @if (Model.AuditData != null && Model.AuditData.Count() > 0)
    {
        <ul class="pagination">
            @{
                int pages = ((int)(Model.TotalPages / Model.p_pagesize)) + 1;
            }
            @for (int i = 1; i <= pages; i++)
            {
                var active = Model.p_page == i ? "active" : "";
                <li class="audit-page @active" data-id="@i" data-form=""><a class="audit-page">@i</a> </li>
            }
        </ul>
                    await Html.RenderPartialAsync("_auditDataLogs", (object)Model.AuditData);
                }



</div>