@model IEnumerable<Cerebrum.ViewModels.Audit.VMAuditDataSP>
<div class="panel panel-info">
    <div class="panel-heading">Search Result <span class="badge">@Model.Count()</span>  <span class="text-right pull-right"><a id="btnPrint" href="#"> Print </a></span></div>
    <div id="printAudit" class="panel-body div-print">
        <table class="table">
            <tr>
                <th>@Html.DisplayNameFor(model => model.Id)</th>
                <th>
                    @Html.DisplayNameFor(model => model.UserName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.IpAddress)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.EventType)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.EventDateTime)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.TableName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.PatientFullName)
                </th>

                <th>

                </th>
            </tr>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @item.Id
                    </td>
                    <td>
                        @item.UserName
                    </td>
                    <td>
                        @item.IpAddress
                    </td>
                    <td>
                        @item.EventType
                    </td>
                    <td>
                        @item.EventDateTime
                    </td>
                    <td>
                        @item.TableName
                    </td>
                    <td>
                        @item.PatientFullName
                    </td>
                    <td>
                        <button class="btn btn-info" data-toggle="collapse" data-target="#@item.Id">Show Changes</button>

                    </td>

                </tr>
                <tr id="@item.Id" class="collapse audit-changes">
                    <td colspan="5">
                        @(await Html.PartialAsync("_auditJson", (object)item.json))
                    </td>
                </tr>
            }

        </table>
    </div>
</div>