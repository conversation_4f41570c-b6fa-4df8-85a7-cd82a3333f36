@model Cerebrum.ViewModels.Audit.VMAuditPageMain

@{
    ViewBag.Title = "Accessed Pages";
    ViewBag.ModuleName = "Admin";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
}

<h3>Page Access History</h3>

<div style="padding-top:3px;" id="pageAccess-search">
    @(await Html.PartialAsync("_pageAccessSearch", (object)(object)Model.PageSearch))
</div>

<hr />

<div style="padding-top:3px;" id="pageAccess-pagination">
    @(await Html.PartialAsync("_pageAccessPaging", (object)(object)Model.PageSearch))
</div>

<div id="pageAccessHistoryList-container">
    @(await Html.PartialAsync("_pageAccessHistoryList", (object)(object)Model.AuditPages))
</div>


