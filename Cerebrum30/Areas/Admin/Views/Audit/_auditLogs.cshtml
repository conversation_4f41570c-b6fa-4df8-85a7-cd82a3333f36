@model IEnumerable<Cerebrum.ViewModels.Audit.VMAuditLog>
<div class="panel panel-info">
    <div class="panel-heading">Search Result <span class="badge">@Model.Count()</span></div>
    <div class="panel-body">
        <table class="table">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.UserName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.IpAddress)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.EventType)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.EventDateTime)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.TableName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.PatientRecordId)
                </th>
               
                @*<th>
                        @Html.DisplayNameFor(model => model.ixChanges)
                    </th>*@
                <th>
                    
                </th>
            </tr>

            @foreach (var item in Model) {
                <tr>
                    <td>
                        @item.UserName
                    </td>
                    <td>
                        @item.IpAddress
                    </td>
                    <td>
                        @item.EventType
                    </td>
                    <td>
                        @item.EventDateTime
                    </td>
                    <td>
                        @item.TableName
                    </td>
                    <td>
                        @item.PatientRecordId
                    </td>
                   <td>
                       <button class="btn btn-info" data-toggle="collapse" data-target="#@item.Id">Show Changes</button>
                       <script>
                           $("#"+@item.Id).append('<td colspan="7">'+json.TableName+'</td>');
                       </script>
                   </td>
                    @*<td>
                            @item.ixChanges
                        </td>*@

                </tr>
                <tr id="@item.Id"  class="collapse">
                    <td colspan="7">
                       @(await Html.PartialAsync("_auditJson", (object)item.json))
                    </td>
                </tr>
}

        </table>
    </div>
</div>