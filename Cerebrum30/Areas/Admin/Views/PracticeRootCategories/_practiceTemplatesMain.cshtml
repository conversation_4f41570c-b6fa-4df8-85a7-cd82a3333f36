@model Cerebrum.ViewModels.Practice.VMPracticeRootCatMain
@{
    ViewBag.Title = "Pracice Root Categories";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@using (Html.BeginForm("GetPracticeTemplates", "PracticeRootCategories", new { area = "admin" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-prac-test-group" }))
{
    <div class="form-group form-group-sm">
        <label class="control-label" for="DoctorId">Doctor(s)</label>
        @Html.DropDownList("GroupId", new SelectList(Model.TestGroups, "Id", "Text"), "Choose One", new { @class = "form-control" })
    </div>   

}

<div id="div-practice-templates-main">


</div>