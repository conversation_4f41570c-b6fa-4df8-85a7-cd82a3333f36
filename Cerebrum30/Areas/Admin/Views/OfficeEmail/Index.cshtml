@model Cerebrum.ViewModels.OfficeEmail.IndexResponse

@{
    ViewBag.Title = "Office Email";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<script src="~/Areas/Admin/scripts/OfficeEmail.js"></script>

<div class="col-sm-12">
    <h2>Office Email</h2>
</div>
<div class="col-sm-12" style="margin-bottom: 24px;">
    <div class="col-sm-2"><label>Office</label></div>
    <div class="col-sm-3">
        @if (Model.offices.Count() == 1)
        {
            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm" })
        }
        else
        {
            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "Please Select Office", htmlAttributes: new { @class = "form-control input-sm" })
        }
    </div>
</div>
<div id="office-email-detail">
</div>
<div class="col-sm-12" style="margin-top: 24px;">
    <div class="col-sm-5 text-right">
        <button type="button" class="btn btn-default btn-sm btn-primary" id="buttonOfficeEmailSave" name="buttonOfficeEmailSave">Save</button>
    </div>
</div>
    <script type="text/javascript">
        $(function () {
            $(document).on("click", "#btn-view-rooms", function () {
                var url = $(this).data('url');
                var officeId = $("#OffId").val();
                $.ajax({
                    url: url, type: "GET", data: { id: officeId },
                    success: function (result) {
                        $("#room-list").html(result);
                    }

                })
            });
            $(document).on("click", ".btn-delete-office-room", function (e) {
                e.preventDefault();
                var deurl = $(this).attr("href");
                var dataid = $(this).data("id");
                $.ajax({
                    url: deurl,
                    type: 'POST',
                    data: { id: dataid },
                    success: function (result) {
                        location.reload();
                    }
                });
            });
            $(document).on('submit', '#frm-office-room-create', function (e) {
                e.preventDefault();
                var officeid = $("#OfficeId").val();
                var roomTypeId = $("#roomTypeId").val();

                $('.btn-office-room-type').prop('disable', true);

                $.ajax({
                    url: this.action,
                    type: this.method,
                    data: { officeid: officeid, roomTypeId: roomTypeId },
                    success: function (result) {
                        location.reload();
                    }
                });
            });
        });
    </script>
