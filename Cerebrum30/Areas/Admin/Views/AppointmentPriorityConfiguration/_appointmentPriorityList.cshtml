@model List<Cerebrum.ViewModels.Admin.VMAppointmentPriority>
@{
    Layout = null;
}
<table class="table table-condensed table-bordered table-responsive" id="appointment-priority-table">
    <thead>
        <tr>
            <th>Priority</th>
            <th class="text-center">Rank</th>
            <th class="text-center">Default</th>
            <th class="text-center">Active</th>
            <th class="text-center"></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Cast<dynamic>().OrderBy((System.Func<dynamic, object>)(r => r.Rank)).ThenBy((System.Func<dynamic, object>)(p => p.PriorityName)))
        {
            @(await Html.PartialAsync("_appointmentPriorityItem", (object)(object)item))
        }
    </tbody>
</table>


