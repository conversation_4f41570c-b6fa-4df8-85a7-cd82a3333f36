@model List<Cerebrum.ViewModels.Admin.VMAppointmentPriority>
@{ 
    ViewBag.Title = "Appointment Priority Configuration";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
    bool isPriorityEnabled = Convert.ToBoolean(ViewBag.IsPracticeAppointmentPriorityEnabled);
    string btnClass = isPriorityEnabled ? "btn-primary2" : "btn-danger";
    string btnText = isPriorityEnabled ? "Enabled" : "Disabled";
    string linkText = $"Click the button to {(isPriorityEnabled ? "disable" : "enable")} Configuration"; 
}
<link href="~/Areas/Admin/Content/appointment-priority.css" rel="stylesheet" />
<script src="~/Areas/Admin/scripts/appointment-priority.js"></script>

<div class="col-md-12 padding-top-bottom-30">
    <span style="font-size:24px;">Practice Appointment Priority Configuration</span>
    <div class="btn-popover-container">
        <span class="popover-btn c-pointer glyphicon glyphicon-info-sign glyphicon-info-sign-blue text-primary glyphicon-info-sign-v-align" data-original-title="" title=""></span>
        <div class="btn-popover-content">
            <div class="form-horizontal">
                <div class="form-group form-group-sm">
                    <div class="col-md-12">
                        Appointment Priority is a key appointment attribute that practices can configure to fit their specific workflow. By setting the Priority for an appointment, it will be prominently displayed on the Daysheet, Worklist, Worksheet, and VP. The Rank of a priority determines its priority: the lower the Rank, the higher the priority. This ensures that the most urgent appointments appear at the top of a doctor's worklist, aiding in the effective prioritization of patient care.
                        Updating a Priority record will have an immediate effect throughout Cerebrum, updating the priority for all appointments (past and future) already set with that priority. If you do not wish to impact the priorities of existing appointments, deactivate the old priority and create a new one.
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 padding-right-0 pull-right">
        <button id="btn-set-app-priority-config-state" class="btn @btnClass toggle-btn pull-right">@btnText</button>
        <span class="pull-right padding-top-5">
            <a href="javascript:void(null)" class="toggle-text" data-toggle="tooltip" id="span-toggle-button" title="After clicking the button, all users will need to re-login to see the changes, except for the current admin user.">@linkText</a>
        </span>
    </div>
</div>
@Html.AntiForgeryToken()
<div class="row">
    <div class="col-md-6">
        <div class="col-md-3"><input type="checkbox" id="chkShowActivePriorities" checked /> Active Only</div>
    </div>
</div>
<div class="col-md-6" id="admin-appointment-priority-list">
    @if (Model.Count > 0)
    {
        @(await Html.PartialAsync("_appointmentPriorityList", (object)(object)Model)) 
    }
    else
    {
        <h4>Priority has not been Configured</h4>
    }
</div>
<div class="col-md-12 padding-top-bottom-30">
    <input type="button" class="btn btn-primary2" id="btn-add-priority" value="Add Priority" />
</div>


