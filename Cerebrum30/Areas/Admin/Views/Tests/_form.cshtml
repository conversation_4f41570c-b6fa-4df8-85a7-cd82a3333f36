@model Cerebrum.ViewModels.Test.VMTest

@{
    ViewBag.Title = "Tests/Edit (Admin)";
    ViewBag.ModuleName = "Tests/Edit (Admin)";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Areas/Schedule/Content/appointments-modal.css" rel="stylesheet" />
@section scripts{
    <script src="~/Scripts/jscolor.js"></script>
}



<div style="margin-top:15px"></div>
<h4>@( Model != null && Model.Id > 0 ? "Edit: "+ @Html.DisplayFor(model => (object)model.testFullName) : "New")</h4>

<!--<h4>Test </h4>-->
<hr />

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

<div class="form-horizontal" style="">

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.color, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.color, new { htmlAttributes = new { @class = "form-control jscolor" } })
            @Html.ValidationMessageFor(model => model.color, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.testFullName, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.testFullName, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.testFullName, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.testShortName, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.testShortName, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.testShortName, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.order, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.order, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.order, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.duration, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.duration, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.duration, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.IsRadiology, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            <div class="checkbox">
                @Html.EditorFor(model => (object)model.IsRadiology)
                @Html.ValidationMessageFor(model => model.IsRadiology, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.RequireDevice, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            <div class="checkbox">
                @Html.EditorFor(model => (object)model.RequireDevice)
                @Html.ValidationMessageFor(model => model.RequireDevice, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.DeviceTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.DropDownList("DeviceTypeId", new SelectList(ViewBag.DeviceTypes, "Value", "Text", Model.DeviceTypeId), "Choose One", new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.DeviceTypeId, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.DeviceDuration, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.DeviceDuration, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.DeviceDuration, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Modalities, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-3">
            <div class="btn-group" data-toggle="buttons">
                @Html.EditorFor(model => (object)model.Modalities)
            </div>
            @Html.ValidationMessageFor(model => model.Modalities, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.TestGroups, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-3">
            <div class="btn-group" data-toggle="buttons">
                @Html.EditorFor(model => (object)model.TestGroups)
            </div>
            @Html.ValidationMessageFor(model => model.TestGroups, "", new { @class = "text-danger" })

        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.HrmTypeShort, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.HrmTypeShort, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.HrmTypeShort, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.HrmTypeLong, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.HrmTypeLong, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.HrmTypeLong, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.HrmModality, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.HrmModality, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.HrmModality, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.LoincCode, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.LoincCode, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.LoincCode, "", new { @class = "text-danger" })
        </div>
    </div>


    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.startTime, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-2">
            @Html.EditorFor(model => model.startTime, new { htmlAttributes = new { @class = "form-control date-picker" } })
            @Html.ValidationMessageFor(model => model.startTime, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group form-group-sm text-center">
        <div class="col-md-offset-1 col-md-3 ">
            @Html.HiddenFor(model => (object)model.Id)
            <input type="submit" value="@( Model != null && Model.Id>0 ? "Update" : "Add")" class="btn btn-default btn-sm btn-primary" />
        </div>
    </div>
</div>
}

<div>
    @Html.ActionLink("Back to List", "Index") | @(Model == null ? null : @Html.ActionLink("Details", "Details", new { id = Model.Id }))
</div>
<br />
<br />
<style>
    .btn.active, .btn:active {
        background-color: #627AF6;
    }

    .btn:focus {
        background-color: red;
    }

    .btn:hover,
    .btn:focus {
        color: #ffffff;
        background-color: #428bff;
        border-color: #357ebd;
    }
</style>
