@model Cerebrum.ViewModels.Common.VMExternalDoctor

<style>
    #frm-edit-ext-doctor .not-active {
        pointer-events: none;
        cursor: default;
        text-decoration: none;
        color: black;
    }

    /*#frm-edit-ext-doctor #ext-doc-address-holder{
        max-height: 200px;
        overflow-y:auto;
    }*/
</style>



@using (Html.BeginForm("Edit", "ExternalDoctors", new { area = "" }, FormMethod.Post, true, new { @id = "frm-edit-ext-doctor" }))
{
    var success = Model.success;
    var message = Model.message;
    @Html.ModalHeader("Edit Doctor " + Model.LastName)
    @Html.HiddenFor(x => (object)x.FirstNameOld)
    @Html.HiddenFor(x => (object)x.LastNameOld)

    <div class="modal-body content-height500">
        @if (success && !String.IsNullOrWhiteSpace(message))
        {
            <div class="alert alert-success alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                @message
            </div>
        }
        else if (!success && !String.IsNullOrWhiteSpace(message))
        {
            <div class="alert alert-danger alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                @message
            </div>
        }

        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)

        <div class="form-horizontal">

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OHIP, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.OHIP, new { htmlAttributes = new { @class = "form-control " + @Model.billNumber_ro } })
                        @Html.ValidationMessageFor(model => model.OHIP, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.IsActive) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                            </label>
                        </div>
                    </div>
                    <div class="pull-right">
                        @Html.LabelFor(model => model.SpecialtyId, htmlAttributes: new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DropDownList("SpecialtyId", new SelectList(Model.specialties, "Id", "Title", Model.SpecialtyId),
                            new { @class = "form-control" })
                        </div>

                    </div>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PhysicianType, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10 ">
                    @foreach (var ptype in Enum.GetNames(typeof(AwareMD.Cerebrum.Shared.Enums.PhysicianType)))
                    {
                        @Html.RadioButtonFor(model => model.PhysicianType, ptype, new { htmlAttributes = new { @class = "form-control" } }) @ptype
                        <span style="padding-right: 12px;"></span>
                    }
                    @Html.ValidationMessageFor(model => model.PhysicianType, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10 ">
                    @Html.EditorFor(model => model.FirstName, new { htmlAttributes = new { @class = "form-control " + @Model.firstNmae_ro } })
                    @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control " + @Model.lastNmae_ro } })
                    @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MiddleName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10  focus">
                    @Html.EditorFor(model => model.MiddleName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Addresses, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="ext-doc-address-holder" data-url="@Url.Action("GetExternalDoctorAddresses","externaldoctors",new { area="" })" data-external-doctor-id="@Model.Id">
                        @{ await Html.RenderPartialAsync("_externalDoctorAddressList", (object)Model.Addresses); }
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Comment, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Comment, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Comment, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.HRMId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.HRMId, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.HRMId, "", new { @class = "text-danger" })
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.IsLocked) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsLocked)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <label class="control-label col-md-2">Contact By</label>
                <div class="col-md-10">
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByHRM, new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByHRM)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByEmail)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByEmail)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByFax)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByFax)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByPhone)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByPhone)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByMail) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByMail)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ManualFax) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ManualFax)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Password, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Password, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Password, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.CPSO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.CPSO, new { htmlAttributes = new { @class = "form-control " + @Model.cpso_ro } })
                    @Html.ValidationMessageFor(model => model.CPSO, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-xs ext-cpso-btn">Check CPSO in HRM</button>
                </div>
                <div class="col-md-6">
                    <a target="_blank" href="https://doctors.cpso.on.ca/?v=doctors">CPSO Doctor Search</a>
                </div>
            </div>
        </div>

    </div><!--Modal body ends-->

                            <div class="modal-footer ">
                                <button type="button" class="btn btn-default btn-sm btn-primary" style="margin-right: 48px;" onclick="return printDoctorAddressLabel(@Model.Id)">Print Address Label</button>
                                @if (Model.UserCanEdit)
                                {
                                    <button type="submit" class="btn btn-default btn-sm btn-primary">Save</button>
                                }
                                else
                                {
                                    <button type="submit" class="btn btn-default btn-sm btn-primary" title="Only Admin can edit doctor." disabled>Save</button>
                                }
                                <button type="button" class="btn btn-default btn-sm " data-dismiss="modal">Close</button>
                            </div>

                            <script type="text/javascript">
                                setTimeout(function () { $('.focus :input').focus() }, 1000);

                                function printDoctorAddressLabel(externalDoctorId) {
                                    var addressIds = $("#frm-edit-ext-doctor input[name='addressCheckboxPrinting']:checkbox:checked").map(function () {
                                        return this.value
                                    }).get();

                                    window.open("Externaldoctors/PrintDoctorAddressLabel?externalDoctorId=" + externalDoctorId + "&addressIds=" + addressIds);
                                    return false;
                                }
                            </script>
                            }
