@model Cerebrum.ViewModels.Common.VMExternalDoctor

@using (Html.BeginForm("Create", "ExternalDoctors", new { area = "" }, FormMethod.Post, true, new { @id = "frm-add-ext-doctor" }))
{
    @Html.ModalHeader((object)("Add Doctor" + "<span class='green' style='display:inline-block;margin-left:200px;font-weight:700;'>" + @Model.message ?? "" + "</span>"))

    <div class="modal-body content-height500">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => (object)model.Id)

        <div class="form-horizontal">

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OHIP, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.OHIP, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.OHIP, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PhysicianType, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @foreach (var ptype in Enum.GetNames(typeof(AwareMD.Cerebrum.Shared.Enums.PhysicianType)))
                    {
                        @Html.RadioButtonFor(model => model.PhysicianType, ptype, new { htmlAttributes = new { @class = "form-control" } }) @ptype
                        <span style="padding-right: 12px;"></span>
                    }
                    @Html.ValidationMessageFor(model => model.PhysicianType, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.FirstName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MiddleName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.MiddleName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Phone, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.Phone, new { htmlAttributes = new { @class = "form-control ex-doc-phone-cr" } })
                        @Html.ValidationMessageFor(model => model.Phone, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor(model => model.PhoneExt, htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">
                        @Html.EditorFor(model => model.PhoneExt, new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                        @Html.ValidationMessageFor(model => model.PhoneExt, "", new { @class = "text-danger" })
                    </div>
                    <div class="pull-left">
                        @*<button style="margin-left:5px;margin-top:2px;" class="btn btn-xs btn-default ext_phone1" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add / Edit Phone" href="#"><span class="glyphicon glyphicon-pencil text-primary"></span></button>*@
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Fax, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Fax, new { htmlAttributes = new { @class = "form-control ex-doc-fax-cr" } })
                    @Html.ValidationMessageFor(model => model.Fax, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Address, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2 pull-left">
                    @*<button style="margin-left:5px;margin-top:2px;" class="btn btn-xs btn-default ext_addr1" data-cb-tp="tooltip" data-cb-tp-placement="bottom"  data-cb-tp-title="Add / Edit Address" href="#"><span class="glyphicon glyphicon-pencil text-primary"></span></button>*@
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.City, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.City, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.City, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.PostalCode, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.PostalCode, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PostalCode, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ProvinceId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.ProvinceId, new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.ProvinceId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ProvinceId, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor(model => model.CountryId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.CountryId, new SelectList(ViewBag.LKCountries, "Value", "Text", Model.CountryId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CountryId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                </div>
            </div> 

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Comment, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Comment, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Comment, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.HRMId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.HRMId, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.HRMId, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.IsActive, new { htmlAttributes = new { @readonly = "readonly" } }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.IsLocked) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsLocked)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <label class="control-label col-md-2">Contact By</label>
                <div class="col-md-10">
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByHRM, new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByHRM)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByEmail)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByEmail)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByFax)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByFax)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByPhone)<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByPhone)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => (object)model.ContactByMail) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByMail)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Password, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Password, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Password, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.CPSO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.CPSO, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.CPSO, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-xs">Check CPSO in HRM</button>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12 text-center green">
                @*<span id="ex_cr_msgId">@Html.LabelFor(x => x.message, @Model.message ?? "")</span>*@
            </div>
        </div>

    </div><!--Modal body ends-->

    <div class="modal-footer">
        <button type="submit" @((Model.success == true) ? "disabled" : "") class="btn btn-default btn-sm btn-primary">Add Doctor</button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
    </div>
}
