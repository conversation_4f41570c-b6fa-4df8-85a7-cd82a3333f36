@model  Cerebrum.ViewModels.VP.VP_VM
<script type="text/javascript">
    function loadPatientMenu() {
        var url = '@Url.Action("GetPatientInfoVP", "patients", new { area = "" })';
        $("#patient-info-container").html('<img src="../../Content/fancybox_loading.gif" />');
        $.ajax({
            method: "POST",
            url: url,
            data: {patientId:@Model.PatientID,appointmentID:@Model.AppointmentID,testID:@Model.TestID},
            headers: { "RequestVerificationToken": requestVerificationToken },
            async: true,
            processData: undefined,
            contentType: undefined,
            beforeSend: function () { $("#ajax-loader").show(); },
            complete: function () { $("#ajax-loader").hide(); },
            success: function (result) { 
                $('#patient-info-container').html(result);
            },
            error: function (xhr, thrownError) { 
                var errorMessage = '<span class="text-danger">An error occured while loading patient information</span>';
                $('#patient-info-container').html(errorMessage);
            }
        });
    }

    $(function () {
        loadPatientMenu();
    });
</script>

<div class="container-fluid">
    <div id="patient-info-container" class="__776543"></div>
</div>

<!-- vplinks -->


<div class="container-fluid spacer-top-10">
    @(await Html.PartialAsync("BillingCode", (object)(object)Model))

    <div class="_placeHolder_VPlinksV _34564567" style="float:left; padding-right:50px"></div>
   
    <div class="" style="float:left">
        <a class="btn btn-default btn-sm" href="javascript:void(0)" id="_swictStateVPAllergy">&nbsp;</a>
        <a class="btn btn-default btn-sm" href="javascript:void(0)" id="_swictStateVPMedication">&nbsp;</a>
    </div>

    <div class="_placeHolder_VPlinksV2 _34564567345" style="float:right;"></div>
</div>




<div class="container-fluid spacer-top-10">
    <div class="row __H789MED">
        <div id="div-medications"><img src="Content/fancybox_loading.gif" style="margin-left: 15px" /></div>
    </div>
</div>

<script>
    var allergyVisible = true;
    var medicationVisible = true;
        $(document).ready(function(){
            var clone9 = $('._toCloneIntoVP').clone();
            clone9.show().appendTo($("._placeHolder_VPlinksV"));

            var clone9_2 = $('._toCloneIntoVP2').clone();
            clone9_2.appendTo($("._placeHolder_VPlinksV2"));

            LoadMedications();

            //var stateAllergy = true;
            //var stateMedication = true;
            var lblAllergy ="Hide Allergies" ;
            var lblMedication ="Hide Medications" ;
            allergyVisible = @(Model.AllergyVisible ? "true" : "false" );
            medicationVisible = @(Model.MedicationVisible ? "true" : "false" );

            if (!allergyVisible) {
                //stateAllergy = false;
                lblAllergy = 'Show Allergies';
                $('.div-medications-allergies').fadeOut('easeInOutExpo');
            }
            $('#_swictStateVPAllergy').html(lblAllergy);

            if (!medicationVisible) {
                //stateMedication = false;
                lblMedication = 'Show Medications';
                $('.div-medications-medications').fadeOut('easeInOutExpo');
            }
            $('#_swictStateVPMedication').html(lblMedication);

            
            var medicationHeight = function(){
                var height = 170;
                var medicationSelector = '.__H789MED';

                if (!allergyVisible && !medicationVisible){
                    height = 0;
                }                
                $(medicationSelector).css('min-height',height+'px');
            };               
            
            $('#_swictStateVPAllergy').on('click', function (e) {
                e.preventDefault();
                if (allergyVisible) {
                    lblAllergy = 'Show Allergies';
                    $('.div-medications-allergies').fadeOut('easeInOutExpo');
                    allergyVisible = false;
                }
                else {
                    lblAllergy = 'Hide Allergies';
                    $('.div-medications-allergies').fadeIn('easeInOutExpo');
                    allergyVisible = true;
                }
                medicationHeight();
                $('#_swictStateVPAllergy').html(lblAllergy);
            });

            $('#_swictStateVPMedication').on('click', function (e) {
                e.preventDefault();
                if (medicationVisible) {
                    lblMedication = 'Show Medications';
                    $('.div-medications-medications').fadeOut('easeInOutExpo');
                    medicationVisible = false;
                }
                else {
                    lblMedication = 'Hide Medications';
                    $('.div-medications-medications').fadeIn('easeInOutExpo');
                    medicationVisible = true;
                }
                medicationHeight();
                $('#_swictStateVPMedication').html(lblMedication);
            });
        });
</script>
