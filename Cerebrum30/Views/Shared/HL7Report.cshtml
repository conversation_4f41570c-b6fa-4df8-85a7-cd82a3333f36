
@model Cerebrum.ViewModels.HL7.HL7ReportVM
@using Cerebrum30.Utility;
<link href="~/Areas/Labs/CSS/olis-viewer-print.css" rel="stylesheet" />
<script src="~/Scripts/ShowSensitiveResult.js"></script>

<div>
    @if (Model == null)
    {
        <div><h2> Report Not Found</h2> </div>
    }
    else
    {
        { await Html.RenderPartialAsync("~/Views/Shared/HL7ReportHeader.cshtml", (object)Model); }

        bool anyconsentblock = Model.HL7ReportVersions.Cast<dynamic>().Any((System.Func<dynamic, bool>)(a => a.consentBlock > 0 || a.HL7Results.Any(cb => cb.showResult == false)));
        if (anyconsentblock)
        {
            <input id="btn-consent1" type="button" class="btn btn-default btn-sm btn-primary" data-orderID='@Model.ORC4' data-IDNumber1='@Model.physicianNo' data-PatientRecordId='@Model.HL7Patient.PatientRecordId' value="Patient Consent override">
            @*@(await Html.PartialAsync("HL7ConsentBlock", (object)new Cerebrum.ViewModels.OLIS.VMConsentReportFor { AccessionNo=Model.accessionNumber,PhysicianNo=Model.physicianNo,HL7PatientId=Model.HL7PatientId}))*@
        }
        { await Html.RenderPartialAsync("~/Views/Shared/HL7ReportMarkedSeen.cshtml", (object)Model.MarkedSeenBy); }
        <div>
            <ul class="nav nav-tabs">
                @{
                    int totalRpts = Model.HL7ReportVersions.Count();
                    int count = 1;
                    foreach (var v in Model.HL7ReportVersions.OrderByDescending(o => o.createdDate))
                    {
                        if ((count++) == 1)
                        {
                            <li class="active">
                                <a class="hl7-version" data-teststatus="@v.status" data-hl7reportid="@v.HL7ReportId" data-id="@v.Id"
                                   data-toggle="tab" href="#V@(v.Id)">Version@(totalRpts--)</a>
                                <input type="hidden" id="HL7_ReportVersionID" value="@v.Id" />
                                <input type="hidden" id="HL7_ReportID" value="@v.HL7ReportId" />
                            </li>
                        }
                        else
                        {
                            <li>
                                <a class="hl7-version" data-teststatus="@v.status" data-hl7reportid="@v.HL7ReportId" data-id="@v.Id"
                                   data-toggle="tab" href="#V@(v.Id)">Version@(totalRpts--)</a>
                            </li>
                        }
                    }
                }
            </ul>
            <div class="tab-content" style="height:500px;overflow-y:scroll;">
                @{
                    int ttlrpt = Model.HL7ReportVersions.Count();
                    int rptcnt = 1;
                    foreach (var r in Model.HL7ReportVersions.OrderByDescending(o => o.createdDate))
                    {
                        string active = (rptcnt++) == 1 ? "in active" : "";
                        <div id="V@(r.Id)" class="tab-pane fade @active">
                            @(await Html.RenderPartialAsync("~/Views/Shared/HL7ReportVersion.cshtml", (object)r));
                            }
                        </div>
                    }
                }
            </div>
        </div>
                    }


</div>

@(await Html.RenderPartialAsync("HL7ShowSensitiveResult"));
}
<script type="text/javascript">

    $(document).on('click', '.hl7-print', function (e) {
        e.preventDefault();

        var id = $(this).data('version');
        var patient = document.getElementById('patient-info');
        var divToPrint = document.getElementById(id);

        var newWin = window.open('', 'Print-Window');

        newWin.document.open();

        newWin.document.write('<html><body onload="window.print()">' + patient.innerHTML + '<br/>' + divToPrint.innerHTML + '</body></html>');

        newWin.document.close();

        setTimeout(function () { newWin.close(); }, 10);

    });


    function OnVersionClick(status, rid, rvid) {
        $("#reportStatus").html(status);
        $("#HL7_ReportID").val(rid);
        $("#HL7_ReportVersionID").val(rvid);
        $(document).trigger("HL7ReportVersionChange");
    }
    function MarkSeen() {
        $.post("/Labs/HL7/MarkSeenHL7Report/", { hl7RptId: $("#reportID").val(), hl7RptVersionId: $("#reportVersionID").val() }, function (data) {
            alert(data);
        });
    }
    $(document).on('click', '.hl7-version', function (e) {
        e.preventDefault();
        var Data = $(this).data();
        $("#reportStatus").html(Data.teststatus);
        $("#HL7_ReportID").val(Data.hl7reportid);
        $("#HL7_ReportVersionID").val(Data.id);
        $(document).trigger("HL7ReportVersionChange");
    });
    $(document).on('click', '.btn-HL7-Result-Note', function (e) {

        e.preventDefault();
        var modalID = '#hl7CommentModal';
        var url = '/labs/hl7/addcomment/'
        var itemId = $(this).data("value");

        $('#ajax-loader').show();
        $.ajax({
            url: url,
            type: 'GET',
            cache: false,
            data:
                {
                    hl7resultid: itemId
                },
            complete: function () {
                $('#ajax-loader').hide();
            },
            success: function (data) {
                loadCBModal(modalID, data, "modal-md")
            },
            error: function (jqXHR, textStatus, errorThrown) {
                checkAjaxError(jqXHR);
            }
        });

    });


    $(document).on('submit', '#frm-add-hl7-comment', function (e) {
        e.preventDefault();
        var modalID = "#hl7CommentModal";
        var modalContent = "##hl7CommentModal-ct";

        removeErrorNotifications();

        $('.modal-submit-btn').prop('disabled', true);
        $('#ajax-loader').show();
        $.ajax({
            url: this.action,
            type: this.method,
            data: $(this).serialize(),
            complete: function () {
                $('.modal-submit-btn').prop('disabled', false);
                $('#ajax-loader').hide();
            },
            success: function (result) {

                if (result.success) {
                    hideModal(modalID);
                    showNotificationMessage("success", result.message);
                    location.reload();
                }
                else {
                    $(modalContent).html(result);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                checkAjaxError(jqXHR);
            }
        });


    });
    $(document).on('click', '#btn-consent1', function (e) {
        e.preventDefault();

        var _params = $.param($(this).data());
        window.location = "/Labs/OLIS/OLISReportConsentReinstatement?" + _params;
    });
</script>