@model Cerebrum.ViewModels.Patient.VMPatientNotesMain
@{
    ViewBag.Title = "Patient Notes";
    ViewBag.ModuleName = "Patient - Notes";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
}
@section customcss{   
    
}

@section patientinfo{

   @Html.GetPatientInfo((object)Model.PatientId, (object)(object)(object)0, (object)(object)0, (object)0)

}

@{ await Html.RenderPartialAsync("_patientNotesList", (object)Model.PatientNotes); }
