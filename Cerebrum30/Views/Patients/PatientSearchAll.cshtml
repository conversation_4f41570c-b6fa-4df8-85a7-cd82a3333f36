@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientInfo>
@{
    ViewBag.Title = "Patient Search";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";    
}
@section customcss{   
    <link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
    <link href="~/Areas/Schedule/Content/appointments-modal.css" rel="stylesheet" />
}



<div class="row">
    <div class="col-md-6">
        <h3>Patient Search</h3>
    </div>
    <div class="col-md-6 text-right">
        <a class="btn btn-default btn-sm btn-add-patient btn-h3-top-align"><span class="default-text-color"> </span> Add Patient</a>
    </div>
</div>

<div style="margin-bottom:10px;" class="row">

    @using (Html.BeginForm("patientsearchall", "patients", new { area = "" }, FormMethod.Get, true, new { @class = "form-inline" }))
    {
        var search = !String.IsNullOrWhiteSpace(ViewBag.AllSearch) ? ViewBag.AllSearch : "";
        <div class="form-group form-group-sm">
            <div class="col-md-4">
                <input name="patientSearchall" id="patientSearchall" type="text" class="form-control" placeholder="Patient Search" value="@search">
            </div>
        </div>
        <button type="submit" class="btn btn-default btn-xs">Search</button>
    }

</div>



<div class="panel panel-info">
    <div class="panel-heading">Results (@Model.Count())</div>
    <table id="tbl-patient-search" class="table table-bordered">
        <tr>       
            <th>
                @Html.DisplayNameFor(model => model.LastName)
            </th>    
            <th>
                @Html.DisplayNameFor(model => model.FirstName)
            </th>   
            
            <th>
                @Html.DisplayNameFor(model => model.MiddleName)
            </th>                        
            <th>
                @Html.DisplayNameFor(model => model.DateOfBirth)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.OHIP)
            </th>  
            <th>
                Status
            </th>           
            <th></th>
        </tr>
        @foreach (var item in Model) {
            <tr>                    
                <td>
                    @item.LastName
                </td>
                <td>
                    @item.FirstName
                </td>
                <td>
                    @item.MiddleName
                </td>
                <td>
                    @item.DateOfBirth
                </td>
                <td>
                    @item.OHIP @item.OHIPVersionCode
                </td>
                <td>
                    @item.StatusStr
                </td>
                <td>              
                    <div class="btn-popover-container">
                        <button type="button" class="btn btn-default btn-xs popover-btn">
                            <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                        </button>
                        <div class="btn-popover-title">
                            <span class="default-text-color">Patient Menu</span>
                        </div>
                        <div class="btn-popover-content">
                            @*@{Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = item.PatientId }); }*@

                            @{
                                var patientMenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();
                                patientMenu.PatientId = item.PatientId;
                                patientMenu.AppointmentId = 0;
                                patientMenu.Practiceid = item.PracticeId;
                            }
                            @{ await Html.RenderPartialAsync("_PatientMenu", (object)patientMenu); }
                        </div>
                    </div>                   
                </td>
            </tr>
}

    </table>
</div>