@model Cerebrum.ViewModels.Patient.VM_Enrollment_n
<style>
    #enrollList td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        /*font-weight: 700;*/
    }

        #enrollList td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #enr_d_dialog input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }

    .t_a {
        height: 50px !important;
    }
</style>

@using (Html.BeginForm("enrollment", "Patients", new { area = "" }, FormMethod.Post, true, new { @id = "enrollment_id" }))
{
    @Html.ModalHeader("Add Enrollment")
    @Html.HiddenFor(x  => (object)x.ed_type)
    @Html.HiddenFor(x  => (object)x.ed_id)
    @Html.HiddenFor(x  => (object)x.ed_patientId)
    @Html.HiddenFor(x  => (object)x.ed_mrpId)
    @Html.HiddenFor(x  => (object)x.isThereUnterminatedEnrl)

    <span class="ui-helper-hidden-accessible"><input type="text" /></span>
    
    <div id="enr_d_dialog" title="Add Doctors Enrollment Information" class="modal-body container form-group form-group-sm" @*style="padding-left:40px;padding-right:40px;"*@>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.ed_enrollmentDate)
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ed_enrollmentDate), new { htmlAttributes = new { @class = "form-control et_date", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.ed_enrollmentTerminationDate)
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ed_enrollmentTerminationDate), new { htmlAttributes = new { @class = "form-control et_date", tabindex = 5 } })
            </div>
        </div>

        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.ed_terminationReason)
            </div>
            @*<div class="col-md-1"> </div>*@
            <div class="col-md-10">
                @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.terminationReasonId), new SelectList(ViewBag.LKTermReasons, "Value", "Text", Model.terminationReasonId), new { @class = "form-control" })
                @*@Html.EnumDropDownListFor(model => model.ed_terminationReason, htmlAttributes: new { @class = "form-control", tabindex = 7 })*@
            </div>
        </div>

        <div class="row marginTop2">
            <div class="col-md-2">
               
            </div>
            <div class="col-md-10" style="color:green;">
                <span id="enrl_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.ed_message), @Model.ed_message ?? "", new {})</span>
            </div>
            @*<div class="form-group">
                <div class="col-md-12 text-center" style="color:green;">
                    <span id="enrl_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.ed_message), @Model.ed_message ?? "", new {})</span>

                </div>
            </div>*@
        </div>
        <div class="row marginTop2"   style="margin-bottom:9px">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button class="btn btn-default marginTop2 marginBott2" type="button" id="newEnrll">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2 en_submit_class" type="submit" id="en_submit" autofocus>Save</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                @*<div class="col-md-1">

                </div>*@
                <div class="col-md-12 @*pull-left*@">
                    <table class="table" id="enrollList" name="enrollList" @*style="font-size: 12px;"*@>
                        @if (Model != null && Model.ed_List != null && Model.ed_List.Count > 0)
                {
                            <thead>
                                <tr class="vertical-center" style="background-color:#ddd;">
                                    <th width="20%">Enrollment Date</th>
                                    <th width="20%">Termination Date</th>
                                    <th width="35%">Reason</th>
                                    <th width="25%">Enrolled to Physician</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.ed_List.Count; i++)
                                {
                                    <tr>
                                        <td class="enList">
                                            @Html.DisplayFor(model => (object)model.ed_List[i].name)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.ed_List[i].id), new { @id = Model.ed_List[i].id })
                                        </td>
                                        <td class="enList">
                                            @Html.DisplayFor(model => (object)model.ed_List[i].name1)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.ed_List[i].id), new { @id = Model.ed_List[i].id })
                                        </td>
                                        <td class="enList">
                                            @Html.DisplayFor(model => (object)model.ed_List[i].value)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.ed_List[i].id), new { @id = Model.ed_List[i].id })
                                        </td>
                                        <td class="enList">
                                            @Html.DisplayFor(model => (object)model.ed_List[i].name2)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.ed_List[i].id), new { @id = Model.ed_List[i].id })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>
    <br /><br /><br />

}
