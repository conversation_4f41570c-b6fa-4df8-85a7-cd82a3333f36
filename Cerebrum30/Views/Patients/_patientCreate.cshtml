@model Cerebrum.ViewModels.Patient.VMPatientDemo
<style>
    .required {
        color: #a94442;
    }

    .inline {
        display: inline-block;
    }

    .displayBlock {
        display: block;
    }

    #frm-add-patient input[type=checkbox], input[type=radio] {
        margin: 0px !important;
    }

    .modal-footer2 {
        padding-top: 15px;
        border-top: 1px solid #e5e5e5;
    }

    #Notes {
        min-height: 70px !important;
    }

    .modal-body table {
        border: 3px solid #ced2ca !important;
    }
</style>
@using (Html.BeginForm("Create", "Patients", new { area = "" }, FormMethod.Post, true, new { @id = "frm-add-patient" }))
{
    @*<script src="~/Scripts/cerebrum3-patient.js"></script>*@
    var attributesDisabled = Model.IsPharmacyFieldsDisabled ? new { htmlAttributes = new Dictionary<string, object> { ["class"] = "form-control", ["disabled"] = "disabled" } } : new { htmlAttributes = new Dictionary<string, object> { ["class"] = "form-control" } };
    var attributesDisabledFax_p_p = Model.IsPharmacyFieldsDisabled ? new { htmlAttributes = new Dictionary<string, object> { ["class"] = "form-control fax_p_p", ["disabled"] = "disabled" } } : new { htmlAttributes = new Dictionary<string, object> { ["class"] = "form-control fax_p_p" } };
    @Html.ModalHeader("Add Patient")
    <style>
        .text-danger {
            /*font-size: 10px;*/
            display: block;
        }

        .activePhColor {
            background-color: #e2dfdf;
        }
    </style>
    <div class="modal-body content-height500">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model  => (object)model.PracticeId)
        @Html.HiddenFor(model  => (object)model.PatientRecordId)
        @Html.HiddenFor(model  => (object)model.FamDoctorId)
        @Html.HiddenFor(model  => (object)model.ReferralDoctorId)
        @Html.HiddenFor(model  => (object)model.AssociatedDoctorId)
        @Html.HiddenFor(model  => (object)model.MainDoctorId)
        @Html.HiddenFor(m  => (object)m.activeOld)

        <div id="dialog_dem_addr" style="z-index:1000;"></div>
        <div id="dialog_dem_phone" style="z-index:1000;"></div>
        <div id="dialog_cont" style="z-index:1000;"></div>
        <div id="dialog_cont_ph" class="dialog_cont_ph_class" style="z-index:1000;"></div>
        <div id="dialog_enroll" style="z-index:1000;"></div>
        <div id="dialog_cohort" style="z-index:1000;"></div>
        <div id="dialog_mrn" style="z-index:1000;"></div>
        <div id="dialog_refdoctor" style="z-index:1000;"></div>
        @*@Html.ValidationSummary(false, "", new { @class = "text-danger" })*@

        <!-- --------------------  -->
        <div class="row" style="background-color:#fff">
            <div class="col-sm-12"><div class="form-group form-group-sm col-sm-12">All fields marked with an asterisk (*) are required</div></div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.SalutationId)"> @Html.DisplayNameFor(model => model.SalutationId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.SalutationId), new SelectList(ViewBag.LKSalutations, "Value", "Text", Model.SalutationId), new { @class = "form-control salutation" })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label highlight" title="@Html.DisplayNameFor(model => model.LastName)"> @Html.DisplayNameFor(model => model.LastName) *</label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.LastName), new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label highlight" title="@Html.DisplayNameFor(model => model.FirstName)"> @Html.DisplayNameFor(model => model.FirstName) *</label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FirstName), new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.MiddleName)"> @Html.DisplayNameFor(model => model.MiddleName) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MiddleName), new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PreferredName)"> @Html.DisplayNameFor(model => model.PreferredName) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PreferredName), new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Alias)"> @Html.DisplayNameFor(model => model.Alias) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Alias), new { htmlAttributes = new { @class = "form-control" } })

                </div>
                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label displayBlock">&nbsp; </label>
                    @Html.EditorFor(model => (object)model.UseAliases) <span class="checkbox-text">@Html.DisplayNameFor(model => model.UseAliases)</span>
                </div>
            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label"> @Html.DisplayNameFor(model => model.DateOfBirth) (mm/dd/yyyy)</label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.DateOfBirth), new { htmlAttributes = new { @class = "form-control dob_clas" } })
                </div>
                <div class="grp">
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Ohip)"> @Html.DisplayNameFor(model => model.Ohip) </label>&nbsp;&nbsp;&nbsp;&nbsp;
                        <label>@Html.EditorFor(model => (object)model.skipOHIPCheck) <span class="checkbox-text">@Html.DisplayNameFor(model => model.skipOHIPCheck)</span></label>
                        <br />
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Ohip), new { htmlAttributes = new { @class = "form-control numbersOnly inline", maxlength = "10" } })
                        <span id="ohip_str" class="" style="font-size:8px;color:red;" hidden>*</span>
                    </div>

                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Version)">@Html.DisplayNameFor(model => model.Version)</label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Version), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ProvinceHC_Id)">@Html.DisplayNameFor(model => model.ProvinceHC_Id)</label>
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceHC_Id), new SelectList(ViewBag.LKProvincesHC, "Value", "Text", Model.ProvinceHC_Id), new { @class = "form-control provinceHC" })
                    </div>



                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.IssueDate)">@Html.DisplayNameFor(model => model.IssueDate)</label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.IssueDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ValidDate)">@Html.DisplayNameFor(model => model.ValidDate)</label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ValidDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ExpiryDate)">@Html.DisplayNameFor(model => model.ExpiryDate)</label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExpiryDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="">&nbsp;</label>
                        <button style="margin-left:5px;margin-top:2px;" class="btn btn-xs btn-default checkOHIPClk" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="OHIP CHECK" href="#">OHIP CHECK</button>
                    </div>
                </div>
                @*</div>*@
            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.GenderId)"> @Html.DisplayNameFor(model => model.GenderId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.GenderId), new SelectList(ViewBag.LKGenders, "Value", "Text", Model.GenderId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.RMB)"> @Html.DisplayNameFor(model => model.RMB) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.RMB), new { htmlAttributes = new { @class = "form-control rmbHC" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    @*<label class="control-label" title="RMB Province">RMB Province</label>
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceRMB_Id), new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.ProvinceRMB_Id), new { @class = "form-control" })*@
                </div>

                @*<div class="form-group form-group-sm col-sm-1">
                    </div>*@

                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.SIN)"> @Html.DisplayNameFor(model => model.SIN) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.SIN), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.ActiveId)"> @Html.DisplayNameFor(model => model.ActiveId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ActiveId), new SelectList(ViewBag.LKActives, "Value", "Text", Model.ActiveId), new { @class = "form-control" })
                </div>

            </div>
        </div>

        <hr />

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.MainDoctor)"> @Html.DisplayNameFor(model => model.MainDoctor) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MainDoctor), new { htmlAttributes = new { @class = "form-control demographic-doctors" } })
                </div>
                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label" title="Enrollment Date">Enroll. Date</label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.enrollDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                </div>

                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label" title="Enrollment Termination Date">Enroll. T. Date</label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.termDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    @*<input type="text" class="form-control custom-input-sm date-picker" /    >*@
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="Termination Reason"> Termination Reason </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.terminationReason), new SelectList(ViewBag.LKTerminationReasons, "Value", "Text", Model.terminationReason), new { @class = "form-control" })
                </div>



                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.FamDoctor)"> @Html.DisplayNameFor(model => model.FamDoctor) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FamDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                </div>

                @*<div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ReferralDoctor)"> @Html.DisplayNameFor(model => model.ReferralDoctor) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ReferralDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                    </div>*@

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.AssociatedDoctor)"> @Html.DisplayNameFor(model => model.AssociatedDoctor) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.AssociatedDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                </div>
            </div>
        </div>

        <hr />

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.CellPhone)"> @Html.DisplayNameFor(model => model.CellPhone) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.CellPhone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Phone)"> @Html.DisplayNameFor(model => model.Phone) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Phone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                </div>
                @*<div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ExtentionPhone)"> @Html.DisplayNameFor(model => model.ExtentionPhone) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExtentionPhone), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                    </div>*@
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="Notes"> Notes </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.notesPh), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PhoneContact)"> @Html.DisplayNameFor(model => model.PhoneContact) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PhoneContact), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.ExtentionContact)"> @Html.DisplayNameFor(model => model.ExtentionContact) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExtentionContact), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="">Preferred Phone</label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.preferredPhoneId), new SelectList(ViewBag.LKPhoneTypes, "Value", "Text", Model.preferredPhoneId), new { @class = "form-control prefPhActive" })
                </div>
            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Email)"> @Html.DisplayNameFor(model => model.Email) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Email), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.ConsentEmailId)">@Html.DisplayNameFor(model => model.ConsentEmailId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ConsentEmailId), new SelectList(ViewBag.LKYesNo, "Value", "Text", Model.ConsentEmailId), new { @class = "form-control custom-input-xs" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <br />
                    <label>@Html.CheckBoxFor((System.Func<dynamic, object>)(model => model.noEmail), Model.noEmail) <span class="checkbox-text">@Html.DisplayNameFor(model => model.noEmail)</span></label>
                    @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.hasEmail), true)
                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>

            </div>
        </div>


        <!-- next -of-kin -->
        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    Other Contact Information (e.g.: Next of kin, power of attorney, ...etc.) :
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.contactPurposeId)"> @Html.DisplayNameFor(model => model.contactPurposeId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.contactPurposeId), new SelectList(ViewBag.LKContactPurposes, "Value", "Text", Model.contactPurposeId), new { @class = "form-control" })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.NextOfkin_LN)"> @Html.DisplayNameFor(model => model.NextOfkin_LN) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_LN), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.NextOfkin_FN)"> @Html.DisplayNameFor(model => model.NextOfkin_FN) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_FN), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Kin_phone)"> @Html.DisplayNameFor(model => model.Kin_phone) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Kin_phone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.contactPhoneTypeId)"> @Html.DisplayNameFor(model => model.contactPhoneTypeId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.contactPhoneTypeId), new SelectList(ViewBag.LKPhoneTypes, "Value", "Text", Model.contactPhoneTypeId), new { @class = "form-control" })
                </div>
            </div>
        </div>
        <!-- /next -of-kin -->

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Address)"> @Html.DisplayNameFor(model => model.Address) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Address), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.City)"> @Html.DisplayNameFor(model => model.City) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.City), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PostalCode)"> @Html.DisplayNameFor(model => model.PostalCode) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PostalCode), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.ProvinceId)"> @Html.DisplayNameFor(model => model.ProvinceId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceId), new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.ProvinceId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.CountryId)"> @Html.DisplayNameFor(model => model.CountryId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.CountryId), new SelectList(ViewBag.LKCountries, "Value", "Text", Model.CountryId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.AddressTypeId)"> @Html.DisplayNameFor(model => model.AddressTypeId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.AddressTypeId), new SelectList(ViewBag.LKAddressTypes, "Value", "Text", Model.AddressTypeId), new { @class = "form-control" })
                </div>

            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="">Address 2 </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Address2), new { htmlAttributes = new { @class = "form-control" } })
                </div>


                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="">City 2 </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.City2), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="">Postal Code 2 </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PostalCode2), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="">Province 2 </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceId2), new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.ProvinceId2), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="">Country 2 </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.CountryId2), new SelectList(ViewBag.LKCountries, "Value", "Text", Model.CountryId2), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.AddressTypeId2)"> @Html.DisplayNameFor(model => model.AddressTypeId2) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.AddressTypeId2), new SelectList(ViewBag.LKAddressTypes, "Value", "Text", Model.AddressTypeId2), new { @class = "form-control" })
                </div>

            </div>
        </div>

        <hr />

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PreferedLanguageId)"> @Html.DisplayNameFor(model => model.PreferedLanguageId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.PreferedLanguageId), new SelectList(ViewBag.LKLanguagesTb, "Value", "Text", Model.PreferedLanguageId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.OfficialLanguageId)"> @Html.DisplayNameFor(model => model.OfficialLanguageId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.OfficialLanguageId), new SelectList(ViewBag.LKLanguagesTbO, "Value", "Text", Model.PreferedLanguageId), new { @class = "form-control" })
                    @*<label class="control-label" title="@Html.DisplayNameFor(model => model.chartNumber)"> @Html.DisplayNameFor(model => model.chartNumber) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.chartNumber), new { htmlAttributes = new { @class = "form-control" } })*@
                </div>
                <div class="form-group form-group-sm col-sm-2">

                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PaymentMethodId) "> @Html.DisplayNameFor(model => model.PaymentMethodId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.PaymentMethodId), new SelectList(ViewBag.LKPaymentMethods, "Value", "Text", Model.PaymentMethodId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.InsuranceKindId) "> @Html.DisplayNameFor(model => model.InsuranceKindId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.InsuranceKindId), new SelectList(ViewBag.LKInsuranceTypes, "Value", "Text", Model.InsuranceKindId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.InsuranceCompanyId)"> @Html.DisplayNameFor(model => model.InsuranceCompanyId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.InsuranceCompanyId), new SelectList(ViewBag.LKInsurances, "Value", "Text", Model.InsuranceCompanyId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.chartNumber)"> @Html.DisplayNameFor(model => model.chartNumber) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.chartNumber), new { htmlAttributes = new { @class = "form-control" } })
                </div>

            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title=" @Html.DisplayNameFor(model => model.HospitalId) "> @Html.DisplayNameFor(model => model.HospitalId) </label>
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.HospitalId), new SelectList(ViewBag.LKHospitals, "Value", "Text", Model.HospitalId), new { @class = "form-control" })

                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.MRN)"> @Html.DisplayNameFor(model => model.MRN) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MRN), new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.FederatedId)"> @Html.DisplayNameFor(model => model.FederatedId) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FederatedId), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.FaxPharmacy)"> @Html.DisplayNameFor(model => model.FaxPharmacy) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FaxPharmacy), attributesDisabledFax_p_p)
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PhonePharmacy) "> @Html.DisplayNameFor(model => model.PhonePharmacy) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PhonePharmacy), attributesDisabledFax_p_p)
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.FavoritePharmacy)"> @Html.DisplayNameFor(model => model.FavoritePharmacy) </label>
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FavoritePharmacy), attributesDisabled)
                </div>

            </div>
        </div>


        <hr />

        <!-- next -of-kin -->
        @*<div class="row" style="background-color:#fff">
                <div class="col-sm-12">
                    <div class="form-group form-group-sm col-sm-2">
                       Other Contact Information (e.g.: Next of kin, power of attorney, ...etc.) :
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.contactPurposeId)"> @Html.DisplayNameFor(model => model.contactPurposeId) </label>
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.contactPurposeId), new SelectList(ViewBag.LKContactPurposes, "Value", "Text", Model.contactPurposeId), new { @class = "form-control" })
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.NextOfkin_LN)"> @Html.DisplayNameFor(model => model.NextOfkin_LN) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_LN), new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.NextOfkin_FN)"> @Html.DisplayNameFor(model => model.NextOfkin_FN) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_FN), new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Kin_phone)"> @Html.DisplayNameFor(model => model.Kin_phone) </label>
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Kin_phone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.contactPhoneTypeId)"> @Html.DisplayNameFor(model => model.contactPhoneTypeId) </label>
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.contactPhoneTypeId), new SelectList(ViewBag.LKPhoneTypes, "Value", "Text", Model.contactPhoneTypeId), new { @class = "form-control" })
                    </div>
                </div>
            </div>
            <hr />*@

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-6">
                    <label class="control-label" title="General Notes">General Notes  @*@Html.DisplayNameFor(model => model.Notes)*@ </label>
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control", @rows = 3 })
                </div>

                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>

            </div>
        </div>

        <script>
            $(document).ready(function () {
                $('#ActiveId').prop("disabled", true);;
            });
        </script>



        <!-- -------------------   -->
        <!-- -------------------   -->
        <!-- -------------------   -->
        @*<div class="form-horizontal ">

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.SalutationId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.SalutationId), new SelectList(ViewBag.LKSalutations, "Value", "Text", Model.SalutationId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.SalutationId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.FirstName), htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FirstName), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.LastName), htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.LastName), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.MiddleName), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MiddleName), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.DateOfBirth), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">

                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.DateOfBirth), new { htmlAttributes = new { @class = "form-control dob_clas" } })
                    @Html.ValidationMessageFor(model => model.DateOfBirth, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.GenderId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.GenderId), new SelectList(ViewBag.LKGenders, "Value", "Text", Model.GenderId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.GenderId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PreferredName), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PreferredName), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PreferredName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-offset-2 col-md-3">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.skipOHIPCheck) <span class="checkbox-text">@Html.DisplayNameFor(model => model.skipOHIPCheck)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.skipOHIPCheck, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="pull-left">
                        <button style="margin-left:5px;margin-top:2px;" class="btn btn-xs btn-default checkOHIPClk" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="OHIP CHECK" href="#">OHIP CHECK</button>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Ohip_1), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Ohip_1), new { htmlAttributes = new { @class = "form-control custom-input-xs numbersOnly" } })
                        @Html.ValidationMessageFor(model => model.Ohip_1, "", new { @class = "text-danger" })
                        <span id="ohip_1_str" class="" style="font-size:8px;color:red;" hidden>*Required(4)</span>
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Ohip_2), new { htmlAttributes = new { @class = "form-control custom-input-xs numbersOnly" } })
                        @Html.ValidationMessageFor(model => model.Ohip_2, "", new { @class = "text-danger" })
                        <span id="ohip_2_str" class="" style="font-size:8px;color:red;" hidden>*Required(3)</span>
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Ohip_3), new { htmlAttributes = new { @class = "form-control custom-input-xs numbersOnly" } })
                        @Html.ValidationMessageFor(model => model.Ohip_3, "", new { @class = "text-danger" })
                        <span id="ohip_3_str" class="" style="font-size:8px;color:red;" hidden>*Required(3)</span>
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.Version), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div style="margin-right:7px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Version), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                        @Html.ValidationMessageFor(model => model.Version, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.IssueDate), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.IssueDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                        @Html.ValidationMessageFor(model => model.IssueDate, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:7px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.ValidDate), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ValidDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                        @Html.ValidationMessageFor(model => model.ValidDate, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:7px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.ExpiryDate), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExpiryDate), new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })

                    </div>

                    <div class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.ProvinceHC_Id), htmlAttributes: new { @class = "control-label col-md-2" })
                    </div>
                    <div class="pull-left">
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceHC_Id), new SelectList(ViewBag.LKProvincesHC, "Value", "Text", Model.ProvinceHC_Id), new { @class = "form-control provinceHC" })

                    </div>

                </div>
                <div class="col-md-12" style="text-align: right;">
                    @Html.ValidationMessageFor(model => model.ExpiryDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <hr />

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.MainDoctor), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <div style="width:105px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MainDoctor), new { htmlAttributes = new { @class = "form-control demographic-doctors" } })
                    </div>

                    <div class="clearfix"></div>
                    @Html.ValidationMessageFor(model => model.MainDoctor, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.FamDoctor), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FamDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                    @Html.ValidationMessageFor(model => model.FamDoctor, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.ReferralDoctor), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <div style="width:100px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ReferralDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                    </div>
                    <div class="clearfix"></div>
                    @Html.ValidationMessageFor(model => model.ReferralDoctor, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.AssociatedDoctor), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.AssociatedDoctor), new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                    @Html.ValidationMessageFor(model => model.AssociatedDoctor, "", new { @class = "text-danger" })
                </div>
            </div>

            <hr />
            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Email), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-3">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Email), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.ConsentEmailId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ConsentEmailId), new SelectList(ViewBag.LKYesNo, "Value", "Text", Model.ConsentEmailId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ConsentEmailId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Phone), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <div class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Phone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                        @Html.ValidationMessageFor(model => model.Phone, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.ExtentionPhone), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExtentionPhone), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                        @Html.ValidationMessageFor(model => model.ExtentionPhone, "", new { @class = "text-danger" })
                    </div>

                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PhoneContact), htmlAttributes: new { @class = "control-label col-md-1" })
                <div class="col-md-5">

                    <div style="margin-right:3px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PhoneContact), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                        @Html.ValidationMessageFor(model => model.PhoneContact, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.ExtentionContact), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ExtentionContact), new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                        @Html.ValidationMessageFor(model => model.ExtentionContact, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.CellPhone), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.CellPhone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                        @Html.ValidationMessageFor(model => model.CellPhone, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Address), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <div style="width:205px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Address), new { htmlAttributes = new { @class = "form-control" } })
                    </div>

                    <div class="clearfix"></div>
                    @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.City), htmlAttributes: new { @class = "control-label col-md-1" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.City), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.City, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PostalCode), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PostalCode), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PostalCode, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.ProvinceId), htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-1">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ProvinceId), new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.ProvinceId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ProvinceId, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.CountryId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.CountryId), new SelectList(ViewBag.LKCountries, "Value", "Text", Model.CountryId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CountryId, "", new { @class = "text-danger" })
                </div>
            </div>

            <hr />
            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PreferedLanguageId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.PreferedLanguageId), new SelectList(ViewBag.LKLanguages, "Value", "Text", Model.PreferedLanguageId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PreferedLanguageId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.chartNumber), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.chartNumber), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.chartNumber, "", new { @class = "text-danger" })
                </div>

            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PaymentMethodId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.PaymentMethodId), new SelectList(ViewBag.LKPaymentMethods, "Value", "Text", Model.PaymentMethodId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PaymentMethodId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.InsuranceKindId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-6">
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.InsuranceKindId), new SelectList(ViewBag.LKInsuranceTypes, "Value", "Text", Model.InsuranceKindId), new { @class = "form-control" })

                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.InsuranceCompanyId), htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">

                        @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.InsuranceCompanyId), new SelectList(ViewBag.LKInsurances, "Value", "Text", Model.InsuranceCompanyId), new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.InsuranceCompanyId, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.HospitalId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.HospitalId), new SelectList(ViewBag.LKHospitals, "Value", "Text", Model.HospitalId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.HospitalId, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.MRN), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <div style="width:100px;" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.MRN), new { htmlAttributes = new { @class = "form-control" } })
                    </div>

                    <div class="clearfix"></div>
                    @Html.ValidationMessageFor(model => model.MRN, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor((System.Func<dynamic, object>)(model => model.FederatedId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FederatedId), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FederatedId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.FaxPharmacy), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FaxPharmacy), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                    @Html.ValidationMessageFor(model => model.FaxPharmacy, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.PhonePharmacy), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.PhonePharmacy), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                    @Html.ValidationMessageFor(model => model.PhonePharmacy, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.FavoritePharmacy), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.FavoritePharmacy), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FavoritePharmacy, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.RMB), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.RMB), new { htmlAttributes = new { @class = "form-control rmbHC" } })
                    @Html.ValidationMessageFor(model => model.RMB, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.ActiveId), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.ActiveId), new SelectList(ViewBag.LKActives, "Value", "Text", Model.ActiveId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.ActiveId, "", new { @class = "text-danger" })
                </div>
            </div>
            <hr />

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.NextOfkin_LN), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="margin-right:2px" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_LN), new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.NextOfkin_LN, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:2px" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.NextOfkin_FN), htmlAttributes: new { @class = "control-label col-md-2" })
                    </div>
                    <div style="margin-right:2px" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.NextOfkin_FN), new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.NextOfkin_FN, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:2px" class="pull-left">
                        @Html.LabelFor((System.Func<dynamic, object>)(model => model.Kin_phone), htmlAttributes: new { @class = "control-label col-md-2" })
                    </div>
                    <div style="margin-right:2px" class="pull-left">
                        @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Kin_phone), new { htmlAttributes = new { @class = "form-control fax_p_p" } })
                        @Html.ValidationMessageFor(model => model.Kin_phone, "", new { @class = "text-danger" })
                    </div>

                </div>
            </div>

            <hr />
            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Alias), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.Alias), new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Alias, "", new { @class = "text-danger" })
                </div>


                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => (object)model.UseAliases) <span class="checkbox-text">@Html.DisplayNameFor(model => model.UseAliases)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.UseAliases, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <hr />

            <div class="form-group form-group-sm">
                @Html.LabelFor((System.Func<dynamic, object>)(model => model.Notes), htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>*@
        @*<div class="col-md-2">
                <button class="btn btn-default addPatCohort" type="button" id="addCohort">Add Cohort</button>
            </div>
            <div class="col-md-4" id="cohortsList" style="width:350px;height:65px;background-color:antiquewhite; overflow: auto;">
                @if (Model.addedCohorts != null)
                    {
                        for (int i = 0; i < Model.addedCohorts.Count; i++)
                        {
                            <div class="row">
                                <div class="col-md-1 ch_t" id="@Model.addedCohorts[i].Value">
                                    <input type="checkbox"  class="<EMAIL>[i].Value" checked />
                                </div>
                                <div class="col-md-9">
                                    @Html.LabelFor((System.Func<dynamic, object>)(m => m.addedCohorts[i].Text), Model.addedCohorts[i].Text, new { })
                                </div>
                            </div>
                        }
                    }
            </div>*@
        @*</div>
            </div>*@<!-- end form horizontal-->
    </div><!--Modal body end-->

    <div class="modal-footer">
        <div class="col-sm-9">
            <div class="text-danger" style="text-align: left; font-size: 13px !important; padding-top: 6px;">
                @*VALIDATION CONTAINER*@


                @Html.ValidationMessageFor(model => model.SalutationId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PreferredName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Alias, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.UseAliases, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.DateOfBirth, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.skipOHIPCheck, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Ohip, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Version, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.IssueDate, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ValidDate, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ExpiryDate, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.GenderId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.RMB, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.MainDoctor, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FamDoctor, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ReferralDoctor, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.AssociatedDoctor, "", new { @class = "text-danger" })


                @Html.ValidationMessageFor(model => model.CellPhone, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Phone, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ExtentionPhone, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PhoneContact, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ExtentionContact, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ConsentEmailId, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.City, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PostalCode, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ProvinceId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.CountryId, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.PreferedLanguageId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.chartNumber, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PaymentMethodId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.InsuranceCompanyId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.HospitalId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.MRN, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FederatedId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FaxPharmacy, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PhonePharmacy, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FavoritePharmacy, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.ActiveId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.NextOfkin_LN, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.NextOfkin_FN, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Kin_phone, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.message, "", new { @class = "text-danger" })
                <span id="ohip_str" style="display:none;">*Required ohip(10)</span>
            </div>
        </div>

        <div class="col-sm-3">
            <div>
                <button type="submit" @((Model.success == true) ? "disabled" : "") class="btn btn-default btn-sm btn-primary submitBtn-add-ptn">Add Patient</button>
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>

}

