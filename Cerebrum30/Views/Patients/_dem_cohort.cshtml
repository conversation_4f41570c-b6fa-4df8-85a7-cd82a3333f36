
@model Cerebrum.ViewModels.Cohort.PatientCohortVM
<style>
    #addCohort input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>
<script>
    var DATEFORMAT = "mm/dd/yy";

    $(document).ready(function () {
        $("#patientCohortStartDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true });
        $("#patientCohortEndDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true });

        getPatientCohortClass();
    });

    function getPatientCohortClass() {
        var practiceDoctorId = $("#patientCohortPracticeDoctorId").val();
        if (practiceDoctorId == "") {
            practiceDoctorId = "0";
        }

        var data = { practiceDoctorId: practiceDoctorId };
        var url = "Patients/GetCohortClassByDoctor";
        ajaxCall(url, data, false, function (result) {
            if (result.errorMessage == "") {
                showPatientCohortClass(result);
            } else {
                showMessageModal("error", result.errorMessage, false);
            }
        });

        return false;
    }

    function showPatientCohortClass(result) {
        $("#patientCohortClassId").html("");
        if (result == null || result.CohortClasses.length != 1) {
            $("#patientCohortClassId").append('<option value="">Select Class</option>');
        }
        if (result != null && result.CohortClasses.length > 0) {
            $.each(result.CohortClasses, function (i, cohortClass) {
                $("#patientCohortClassId").append('<option value="' + cohortClass.value + '">' + cohortClass.text + '</option>');
            });
        }

        getPatientCohort();
    }

    function getPatientCohort() {
        var cohortClassId = $("#patientCohortClassId").val();
        if (cohortClassId == "") {
            showPatientCohort(null);
            return false;
        }

        var practiceDoctorId = $("#patientCohortPracticeDoctorId").val();
        if (practiceDoctorId == "") {
            practiceDoctorId = "0";
        }
        var data = { practiceDoctorId: practiceDoctorId, cohortClassId: cohortClassId };
        var url = "Patients/GetCohortByDoctorClass";
        ajaxCall(url, data, false, function (result) {
            if (result.errorMessage == "") {
                showPatientCohort(result);
            } else {
                showMessageModal("error", result.errorMessage, false);
            }
        });

        return false;
    }

    function showPatientCohort(result) {
        $("#patientCohortCohortId").html("");
        if (result == null || result.Cohorts.length != 1) {
            $("#patientCohortCohortId").append('<option value="">Select Cohort</option>');
        }
        if (result != null && result.Cohorts.length > 0) {
            $.each(result.Cohorts, function (i, cohort) {
                $("#patientCohortCohortId").append('<option value="' + cohort.value + '">' + cohort.text + '</option>');
            });
        }
    }

    function checkAjaxError(url, xhr, thrownError) {
        if (xhr.status == 401) {
            window.location = "account/login";
        } else if (xhr.status == 403) {
            showMessageModal("error", "No permission to execute this task", false);
        } else {
            showMessageModal("error", "Error while tryng to call '" + url + "' " + xhr.status + "  " + thrownError, false);
        }
    }
</script>
@using (Html.BeginForm("Dem_cohort", "Patients", new { area = "" }, FormMethod.Post, true, new { @id = "dem_cohort_id" }))
{
    @Html.ModalHeader("Add Cohort")

    @Html.HiddenFor(x => (object)x.patientRecordId)
    <div id="addCohort" class="modal-body container form-group form-group-sm" @*style="padding-left:40px;padding-right:40px;width:950px;"*@>
        <div class="row marginTop2" style="margin-top: 10px;">
            <div class="col-md-2">
                <span>Patient:</span>
            </div>
            <div class="col-md-10 marg_bott height25">
                @Html.EditorFor(model => model.patientName, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Doctor:</span>
            </div>
            <div class="col-md-10">
                @if (Model.PracticeDoctors.Count() == 1)
                {
                    @Html.DropDownList("patientCohortPracticeDoctorId", new SelectList(Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohortClass();" })
                }
                else
                {
                    @Html.DropDownList("patientCohortPracticeDoctorId", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohortClass();" })
                }
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Class:</span>
            </div>
            <div class="col-md-10">
                @if (Model.CohortClasses.Count() == 1)
                {
                    @Html.DropDownList("patientCohortClassId", new SelectList(Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohort();" })
                }
                else
                {
                    @Html.DropDownList("patientCohortClassId", new SelectList(Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohort();" })
                }
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Cohort:</span>
            </div>
            <div class="col-md-10">
                @if (Model.Cohorts.Count() == 1)
                {
                    @Html.DropDownList("patientCohortCohortId", new SelectList(Model.Cohorts, "value", "text"), htmlAttributes: new { @class = "form-control selec_" })
                }
                else
                {
                    @Html.DropDownList("patientCohortCohortId", new SelectList(Model.Cohorts, "value", "text"), "Select Cohort", htmlAttributes: new { @class = "form-control selec_" })
                }
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2 ">
                <span>Started:</span>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control input-sm" id="patientCohortStartDate">
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Terminated:</span>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control input-sm" id="patientCohortEndDate">
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Notes:</span>
            </div>
            <div class="col-md-10">
                <textarea rows="3" style="width: 100%;" id="patientCohortNote" name="patientCohortNote"></textarea>
            </div>
        </div>
        <div class="row marginTop2">           
            <div class="col-md-12 text-center green">
                <span id="coh_messageId">@Model.errorMessage</span>
            </div>
        </div>
        <div class="row" @*style="border-bottom:1px solid #e2dada;"*@>
                <div class="col-md-2 text-left">
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_cohort_submit">Save</button>
                </div>
        </div>
    </div>


}

