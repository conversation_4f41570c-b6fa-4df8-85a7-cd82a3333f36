
@{
    ViewBag.Title = "HCV Response";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>Healthcard Batch Validation Response</h2>
<script src="~/Scripts/moment.min.js"></script>

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"><h4>HCV Response Search</h4></div>
            <div class="panel-body">
                @(await Html.PartialAsync("HCVResponse", (object)new { area = "" }))
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div id="hcv-search-result" class="col-md-12"></div>
</div>
<br />
<br />
<script>
    $(function () {
        $(document).on('click', '#btn-hcv-res-srch', function (e) {
            e.preventDefault();
            $('#frm-search-hcv-response').submit();
        });
        $(document).on('submit', '#frm-search-hcv-response', function (e) {
            e.preventDefault();

            $('#btn-hcv-res-srch').prop('disable', true);
            $('#ajax-loader').show();
            $.ajax({
                url: this.action,
                type: this.method,
                data: $(this).serialize(),
                complete: function () {
                    $('#ajax-loader').hide();
                },
                success: function (result) {
                    $('#hcv-search-result').html(result);
                    $('#btn-hcv-res-srch').prop('disable', false);
                    $('#ajax-loader').hide();
                }
            }).fail(function (jqXHR, textStatus, errorThrown) {
                checkAjaxError(jqXHR, null);
            });
        });
        // Load default
        $('#frm-search-hcv-response').submit();
    });
</script>