
@model IEnumerable<Cerebrum30.Models.DemographicsViewModel>
<table class="table">
    <tr>
        <th></th>
        <th>
            OHIP
        </th>
        <th>
           FullName
        </th>
        
        <th>
            @Html.DisplayNameFor(model => model.gender)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.dateOfBirth)
        </th>

        <th></th>
    </tr>
    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.Hidden("patientRecordId", item.patientRecordId)
            </td>
            <td>
                @item.ohip
            </td>
            <td>
                @item.FullName
            </td>
            
            <td>
                @item.gender
            </td>
            <td>
                @item.dateOfBirth
            </td>
            <td>
                <a href="#">Select</a>
            </td>
        </tr>
    }

</table>

