@{
    ViewBag.Title = "LinkMe";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<style>
body {
    background-color: #f8f8f8 !important;
}

.btn-primary {
    color: #fff;
    background-color: #0495c9 !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
    color: #fff;
    background-color: #00b3db !important;
}
</style>
<div class="row">
    <div class="col-md-12">
        <section id="loginForm">
            @using (Html.BeginForm("LinkMe", "OneIdLoginAnonymous", FormMethod.Post, new { name = "frmLinkMe", id = "frmLinkMe" }))
            {
                @Html.AntiForgeryToken()
                if (ViewBag.ListOfUsers != null)
                {
                    <h2 class="col-lg-offset-1">Login</h2>
                    <div class="form-group">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="col-md-4">
                                    @Html.DropDownList("Users", new SelectList(ViewBag.ListOfUsers, "UserId", "UserName"), new { @class = "form-control", @style = "align: center; width: 250px" })

                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-default btn-primary"
                                            style="margin-right:17px" id="btnLoginAsSelected">
                                        Log in as this user
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>
                }
            }

        </section>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {

        $('#btnLoginAsSelected').on('click', function (e) {
            e.preventDefault();
            document.frmLinkMe.submit();
        });
    });

</script>
@section scripts {
    @Scripts.Render("~/bundles/jqueryval")
}