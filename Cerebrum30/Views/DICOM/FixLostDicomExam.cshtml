@model Cerebrum.ViewModels.RadDicom.VMFixAccessionNumber
@{
    ViewBag.Title = "Manage DICOM Exam";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
}

<h2>Manage DICOM Exam</h2>
<script>
$(document).ready(function () {
        $("#badaccessionlisttable td").click(function () {
            var myCol = $(this).index();
            var $tr = $(this).closest('tr');
            var myRow = $tr.index();
            var column_num = parseInt($(this).index());
            var row_num = parseInt($(this).parent().index());
            document.getElementById("badacceidupdate").value = document.getElementById("badaccessionlisttable").rows[myRow].cells[0].innerHTML;
            document.getElementById("badaccessionnumberupdate").value = document.getElementById("badaccessionlisttable").rows[myRow].cells[1].innerHTML;

   
        });
    });

  $(document).ready(function () {
        $("#goodaccessionlisttable td").click(function () {
            var myCol = $(this).index();
          
            var $tr = $(this).closest('tr');
            var myRow = $tr.index();
            var column_num = parseInt($(this).index());
            var row_num = parseInt($(this).parent().index()) ;

            if (myCol == 5)
            {
                document.getElementById("goodaccessionnumberupdate").value = document.getElementById("goodaccessionlisttable").rows[myRow].cells[0].innerHTML;
                document.getElementById("importaccessionnumber").value = document.getElementById("goodaccessionlisttable").rows[myRow].cells[0].innerHTML;
            }

            if (myCol == 6) {

                document.getElementById("importaccessionnumber").value = document.getElementById("goodaccessionlisttable").rows[myRow].cells[0].innerHTML;

            }

        });
    });

 $(document).on('click', '#reattachdicomassessionnumber', function (e) {

        e.preventDefault();

        $.ajax({

            method: 'POST',
            url: 'DICOM/FixandUpdateLostDicomExam',

            data: $("#frm-lostdicom-update").serialize(),

            success: function (data) {

                if (data.Errored) {

                    if (data.Errored == "1") {

                        showNotificationMessage('Error', 'Message Cannot  be empty');
                    }
                    else{

                        showNotificationMessage('success', 'Changes saved');
                    }
                }

            },
            error: function (xhr, thrownError) {
                checkAjaxError(jqXHR);
            }
        });

 });

 $(document).on('click', '#importaccessionnumberupdate', function (e) {

     e.preventDefault();

     $.ajax({

         method: 'POST',
         url: 'DICOM/ImportMeasurements',

         data: $("#import-measurement-update").serialize(),

         success: function (data) {

             if (data.Errored) {

                 if (data.Errored == "1") {

                     showNotificationMessage('Error', 'Message Cannot  be empty');
                 }
                 else {

                     showNotificationMessage('success', 'Changes saved');
                 }
             }

         },
         error:function (xhr, thrownError) {
             checkAjaxError(jqXHR);
         }
     });

 });

</script>

<style>
    .btn-dicom {
        padding: 2px 20px 2px 20px; 
        font-weight: bold; 
        background-color: #ddd;
    }
</style>

@using (Html.BeginForm("FixLostDicomExam", "DICOM", new { area = "" }, FormMethod.Post, true, new { @class = "form-inline", @id = "frm-triage-search" }))
{

    <div class="panel panel-default ">
        <div class="panel-heading"><h4> Step 1 : Find DICOM Exam to move</h4></div>
        <div class="panel-body">

            <div>
                @(await Html.PartialAsync("_BadAccessionSearch", (object)(object)Model.vmaccessionsearch))
            </div>

            <div>
                @(await Html.PartialAsync("_BadAccessionList", (object)(object)Model.badaccessionlist))
            </div>

        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 2 : Find Appointment / Test To Move to</h4></div>
        <div class="panel-body">

            <div>
                @(await Html.PartialAsync("_GoodAccessionSearch", (object)(object)Model.vmaccessionsearch))
            </div>
            <div>
                @(await Html.PartialAsync("_GoodAccessionList", (object)(object)Model.goodaccessionlist))
            </div>

        </div>
    </div>
          
}


@using (Html.BeginForm("FixandUpdateLostDicomExam", "DICOM", new { area = "" }, FormMethod.Post, true, new { @class = "form-inline", @id = "frm-lostdicom-update" }))
{

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 3: Attach/Reattach Exam to correct test </h4></div>
        <div class="panel-body">
            <div>
                Study Id: <input id="badacceidupdate" type="text" name="badacceidupdate" readonly>
                Removed Accession: <input id="badaccessionnumberupdate" type="text" name="badaccessionnumberupdate" readonly>
                Add to  Accession: <input id="goodaccessionnumberupdate" type="text" name="goodaccessionnumberupdate" readonly>
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <a href="#" id="reattachdicomassessionnumber" class="btn btn-default btn-sm btn-dicom">Reattach</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

}

@using (Html.BeginForm("ImportMeasurements", "DICOM", new { area = "" }, FormMethod.Post, true, new { @class = "form-inline", @id = "import-measurement-update" }))
{

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 4: Import Measurement </h4></div>
        <div class="panel-body">
            <div>
                Accession Number: <input id="importaccessionnumber" type="text" name="importaccessionnumber" >
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <a href="#" id="importaccessionnumberupdate"  style="margin-left: 20px;" class="btn btn-default btn-sm btn-dicom">Import</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

}


















