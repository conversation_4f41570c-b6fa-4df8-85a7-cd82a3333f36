@model Cerebrum30.Models.VM_Demogr_Address 
<style>
    #dem_addr_List td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        /*font-weight: 700;*/
    }

        #dem_addr_List td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #dem_address_id input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("dem_address", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "dem_address_id" }))
{
    @Html.ModalHeader("Add Address")
    @Html.HiddenFor(x  => (object)x.dem_type)
    @Html.HiddenFor(x  => (object)x.dem_id)
    @Html.HiddenFor(x  => (object)x.dem_demId)
    <div class="ajax-loader-a" style="position: fixed; left: 15%; top: 25%; display: none;z-index:1600;">
        <img src="@Url.Content("~/Areas/Medications/Content/images/ajax-loader.gif")" />
    </div>
    <div class="container form-group form-group-sm" id="addres_dialog_edit" title="Add Doctor's Address" @*style="width:560px;"*@>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_addressLine1)
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.dem_addressLine1), new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_city)
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.dem_city), new { htmlAttributes = new { @class = "form-control", tabindex = 2 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_postalCode)
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.dem_postalCode), new { htmlAttributes = new { @class = "form-control", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_province)
            </div>
            <div class="col-md-10">
                @Html.EnumDropDownListFor(model => model.dem_province, htmlAttributes: new { @class = "form-control", tabindex = 4 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_country)
            </div>
            <div class="col-md-10">
                @Html.EnumDropDownListFor(model => model.dem_country, htmlAttributes: new { @class = "form-control", tabindex = 5 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dem_addr_types)
            </div>
            <div class="col-md-10">
                @*@Html.EnumDropDownListFor(model => model.dem_addr_types, htmlAttributes: new { @class = "form-control", tabindex = 6 })*@
                @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.dem_addr_typesId), new SelectList(ViewBag.LKAddressTypes, "Value", "Text", Model.dem_addr_typesId), new { @class = "form-control", tabindex = 6 })
            </div>
        </div>
        
        <div class="row marginTop2">
            <div class="col-md-2">

            </div>
            <div class="col-md-10 green">
                <span id="dem_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.dem_message), @Model.dem_message ?? "", new {})</span>
            </div>
            
            @*<div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="dem_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.dem_message), @Model.dem_message ?? "", new {})</span>

                </div>
            </div>*@
        </div>

        <div class="row" style="border-bottom: 1px solid #fff; margin-bottom:9px">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button class="btn btn-default marginTop2 marginBott2" type="button" id="newDemAddr">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_addr_submit">Save</button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                @*<div class="col-md-1">

                </div>*@
                <div class="col-md-12 pull-left">
                    <table class="table" id="dem_addr_List" name="dem_addr_List" @*style="font-size: 12px; margin-bottom: 30px;"*@>
                        @if (Model != null && Model.dem_List != null && Model.dem_List.Count > 0)
                {
                            <thead>
                                <tr class="vertical-center" style="background-color:#ddd;">
                                    <th width="40%">Address</th>
                                    <th width="12%">PC</th>
                                    <th width="14%">City</th>
                                    <th width="10%">Province</th>
                                    <th width="12%">Country</th>
                                    <th width="12%">Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.dem_List.Count; i++)
                                {
                                    <tr>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].name)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].name1)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].name4)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].name2)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].name3)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                        <td class="demAList">
                                            @Html.DisplayFor(model => (object)model.dem_List[i].value)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.dem_List[i].id), new { @id = Model.dem_List[i].id })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>
    <br /><br />
}

