@model Cerebrum30.Models.VM_Ext_Address
<style>
    #ext_addr_List td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        font-weight: 700;
    }

        #ext_addr_List td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #ext_addres_dialog_edit input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("ext_address", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "ext_address_id" }))
{
    @Html.ModalHeader("Add Address")
    @Html.HiddenFor(x  => (object)x.ex_type)
    @Html.HiddenFor(x  => (object)x.ex_id)
    @Html.HiddenFor(x  => (object)x.ex_docId)
    <div id="ext_addres_dialog_edit" title="Add Doctor's Address"  style="padding-left:30px;padding-right:30px;">
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_addressLine1)
            </div>
            <div class="col-md-9">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ext_addressLine1), new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_faxNumber)
            </div>
            <div class="col-md-4">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ext_faxNumber), new { htmlAttributes = new { @class = "form-control ex_addr_fx", tabindex = 2 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_city)
            </div>
            <div class="col-md-4">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ext_city), new { htmlAttributes = new { @class = "form-control", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_postalCode)
            </div>
            <div class="col-md-3">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.ext_postalCode), new { htmlAttributes = new { @class = "form-control", tabindex = 4 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_province)
            </div>
            <div class="col-md-3">
                @Html.EnumDropDownListFor(model => model.ext_province, htmlAttributes: new { @class = "form-control", tabindex = 5 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_country)
            </div>
            <div class="col-md-3">
                @Html.EnumDropDownListFor(model => model.ext_country, htmlAttributes: new { @class = "form-control", tabindex = 6 })
            </div>
        </div>
        @*<div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_add_types)
            </div>
            <div class="col-md-3">
                @Html.EnumDropDownListFor(model => model.ext_add_types, htmlAttributes: new { @class = "form-control", tabindex = 7 })
            </div>
        </div>*@

        <div class="row">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="ex_add_msgId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.ex_message), @Model.ex_message ?? "", new {})</span>

                </div>
            </div>
        </div>
        <div class="row" style="border-bottom:1px solid #e2dada;">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button class="btn btn-default marginTop2 marginBott2" type="button" id="newExAddr">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="ex_addr_submit">Save</button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-1">

                </div>
                <div class="col-md-11 pull-left">
                    <table class="table" id="ext_addr_List" name="ext_addr_List" style="font-size: 12px;">
                        @if (Model != null && Model.ex_List != null && Model.ex_List.Count > 0)
                {
                            <tbody>
                                @for (int i = 0; i < Model.ex_List.Count; i++)
                                {
                                    <tr>
                                        <td class="exAList">
                                            @Html.DisplayFor(model => (object)model.ex_List[i].name)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.ex_List[i].id), new { @id = Model.ex_List[i].id })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>


}

