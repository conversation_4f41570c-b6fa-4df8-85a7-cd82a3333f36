
@model Cerebrum30.Models.VM_Demogr_Cohort
<style>
    #addCohort input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("Dem_cohort", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "dem_cohort_id" }))
{
    @Html.ModalHeader("Add Cohort")

    @Html.HiddenFor(x  => (object)x.pat_cohort_Id)
    @Html.HiddenFor(x  => (object)x.coh_patientRecordId)
    <div id="addCohort" class="modal-body container form-group form-group-sm" @*style="padding-left:40px;padding-right:40px;width:950px;"*@>
        <div class="row marginTop2" style="margin-top: 10px;">
            <div class="col-md-2">
                <span>Patient:</span>
            </div>
            <div class="col-md-10 marg_bott height25">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.coh_name), new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Cohort:</span>
            </div>
            <div class="col-md-10">
                @Html.DropDownListFor((System.Func<dynamic, object>)(model => model.SelectedCohort_1_Id), Model.cohortsList_1, "", new { @class = "form-control selec_", tabindex = 2 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2 ">
                <span>Started:</span>
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.dateStarted), new { htmlAttributes = new { @class = "form-control coh_d", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Terminated:</span>
            </div>
            <div class="col-md-10">
                @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.dateTerminated), new { htmlAttributes = new { @class = "form-control coh_d", tabindex = 4 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Doctor:</span>
            </div>
            <div class="col-md-10">
                @Html.DropDownListFor((System.Func<dynamic, object>)(model => Model.SelectedDoctor_1_Id), Model.doctorsList_1, "", new { @class = "form-control selec_", tabindex = 5 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Notes:</span>
            </div>
            <div class="col-md-10">
                @Html.TextAreaFor(model => model.Notes, new { @class = "form-control", tabindex = 6, @rows = 3 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                
            </div>
            <div class="col-md-10 green">
                <span id="coh_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.message), @Model.message ?? "", new {})</span>
            </div>
            
            @*<div class="col-md-12 text-center green">
                <span id="coh_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.message), @Model.message ?? "", new {})</span>
            </div>*@
        </div>
        <div class="row" @*style="border-bottom:1px solid #e2dada;"*@>
                <div class="col-md-2 text-left">
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_cohort_submit">Save</button>
                </div>
        </div>
    </div>


}

