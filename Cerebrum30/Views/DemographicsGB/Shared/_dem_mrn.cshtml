@model Cerebrum30.Models.VM_MRN
<style>
    #MRN_List td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        font-weight: 700;
    }

        #MRN_List td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #mrn_dialog input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }

    .t_a {
        height: 50px !important;
    }
</style>
@using (Html.BeginForm("dem_mrn", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "dem_mrn_id" }))
{
    @Html.ModalHeader("MRN")
    @Html.HiddenFor(x  => (object)x.mrn_type)
    @Html.HiddenFor(x  => (object)x.mrn_hospitalName)
    @Html.HiddenFor(x  => (object)x.mrn_patientId)
    @Html.HiddenFor(x  => (object)x.mrn_hospitalId)
    @Html.HiddenFor(x  => (object)x.mrn_hospitalCode)
    <div id="mrn_dialog" title="" style="padding-left:40px;padding-right:40px;">
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    @Html.LabelFor((System.Func<dynamic, object>)(model => model.mrn_hospitalName), @Model.mrn_hospitalName ?? "", new {})
                </div>
                <div class="col-md-4">
                    @Html.EditorFor((System.Func<dynamic, object>)(model => (object)model.mrn_code), new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="ref_d_messageId">@Html.LabelFor((System.Func<dynamic, object>)(x => x.message_mrn), @Model.message_mrn ?? "", new {})</span>

                </div>
            </div>
        </div>
        <div class="row marginTop2" style="border-bottom:1px solid #e2dada;">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    @*<button class="btn btn-default marginTop2 marginBott2" type="button" id="newContact_p">New</button>*@
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="mrn_sub">Save</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-1">

                </div>
                <div class="col-md-11 pull-left">
                    <table class="table" id="MRN_List" name="MRN_List" style="font-size: 12px;">
                        @if (Model != null && Model.mrn_List != null && Model.mrn_List.Count > 0)
                {
                            <tbody>
                                @for (int i = 0; i < Model.mrn_List.Count; i++)
                                {
                                    <tr>
                                        <td class="mrn_List">
                                            @Html.DisplayFor(model => (object)model.mrn_List[i].name)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.mrn_List[i].id), new { @id = Model.mrn_List[i].id, data_codeHosp = Model.mrn_List[i].name })
                                        </td>
                                        <td class="mrn_List">
                                            @Html.DisplayFor(model => (object)model.mrn_List[i].value)
                                            @Html.HiddenFor((System.Func<dynamic, object>)(model => (object)model.mrn_List[i].id), new { @id = Model.mrn_List[i].id, data_codeHosp = Model.mrn_List[i].name })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>


}
