
@*@model IEnumerable<Cerebrum3DB.DBClasses.ExternalDoctor>
<p>
    @Html.Editor("Search", new { htmlAttributes = new { @class = "form-control", @placeholder = "Search doctor here" } })
</p>
<table class="table">
    <tr>

        <th>
            @Html.DisplayNameFor(model => model.CPSO)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.FullName)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.Hidden("Id", item.Id)
                @item.CPSO
            </td>
            <td>
                @item.FullName
            </td>
            <td>
                <a href="#">Select</a>
            </td>
        </tr>
    }

</table>*@
