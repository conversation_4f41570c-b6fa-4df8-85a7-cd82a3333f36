# Round 3 PowerShell script to fix remaining CS1973 Dynamic Dispatch errors
# .NET 8 upgrade for Cerebrum3 - Targeting specific missed patterns

Write-Host "Starting Round 3 CS1973 Dynamic Dispatch Error Fixes..." -ForegroundColor Green

# Get all CSHTML files
$files = Get-ChildItem -Path "." -Recurse -Filter "*.cshtml" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

Write-Host "Found $($files.Count) CSHTML files to process" -ForegroundColor Yellow

$totalChanges = 0
$processedFiles = 0

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        $originalContent = $content
        $fileChanges = 0
        
        # Pattern 1: Fix standalone PartialAsync calls without await
        # Html.PartialAsync("view", model) -> Html.PartialAsync("view", (object)model)
        $pattern1Before = $content
        $content = $content -replace 'Html\.PartialAsync\("([^"]+)",\s*([^)]+)\)', 'Html.PartialAsync("$1", (object)$2)'
        if ($content -ne $pattern1Before) { $fileChanges++ }
        
        # Pattern 2: Fix ModalHeader calls that were missed
        # @Html.ModalHeader("text") -> @Html.ModalHeader((object)"text")
        $pattern2Before = $content
        $content = $content -replace '@Html\.ModalHeader\(\s*"([^"]+)"\s*\)', '@Html.ModalHeader((object)"$1")'
        if ($content -ne $pattern2Before) { $fileChanges++ }
        
        # Pattern 3: Fix complex TextBox calls with model properties
        # @Html.TextBox("name", Model.Property.SubProperty) -> @Html.TextBox("name", (object)Model.Property.SubProperty)
        $pattern3Before = $content
        $content = $content -replace '@Html\.TextBox\("([^"]+)",\s*(Model\.[A-Za-z0-9_.]+)\s*,', '@Html.TextBox("$1", (object)$2,'
        if ($content -ne $pattern3Before) { $fileChanges++ }
        
        # Pattern 4: Fix Hidden calls with item properties
        # @Html.Hidden("name", item.property) -> @Html.Hidden("name", (object)item.property)
        $pattern4Before = $content
        $content = $content -replace '@Html\.Hidden\("([^"]+)",\s*(item\.[a-zA-Z0-9_]+)\s*\)', '@Html.Hidden("$1", (object)$2)'
        if ($content -ne $pattern4Before) { $fileChanges++ }
        
        # Pattern 5: Fix CheckBox calls without html attributes
        # @Html.CheckBox("name", someValue) -> @Html.CheckBox("name", (object)someValue)
        $pattern5Before = $content
        $content = $content -replace '@Html\.CheckBox\("([^"]+)",\s*([^,\)]+)\s*\)', '@Html.CheckBox("$1", (object)$2)'
        if ($content -ne $pattern5Before) { $fileChanges++ }
        
        # Pattern 6: Fix double object casting that may have been introduced
        # ((object)(object)x) -> ((object)x)
        $pattern6Before = $content
        $content = $content -replace '\(\(object\)\(object\)([^)]+)\)', '((object)$1)'
        if ($content -ne $pattern6Before) { $fileChanges++ }
        
        # Save the file if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
            Write-Host "  Fixed $fileChanges patterns in: $($file.FullName)" -ForegroundColor Cyan
            $totalChanges += $fileChanges
        }
        
        $processedFiles++
        
        # Progress indicator
        if ($processedFiles % 100 -eq 0) {
            Write-Host "Processed $processedFiles files..." -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "Error processing file $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nRound 3 CS1973 Fix Summary:" -ForegroundColor Green
Write-Host "  Files processed: $processedFiles" -ForegroundColor Yellow
Write-Host "  Total changes applied: $totalChanges" -ForegroundColor Yellow
Write-Host "  Round 3 fix completed successfully!" -ForegroundColor Green