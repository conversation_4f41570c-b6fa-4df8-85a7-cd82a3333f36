# Round 3 PowerShell script to fix remaining CS1973 Dynamic Dispatch errors
# .NET 8 upgrade for Cerebrum3 - Targeting specific missed patterns

Write-Host "Starting Round 3 CS1973 Dynamic Dispatch Error Fixes..." -ForegroundColor Green

# Get all CSHTML files
$files = Get-ChildItem -Path "." -Recurse -Filter "*.cshtml" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

Write-Host "Found $($files.Count) CSHTML files to process" -ForegroundColor Yellow

$totalChanges = 0
$processedFiles = 0

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        $originalContent = $content
        $fileChanges = 0
        
        # Pattern 1: Fix PartialAsync calls with collection models
        # Html.PartialAsync("view", collectionModel) -> Html.PartialAsync("view", (object)collectionModel)
        $pattern1Before = $content
        $content = $content -replace '(@\()?await Html\.PartialAsync\("([^"]+)",\s*([^)]+)\)', {
            param($match)
            $prefix = if ($match.Groups[1].Value) { "@(await " } else { "await " }
            $viewName = $match.Groups[2].Value
            $model = $match.Groups[3].Value.Trim()
            $suffix = if ($match.Groups[1].Value) { ")" } else { ""
            if ($model -notmatch '^\(object\)') {
                return "${prefix}Html.PartialAsync(`"$viewName`", (object)$model)$suffix"
            }
            return $match.Value
        }
        if ($content -ne $pattern1Before) { $fileChanges++ }
        
        # Pattern 2: Fix standalone PartialAsync calls without await
        # Html.PartialAsync("view", model) -> Html.PartialAsync("view", (object)model)
        $pattern2Before = $content
        $content = $content -replace 'Html\.PartialAsync\("([^"]+)",\s*([^)]+)\)(?!\s*;)', 'Html.PartialAsync("$1", (object)$2)'
        if ($content -ne $pattern2Before) { $fileChanges++ }
        
        # Pattern 3: Fix ModalHeader calls that were missed
        # @Html.ModalHeader("text") -> @Html.ModalHeader((object)"text")
        $pattern3Before = $content
        $content = $content -replace '@Html\.ModalHeader\(\s*"([^"]+)"\s*\)', '@Html.ModalHeader((object)"$1")'
        if ($content -ne $pattern3Before) { $fileChanges++ }
        
        # Pattern 4: Fix complex TextBox calls with model properties
        # @Html.TextBox("name", Model.Property.SubProperty) -> @Html.TextBox("name", (object)Model.Property.SubProperty)
        $pattern4Before = $content
        $content = $content -replace '@Html\.TextBox\("([^"]+)",\s*(Model\.[A-Za-z0-9_.]+)\s*,', '@Html.TextBox("$1", (object)$2,'
        if ($content -ne $pattern4Before) { $fileChanges++ }
        
        # Pattern 5: Fix TextBox calls without htmlAttributes (2 parameter version)
        # @Html.TextBox("name", someValue) -> @Html.TextBox("name", (object)someValue)
        $pattern5Before = $content
        $content = $content -replace '@Html\.TextBox\("([^"]+)",\s*([^,)]+)\s*\)(?!\s*,)', '@Html.TextBox("$1", (object)$2)'
        if ($content -ne $pattern5Before) { $fileChanges++ }
        
        # Pattern 6: Fix Hidden calls with complex expressions
        # @Html.Hidden("name", item.property) -> @Html.Hidden("name", (object)item.property)
        $pattern6Before = $content
        $content = $content -replace '@Html\.Hidden\("([^"]+)",\s*(item\.[a-zA-Z0-9_]+)\s*\)', '@Html.Hidden("$1", (object)$2)'
        if ($content -ne $pattern6Before) { $fileChanges++ }
        
        # Pattern 7: Fix CheckBox calls with dynamic values
        # @Html.CheckBox("name", someValue) -> @Html.CheckBox("name", (object)someValue)
        $pattern7Before = $content
        $content = $content -replace '@Html\.CheckBox\("([^"]+)",\s*([^,\)]+)\s*\)(?!\s*,)', '@Html.CheckBox("$1", (object)$2)'
        if ($content -ne $pattern7Before) { $fileChanges++ }
        
        # Pattern 8: Fix DropDownList calls in specific contexts
        # @Html.DropDownList("name", collection, new {...}) -> @Html.DropDownList("name", (object)collection, new {...})
        $pattern8Before = $content
        $content = $content -replace '@Html\.DropDownList\("([^"]+)",\s*([^,]+),\s*(new\s*\{[^}]*\})\)', '@Html.DropDownList("$1", (object)$2, $3)'
        if ($content -ne $pattern8Before) { $fileChanges++ }
        
        # Pattern 9: Fix RadioButton calls
        # @Html.RadioButton("name", value) -> @Html.RadioButton("name", (object)value)
        $pattern9Before = $content
        $content = $content -replace '@Html\.RadioButton\("([^"]+)",\s*([^,)]+)\s*\)(?!\s*,)', '@Html.RadioButton("$1", (object)$2)'
        if ($content -ne $pattern9Before) { $fileChanges++ }
        
        # Pattern 10: Fix Password calls
        # @Html.Password("name", value) -> @Html.Password("name", (object)value)
        $pattern10Before = $content
        $content = $content -replace '@Html\.Password\("([^"]+)",\s*([^,)]+)\s*\)(?!\s*,)', '@Html.Password("$1", (object)$2)'
        if ($content -ne $pattern10Before) { $fileChanges++ }
        
        # Pattern 11: Fix ListBox calls
        # @Html.ListBox("name", selectList) -> @Html.ListBox("name", (object)selectList)
        $pattern11Before = $content
        $content = $content -replace '@Html\.ListBox\("([^"]+)",\s*([^,)]+)\s*\)(?!\s*,)', '@Html.ListBox("$1", (object)$2)'
        if ($content -ne $pattern11Before) { $fileChanges++ }
        
        # Pattern 12: Fix ActionLink calls with dynamic parameters
        # @Html.ActionLink(linkText, "action", routeValues) -> @Html.ActionLink((object)linkText, "action", (object)routeValues)
        $pattern12Before = $content
        $content = $content -replace '@Html\.ActionLink\(([^,]+),\s*"([^"]+)",\s*([^,)]+)\)', '@Html.ActionLink((object)$1, "$2", (object)$3)'
        if ($content -ne $pattern12Before) { $fileChanges++ }
        
        # Pattern 13: Fix double object casting that may have been introduced
        # ((object)(object)x) -> ((object)x)
        $pattern13Before = $content
        $content = $content -replace '\(\(object\)\(object\)([^)]+)\)', '((object)$1)'
        if ($content -ne $pattern13Before) { $fileChanges++ }
        
        # Pattern 14: Remove object casting from string literals that don't need it
        # (object)"literal" -> "literal" for specific safe contexts
        $pattern14Before = $content
        $content = $content -replace '\(object\)("([^"]+)")\s*\)', '$1'
        if ($content -ne $pattern14Before) { $fileChanges++ }
        
        # Save the file if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
            Write-Host "  Fixed $fileChanges patterns in: $($file.FullName)" -ForegroundColor Cyan
            $totalChanges += $fileChanges
        }
        
        $processedFiles++
        
        # Progress indicator
        if ($processedFiles % 100 -eq 0) {
            Write-Host "Processed $processedFiles files..." -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "Error processing file $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nRound 3 CS1973 Fix Summary:" -ForegroundColor Green
Write-Host "  Files processed: $processedFiles" -ForegroundColor Yellow
Write-Host "  Total changes applied: $totalChanges" -ForegroundColor Yellow
Write-Host "  Round 3 fix completed successfully!" -ForegroundColor Green