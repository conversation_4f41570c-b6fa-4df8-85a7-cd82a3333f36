# Comprehensive CS1973 Fix Script
# This script fixes all CS1973 errors by casting dynamic arguments to object type
# for HTML helper extension methods

param(
    [string]$Path = "Cerebrum30",
    [switch]$WhatIf = $false
)

$ErrorActionPreference = "Stop"

# Get all .cshtml files
$files = Get-ChildItem -Path $Path -Recurse -Filter "*.cshtml" | Where-Object { $_.Name -notlike "*~*" }

$totalFiles = $files.Count
$modifiedFiles = 0
$totalFixes = 0

Write-Host "🔍 Found $totalFiles .cshtml files to process" -ForegroundColor Cyan
Write-Host ""

foreach ($file in $files) {
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileFixes = 0
    
    if ([string]::IsNullOrWhiteSpace($content)) {
        continue
    }
    
    Write-Host "📄 Processing: $relativePath" -ForegroundColor Yellow
    
    # Pattern 1: Html.Hidden with 2 parameters - Html.Hidden("name", value) -> Html.Hidden("name", (object)value)
    $pattern1 = '@Html\.Hidden\("([^"]+)",\s*([^,)]+)\s*\)'
    $replacement1 = '@Html.Hidden("$1", (object)$2)'
    if ($content -match $pattern1) {
        $matches = [regex]::Matches($content, $pattern1)
        $content = [regex]::Replace($content, $pattern1, $replacement1)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Hidden(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 2: Html.Hidden with 3 parameters - Html.Hidden("name", value, htmlAttributes) -> Html.Hidden("name", (object)value, htmlAttributes)
    $pattern2 = '@Html\.Hidden\("([^"]+)",\s*([^,]+),\s*([^)]+)\)'
    $replacement2 = '@Html.Hidden("$1", (object)$2, $3)'
    if ($content -match $pattern2) {
        $matches = [regex]::Matches($content, $pattern2)
        $content = [regex]::Replace($content, $pattern2, $replacement2)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Hidden(3 params) calls" -ForegroundColor Green
    }
    
    # Pattern 3: Html.TextBox with 2 parameters - Html.TextBox("name", value) -> Html.TextBox("name", (object)value)
    $pattern3 = '@Html\.TextBox\("([^"]+)",\s*([^,)]+)\s*\)'
    $replacement3 = '@Html.TextBox("$1", (object)$2)'
    if ($content -match $pattern3) {
        $matches = [regex]::Matches($content, $pattern3)
        $content = [regex]::Replace($content, $pattern3, $replacement3)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextBox(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 4: Html.TextBox with 3 parameters - Html.TextBox("name", value, htmlAttributes) -> Html.TextBox("name", (object)value, htmlAttributes)
    $pattern4 = '@Html\.TextBox\("([^"]+)",\s*([^,]+),\s*([^)]+)\)'
    $replacement4 = '@Html.TextBox("$1", (object)$2, $3)'
    if ($content -match $pattern4) {
        $matches = [regex]::Matches($content, $pattern4)
        $content = [regex]::Replace($content, $pattern4, $replacement4)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextBox(3 params) calls" -ForegroundColor Green
    }
    
    # Pattern 5: Html.CheckBox with 2 parameters - Html.CheckBox("name", value) -> Html.CheckBox("name", (object)value)
    $pattern5 = '@Html\.CheckBox\("([^"]+)",\s*([^,)]+)\s*\)'
    $replacement5 = '@Html.CheckBox("$1", (object)$2)'
    if ($content -match $pattern5) {
        $matches = [regex]::Matches($content, $pattern5)
        $content = [regex]::Replace($content, $pattern5, $replacement5)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.CheckBox(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 6: Html.CheckBox with 3 parameters - Html.CheckBox("name", value, htmlAttributes) -> Html.CheckBox("name", (object)value, htmlAttributes)
    $pattern6 = '@Html\.CheckBox\("([^"]+)",\s*([^,]+),\s*([^)]+)\)'
    $replacement6 = '@Html.CheckBox("$1", (object)$2, $3)'
    if ($content -match $pattern6) {
        $matches = [regex]::Matches($content, $pattern6)
        $content = [regex]::Replace($content, $pattern6, $replacement6)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.CheckBox(3 params) calls" -ForegroundColor Green
    }
    
    # Pattern 7: Html.DropDownList with 2 parameters - Html.DropDownList("name", selectList) -> Html.DropDownList("name", (object)selectList)
    $pattern7 = '@Html\.DropDownList\("([^"]+)",\s*([^,)]+)\s*\)'
    $replacement7 = '@Html.DropDownList("$1", (object)$2)'
    if ($content -match $pattern7) {
        $matches = [regex]::Matches($content, $pattern7)
        $content = [regex]::Replace($content, $pattern7, $replacement7)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.DropDownList(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 8: Html.DropDownList with 3 parameters - Html.DropDownList("name", selectList, htmlAttributes) -> Html.DropDownList("name", (object)selectList, htmlAttributes)
    $pattern8 = '@Html\.DropDownList\("([^"]+)",\s*([^,]+),\s*([^)]+)\)'
    $replacement8 = '@Html.DropDownList("$1", (object)$2, $3)'
    if ($content -match $pattern8) {
        $matches = [regex]::Matches($content, $pattern8)
        $content = [regex]::Replace($content, $pattern8, $replacement8)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.DropDownList(3 params) calls" -ForegroundColor Green
    }
    
    # Pattern 9: Html.ModalHeader with 1 parameter - Html.ModalHeader("title") -> Html.ModalHeader((object)"title")
    $pattern9 = '@Html\.ModalHeader\(\s*"([^"]+)"\s*\)'
    $replacement9 = '@Html.ModalHeader((object)"$1")'
    if ($content -match $pattern9) {
        $matches = [regex]::Matches($content, $pattern9)
        $content = [regex]::Replace($content, $pattern9, $replacement9)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.ModalHeader calls" -ForegroundColor Green
    }
    
    # Pattern 10: Html.ModalHeader with 2 parameters - Html.ModalHeader("title", "info") -> Html.ModalHeader((object)"title", (object)"info")
    $pattern10 = '@Html\.ModalHeader\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)'
    $replacement10 = '@Html.ModalHeader((object)"$1", (object)"$2")'
    if ($content -match $pattern10) {
        $matches = [regex]::Matches($content, $pattern10)
        $content = [regex]::Replace($content, $pattern10, $replacement10)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.ModalHeader(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 11: Html.TextArea with 2 parameters - Html.TextArea("name", value) -> Html.TextArea("name", (object)value)
    $pattern11 = '@Html\.TextArea\("([^"]+)",\s*([^,)]+)\s*\)'
    $replacement11 = '@Html.TextArea("$1", (object)$2)'
    if ($content -match $pattern11) {
        $matches = [regex]::Matches($content, $pattern11)
        $content = [regex]::Replace($content, $pattern11, $replacement11)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextArea(2 params) calls" -ForegroundColor Green
    }
    
    # Pattern 12: Html.TextArea with 3 parameters - Html.TextArea("name", value, htmlAttributes) -> Html.TextArea("name", (object)value, htmlAttributes)
    $pattern12 = '@Html\.TextArea\("([^"]+)",\s*([^,]+),\s*([^)]+)\)'
    $replacement12 = '@Html.TextArea("$1", (object)$2, $3)'
    if ($content -match $pattern12) {
        $matches = [regex]::Matches($content, $pattern12)
        $content = [regex]::Replace($content, $pattern12, $replacement12)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextArea(3 params) calls" -ForegroundColor Green
    }
    
    # Pattern 13: Html.ActionLink with multiple parameters - Html.ActionLink(linkText, actionName, ...) -> Html.ActionLink((object)linkText, (object)actionName, ...)
    $pattern13 = '@Html\.ActionLink\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)'
    $replacement13 = '@Html.ActionLink((object)$1, (object)$2, (object)$3, (object)$4, $5)'
    if ($content -match $pattern13) {
        $matches = [regex]::Matches($content, $pattern13)
        $content = [regex]::Replace($content, $pattern13, $replacement13)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.ActionLink calls" -ForegroundColor Green
    }
    
    # Pattern 14: Html.Label with 2 parameters - Html.Label("name", "text") -> Html.Label((object)"name", (object)"text")
    $pattern14 = '@Html\.Label\("([^"]+)",\s*"([^"]+)"\)'
    $replacement14 = '@Html.Label((object)"$1", (object)"$2")'
    if ($content -match $pattern14) {
        $matches = [regex]::Matches($content, $pattern14)
        $content = [regex]::Replace($content, $pattern14, $replacement14)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Label calls" -ForegroundColor Green
    }
    
    # Pattern 15: Html.RenderPartialAsync - Html.RenderPartialAsync("partialName", model) -> Html.RenderPartialAsync((object)"partialName", (object)model)
    $pattern15 = '@Html\.RenderPartialAsync\("([^"]+)",\s*([^)]+)\)'
    $replacement15 = '@Html.RenderPartialAsync((object)"$1", (object)$2)'
    if ($content -match $pattern15) {
        $matches = [regex]::Matches($content, $pattern15)
        $content = [regex]::Replace($content, $pattern15, $replacement15)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.RenderPartialAsync calls" -ForegroundColor Green
    }
    
    # Pattern 16: Html.Truncate - Html.Truncate(text, length) -> Html.Truncate((object)text, (object)length)
    $pattern16 = '@Html\.Truncate\(([^,]+),\s*([^)]+)\)'
    $replacement16 = '@Html.Truncate((object)$1, (object)$2)'
    if ($content -match $pattern16) {
        $matches = [regex]::Matches($content, $pattern16)
        $content = [regex]::Replace($content, $pattern16, $replacement16)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Truncate calls" -ForegroundColor Green
    }

    # Pattern 17: Html.ModalHeader with string concatenation - Html.ModalHeader("text" + variable) -> Html.ModalHeader((object)("text" + variable))
    $pattern17 = '@Html\.ModalHeader\(([^)]+)\)'
    $replacement17 = '@Html.ModalHeader((object)($1))'
    if ($content -match $pattern17 -and $content -notmatch '@Html\.ModalHeader\(\(object\)') {
        $matches = [regex]::Matches($content, $pattern17)
        $content = [regex]::Replace($content, $pattern17, $replacement17)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.ModalHeader(complex) calls" -ForegroundColor Green
    }

    # Pattern 18: Html.Hidden with complex expressions - Html.Hidden("name", complexExpression) -> Html.Hidden("name", (object)complexExpression)
    $pattern18 = '@Html\.Hidden\("([^"]+)",\s*([^,)]+)\)'
    $replacement18 = '@Html.Hidden("$1", (object)$2)'
    if ($content -match $pattern18 -and $content -notmatch '@Html\.Hidden\("[^"]+",\s*\(object\)') {
        $matches = [regex]::Matches($content, $pattern18)
        $content = [regex]::Replace($content, $pattern18, $replacement18)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Hidden(complex) calls" -ForegroundColor Green
    }

    # Pattern 19: Html.CheckBox with complex expressions - Html.CheckBox("name", complexExpression) -> Html.CheckBox("name", (object)complexExpression)
    $pattern19 = '@Html\.CheckBox\("([^"]+)",\s*([^,)]+)\)'
    $replacement19 = '@Html.CheckBox("$1", (object)$2)'
    if ($content -match $pattern19 -and $content -notmatch '@Html\.CheckBox\("[^"]+",\s*\(object\)') {
        $matches = [regex]::Matches($content, $pattern19)
        $content = [regex]::Replace($content, $pattern19, $replacement19)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.CheckBox(complex) calls" -ForegroundColor Green
    }

    # Pattern 20: Html.TextBox with complex expressions - Html.TextBox("name", complexExpression) -> Html.TextBox("name", (object)complexExpression)
    $pattern20 = '@Html\.TextBox\("([^"]+)",\s*([^,)]+)\)'
    $replacement20 = '@Html.TextBox("$1", (object)$2)'
    if ($content -match $pattern20 -and $content -notmatch '@Html\.TextBox\("[^"]+",\s*\(object\)') {
        $matches = [regex]::Matches($content, $pattern20)
        $content = [regex]::Replace($content, $pattern20, $replacement20)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextBox(complex) calls" -ForegroundColor Green
    }

    # Pattern 21: Html.Label with complex expressions - Html.Label("name", complexExpression) -> Html.Label("name", (object)complexExpression)
    $pattern21 = '@Html\.Label\("([^"]+)",\s*([^,)]+)\)'
    $replacement21 = '@Html.Label("$1", (object)$2)'
    if ($content -match $pattern21 -and $content -notmatch '@Html\.Label\("[^"]+",\s*\(object\)') {
        $matches = [regex]::Matches($content, $pattern21)
        $content = [regex]::Replace($content, $pattern21, $replacement21)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.Label(complex) calls" -ForegroundColor Green
    }

    # Pattern 22: Html.TextArea with complex expressions - Html.TextArea("name", complexExpression) -> Html.TextArea("name", (object)complexExpression)
    $pattern22 = '@Html\.TextArea\("([^"]+)",\s*([^,)]+)\)'
    $replacement22 = '@Html.TextArea("$1", (object)$2)'
    if ($content -match $pattern22 -and $content -notmatch '@Html\.TextArea\("[^"]+",\s*\(object\)') {
        $matches = [regex]::Matches($content, $pattern22)
        $content = [regex]::Replace($content, $pattern22, $replacement22)
        $fileFixes += $matches.Count
        Write-Host "  ✅ Fixed $($matches.Count) Html.TextArea(complex) calls" -ForegroundColor Green
    }

    # Only write if content changed and not in WhatIf mode
    if ($content -ne $originalContent) {
        if (-not $WhatIf) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        }
        $modifiedFiles++
        $totalFixes += $fileFixes
        Write-Host "  📝 Total fixes in file: $fileFixes" -ForegroundColor Cyan
    } else {
        Write-Host "  ⏭️  No changes needed" -ForegroundColor Gray
    }
    
    Write-Host ""
}

Write-Host "🎉 Summary:" -ForegroundColor Green
Write-Host "  📁 Files processed: $totalFiles" -ForegroundColor White
Write-Host "  ✏️  Files modified: $modifiedFiles" -ForegroundColor White
Write-Host "  🔧 Total fixes applied: $totalFixes" -ForegroundColor White

if ($WhatIf) {
    Write-Host "  ⚠️  WhatIf mode - no files were actually modified" -ForegroundColor Yellow
}
