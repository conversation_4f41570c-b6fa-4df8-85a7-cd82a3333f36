# Cleanup script to fix double casting and CS1503 errors from CS1973 fixes
# .NET 8 upgrade for Cerebrum3 - Cleanup phase

Write-Host "Starting CS1973 Cleanup (Double Casting & Type Mismatches)..." -ForegroundColor Green

# Get all CSHTML files
$files = Get-ChildItem -Path "." -Recurse -Filter "*.cshtml" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

Write-Host "Found $($files.Count) CSHTML files to process" -ForegroundColor Yellow

$totalChanges = 0
$processedFiles = 0

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        $originalContent = $content
        $fileChanges = 0
        
        # Pattern 1: Fix triple casting - ((object)(object)(object)x) -> ((object)x)
        $pattern1Before = $content
        $content = $content -replace '\(\(object\)\(object\)\(object\)([^)]+)\)', '((object)$1)'
        if ($content -ne $pattern1Before) { $fileChanges++ }
        
        # Pattern 2: Fix double casting - ((object)(object)x) -> ((object)x)
        $pattern2Before = $content
        $content = $content -replace '\(\(object\)\(object\)([^)]+)\)', '((object)$1)'
        if ($content -ne $pattern2Before) { $fileChanges++ }
        
        # Pattern 3: Fix SelectList constructor casting issues
        # new SelectList(...) already returns proper type, remove (object) wrapper
        $pattern3Before = $content
        $content = $content -replace '\(object\)(new SelectList\([^)]+\))', '$1'
        if ($content -ne $pattern3Before) { $fileChanges++ }
        
        # Pattern 4: Fix DropDownList with SelectList - remove object casting on SelectList
        $pattern4Before = $content
        $content = $content -replace '@Html\.DropDownList\("([^"]+)",\s*\(object\)(new SelectList\([^)]+\))\)', '@Html.DropDownList("$1", $2)'
        if ($content -ne $pattern4Before) { $fileChanges++ }
        
        # Pattern 5: Fix string literal casting - remove (object) from string literals
        $pattern5Before = $content
        $content = $content -replace '\(object\)("([^"]*)")', '$1'
        if ($content -ne $pattern5Before) { $fileChanges++ }
        
        # Pattern 6: Fix integer literal casting for specific contexts where not needed
        # For Html.GetPatientInfo integer parameters, keep only first argument cast
        $pattern6Before = $content
        $content = $content -replace '@Html\.GetPatientInfo\(\(object\)([^,]+),\s*\(object\)0,\s*\(object\)0,\s*\(object\)0\)', '@Html.GetPatientInfo((object)$1, 0, 0, 0)'
        if ($content -ne $pattern6Before) { $fileChanges++ }
        
        # Pattern 7: Fix RenderPartialAsync double object casting
        $pattern7Before = $content
        $content = $content -replace 'Html\.RenderPartialAsync\("([^"]+)",\s*\(object\)\(object\)([^)]+)\)', 'Html.RenderPartialAsync("$1", (object)$2)'
        if ($content -ne $pattern7Before) { $fileChanges++ }
        
        # Pattern 8: Fix specific model property casting that causes CS1503
        # Remove object casting from Model properties that should be strongly typed
        $pattern8Before = $content
        $content = $content -replace '@Html\.TextBox\("([^"]+)",\s*\(object\)(Model\.[A-Z][a-zA-Z0-9_.]*),\s*new \{', '@Html.TextBox("$1", $2, new {'
        if ($content -ne $pattern8Before) { $fileChanges++ }
        
        # Pattern 9: Fix ViewBag casting issues
        $pattern9Before = $content
        $content = $content -replace '\(object\)(ViewBag\.[A-Z][a-zA-Z0-9_]*)', '$1'
        if ($content -ne $pattern9Before) { $fileChanges++ }
        
        # Pattern 10: Fix @item property casting in foreach loops
        $pattern10Before = $content
        $content = $content -replace '@Html\.Hidden\("([^"]+)",\s*\(object\)(item\.[a-zA-Z0-9_]+)\)', '@Html.Hidden("$1", $2)'
        if ($content -ne $pattern10Before) { $fileChanges++ }
        
        # Save the file if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
            Write-Host "  Cleaned $fileChanges casting issues in: $($file.FullName)" -ForegroundColor Cyan
            $totalChanges += $fileChanges
        }
        
        $processedFiles++
        
        # Progress indicator
        if ($processedFiles % 100 -eq 0) {
            Write-Host "Processed $processedFiles files..." -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "Error processing file $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nCS1973 Cleanup Summary:" -ForegroundColor Green
Write-Host "  Files processed: $processedFiles" -ForegroundColor Yellow
Write-Host "  Total cleanup changes applied: $totalChanges" -ForegroundColor Yellow
Write-Host "  Cleanup completed successfully!" -ForegroundColor Green